# 🛡️ Obu Marketplace - Complete Authentication Setup Guide

## 📋 **Overview**

This guide provides comprehensive instructions for setting up the complete authentication system in the Obu Marketplace, including Supabase integration, social login configuration, and test account creation.

## 🏗️ **System Architecture**

The Obu Marketplace uses a hybrid authentication system:
- **Primary**: Supabase Auth (email/password + social login)
- **Fallback**: localStorage-based authentication
- **Buyer-Only**: Restricted to buyer accounts only (no seller functionality)
- **Multi-Currency**: PayMongo integration for PHP and USD payments

## 🚀 **Quick Start**

1. **Clone and Setup Project**
2. **Configure Supabase Authentication**
3. **Set up Social Login Providers**
4. **Create Test Accounts**
5. **Test Authentication Flows**

---

## 📊 **Step 1: Supabase Authentication Setup**

### **1.1 Create Supabase Project**

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click **"New Project"**
3. Choose organization and enter project details:
   - **Name**: `obu-marketplace`
   - **Database Password**: Create a strong password
   - **Region**: Choose closest to your users
4. Wait for project creation (2-3 minutes)

### **1.2 Configure Authentication Settings**

1. Navigate to **Authentication > Settings** in Supabase dashboard
2. Configure the following settings:

```yaml
Site URL: http://localhost:8000  # For development
Additional Redirect URLs:
  - http://localhost:8000/
  - https://yourdomain.com/  # For production

Enable email confirmations: ON
Enable phone confirmations: OFF
Enable custom SMTP: OFF (use Supabase default for testing)
```

### **1.3 Update Frontend Configuration**

1. Get your Supabase credentials from **Settings > API**:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Public Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

2. Update `js/supabase-integration.js`:

```javascript
constructor() {
    this.supabaseUrl = 'https://your-project-id.supabase.co';
    this.supabaseKey = 'your-anon-public-key-here';
    // ... rest of constructor
}
```

### **1.4 Run Database Schema**

Execute the following SQL scripts in **SQL Editor** (in order):

1. **`supabase-complete-schema.sql`** - Creates all tables and relationships
2. **`supabase-rls-policies.sql`** - Sets up Row Level Security
3. **`supabase-complete-sample-data.sql`** - Populates sample data

---

## 🔐 **Step 2: Social Authentication Setup**

### **2.1 Google OAuth Configuration**

#### **Google Cloud Console Setup:**

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create new project or select existing one
3. Enable **Google+ API** and **Google Identity API**
4. Navigate to **Credentials > Create Credentials > OAuth 2.0 Client IDs**
5. Configure OAuth consent screen:
   - **Application type**: Web application
   - **Name**: Obu Marketplace
   - **Authorized domains**: Add your domain

6. Create OAuth 2.0 Client ID:
   - **Application type**: Web application
   - **Name**: Obu Marketplace Auth
   - **Authorized redirect URIs**:
     ```
     https://your-project-id.supabase.co/auth/v1/callback
     ```

7. Copy **Client ID** and **Client Secret**

#### **Supabase Google Configuration:**

1. In Supabase dashboard, go to **Authentication > Providers**
2. Find **Google** and click **Enable**
3. Enter your Google OAuth credentials:
   - **Client ID**: Your Google Client ID
   - **Client Secret**: Your Google Client Secret
4. Click **Save**

### **2.2 Apple Sign-In Configuration**

#### **Apple Developer Setup:**

1. Go to [Apple Developer Portal](https://developer.apple.com)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create **App ID** with Sign In with Apple capability
4. Create **Service ID** for web authentication:
   - **Description**: Obu Marketplace Web Auth
   - **Identifier**: com.obumarketplace.web
   - **Configure Sign In with Apple**:
     - **Primary App ID**: Select your App ID
     - **Return URLs**: 
       ```
       https://your-project-id.supabase.co/auth/v1/callback
       ```

5. Generate **Key** for Sign In with Apple:
   - **Key Name**: Obu Marketplace Apple Auth Key
   - **Enable Sign In with Apple**
   - Download the `.p8` key file

#### **Supabase Apple Configuration:**

1. In Supabase dashboard, go to **Authentication > Providers**
2. Find **Apple** and click **Enable**
3. Enter your Apple credentials:
   - **Service ID**: Your Apple Service ID
   - **Team ID**: Your Apple Team ID
   - **Key ID**: Your Apple Key ID
   - **Private Key**: Upload your `.p8` file content
4. Click **Save**

---

## 👥 **Step 3: Test Account Creation**

### **3.1 Admin Account Setup**

#### **Method 1: Through Supabase Dashboard**

1. Go to **Authentication > Users** in Supabase dashboard
2. Click **"Add user"**
3. Enter admin details:
   ```
   Email: <EMAIL>
   Password: try465
   Email Confirm: ON
   ```
4. Click **"Create user"**

#### **Method 2: Through Application**

1. Open your marketplace at `http://localhost:8000`
2. Click **"Register"** button
3. Fill in registration form:
   ```
   Name: Obu Admin
   Email: <EMAIL>
   Password: try465
   Confirm Password: try465
   ```
4. Click **"Create Buyer Account"**

#### **Set Admin Permissions (Optional)**

If you want admin-specific features, update the user in Supabase:

```sql
-- In Supabase SQL Editor
UPDATE auth.users 
SET raw_user_meta_data = raw_user_meta_data || '{"role": "admin"}'::jsonb
WHERE email = '<EMAIL>';

UPDATE public.users 
SET account_type = 'admin' 
WHERE email = '<EMAIL>';
```

### **3.2 User Account Setup**

#### **Create Standard User Account**

1. Open marketplace in **incognito/private window**
2. Click **"Register"** button
3. Fill in registration form:
   ```
   Name: Obu User
   Email: <EMAIL>
   Password: try465
   Confirm Password: try465
   ```
4. Click **"Create Buyer Account"**

### **3.3 Verify Account Creation**

#### **Check in Supabase Dashboard:**

1. Go to **Authentication > Users**
2. Verify both accounts are listed:
   - `<EMAIL>` - Email confirmed
   - `<EMAIL>` - Email confirmed

#### **Check in Database:**

```sql
-- In Supabase SQL Editor
SELECT id, name, email, account_type, created_at 
FROM public.users 
WHERE email IN ('<EMAIL>', '<EMAIL>');
```

---

## 🧪 **Step 4: Testing Guidelines**

### **4.1 Basic Authentication Testing**

#### **Test Email/Password Login:**

1. **Admin Account Test:**
   ```
   Email: <EMAIL>
   Password: try465
   Expected: Successful login, user menu appears
   ```

2. **User Account Test:**
   ```
   Email: <EMAIL>
   Password: try465
   Expected: Successful login, user menu appears
   ```

3. **Invalid Credentials Test:**
   ```
   Email: <EMAIL>
   Password: wrongpass
   Expected: Error message "Invalid credentials"
   ```

#### **Test Registration Flow:**

1. Use a new email address
2. Fill registration form with valid data
3. Verify account creation in Supabase dashboard
4. Test login with new account

### **4.2 Social Authentication Testing**

#### **Google OAuth Test:**

1. Click **"Continue with Google"** button
2. Should redirect to Google OAuth page
3. Sign in with Google account
4. Should redirect back and create user profile
5. Verify user appears in Supabase users table

#### **Apple Sign-In Test:**

1. Click **"Continue with Apple"** button
2. Should redirect to Apple Sign-In page
3. Sign in with Apple ID
4. Should redirect back and create user profile
5. Verify user appears in Supabase users table

### **4.3 Complete User Journey Testing**

#### **Full Buyer Journey:**

1. **Registration/Login** ✅
   ```
   - Register new account or login with test account
   - Verify user menu appears with correct name
   ```

2. **Browse Products** ✅
   ```
   - View product categories
   - Filter products by category
   - Search for specific products
   ```

3. **Shopping Cart** ✅
   ```
   - Add products to cart
   - Update quantities
   - Remove items
   - View cart total with currency conversion
   ```

4. **Checkout Process** ✅
   ```
   - Proceed to checkout
   - Choose payment method (PayMongo or traditional)
   - Select currency (PHP or USD)
   - Complete purchase
   ```

5. **Purchase History** ✅
   ```
   - View purchase history
   - Download purchased items
   - Write product reviews
   ```

### **4.4 Browser Console Testing**

Open browser console and run these commands:

```javascript
// Test authentication system
window.testObuMarketplace()

// Test Supabase connection
window.testSupabaseConnection()

// Check current user
authManager.currentUser

// Test social auth availability
typeof authManager.signInWithGoogle === 'function'
typeof authManager.signInWithApple === 'function'

// Verify buyer-only restrictions
authManager.currentUser?.accountType === 'buyer'

// Test PayMongo integration
payMongoService.getStatus()

// Test currency conversion
currencyManager.convertAmount(100, 'USD', 'PHP')
```

---

## 🔧 **Step 5: Troubleshooting**

### **5.1 Common Authentication Issues**

#### **"Supabase connection failed"**

**Symptoms:** Authentication not working, console errors
**Solutions:**
1. Verify Supabase URL and API key in `js/supabase-integration.js`
2. Check Supabase project is active and accessible
3. Verify CORS settings in Supabase dashboard

```javascript
// Test connection
window.quickSupabaseCheck()
```

#### **"Social login not working"**

**Symptoms:** Social login buttons don't redirect or fail
**Solutions:**
1. Verify OAuth provider configuration in Supabase
2. Check redirect URLs are correctly configured
3. Ensure OAuth apps are approved and active

```javascript
// Check social auth configuration
authManager.signInWithGoogle()  // Should not throw error
authManager.signInWithApple()   // Should not throw error
```

#### **"User registration fails"**

**Symptoms:** Registration form shows errors
**Solutions:**
1. Check email format is valid
2. Verify password meets requirements (min 8 characters)
3. Check Supabase email confirmation settings

```javascript
// Test validation
Utils.validateEmail('<EMAIL>')  // Should return true
Utils.validatePassword('try465')         // Should return {isValid: true}
```

#### **"PayMongo payment fails"**

**Symptoms:** Payment processing errors
**Solutions:**
1. Verify PayMongo API keys are configured
2. Check currency is supported (PHP/USD)
3. Test with PayMongo test card numbers

```javascript
// Test PayMongo service
payMongoService.testConnection()
payMongoService.getPaymentMethodsForCurrency('PHP')
```

### **5.2 Debug Commands**

#### **Authentication Status Check:**

```javascript
// Check authentication system status
console.log('Auth Manager:', typeof authManager !== 'undefined');
console.log('Current User:', authManager.currentUser);
console.log('Supabase Connected:', supabaseIntegration?.isConnected());
console.log('Social Auth Available:', {
    google: typeof authManager.signInWithGoogle === 'function',
    apple: typeof authManager.signInWithApple === 'function'
});
```

#### **Payment System Check:**

```javascript
// Check payment systems
console.log('PayMongo Available:', typeof payMongoService !== 'undefined');
console.log('Currency Manager:', typeof currencyManager !== 'undefined');
console.log('Supported Currencies:', currencyManager?.getAvailableCurrencies());
console.log('Current Currency:', currencyManager?.getCurrentCurrency());
```

#### **Database Connection Test:**

```javascript
// Test database operations
window.testSupabaseConnection().then(result => {
    console.log('Connection Test Result:', result);
});
```

#### **Complete System Verification:**

```javascript
// Run comprehensive tests
window.testComprehensive().then(() => {
    console.log('All tests completed - check results above');
});

// Run production verification
window.verifyProduction().then(status => {
    console.log('Production Readiness:', status);
});
```

### **5.3 Production Deployment Checklist**

#### **Before Going Live:**

- [ ] Update Supabase URLs to production values
- [ ] Configure production OAuth redirect URLs
- [ ] Set up custom domain for Supabase (optional)
- [ ] Enable email rate limiting
- [ ] Configure custom SMTP for emails (optional)
- [ ] Test all authentication flows on production
- [ ] Set up monitoring and error tracking
- [ ] Configure PayMongo with live API keys
- [ ] Test payment flows with real transactions

#### **Security Considerations:**

- [ ] Enable Row Level Security (RLS) policies
- [ ] Verify buyer-only restrictions are enforced
- [ ] Test authentication with different browsers
- [ ] Verify session management and timeouts
- [ ] Check for XSS and CSRF vulnerabilities
- [ ] Validate payment security measures
- [ ] Test currency conversion accuracy

---

## 📱 **Step 6: Mobile Testing**

### **6.1 Mobile Authentication Testing**

#### **Test on Different Devices:**

1. **iOS Safari:**
   - Test email/password login
   - Test Apple Sign-In (native integration)
   - Verify touch interactions work properly

2. **Android Chrome:**
   - Test email/password login
   - Test Google OAuth (native integration)
   - Verify payment forms are touch-friendly

3. **Mobile Browsers:**
   - Test responsive design
   - Verify modals display correctly
   - Test currency selector functionality

#### **Mobile-Specific Features:**

```javascript
// Test mobile features
window.testMobileFeatures()

// Check touch support
console.log('Touch Support:', 'ontouchstart' in window);

// Verify responsive elements
document.querySelectorAll('[class*="md:"], [class*="lg:"]').length
```

### **6.2 Payment Testing on Mobile**

1. **PayMongo Mobile Flow:**
   - Test card input formatting
   - Verify e-wallet redirects work
   - Test payment completion flow

2. **Currency Conversion:**
   - Test currency selector on mobile
   - Verify price updates work correctly
   - Test exchange rate display

---

## ✅ **Success Verification**

Your authentication system is properly configured when:

### **✅ Core Authentication**
- Test accounts login successfully:
  - `<EMAIL>` / `try465`
  - `<EMAIL>` / `try465`
- Social login buttons appear and function
- User registration creates accounts in Supabase
- Buyer-only restrictions are enforced

### **✅ Payment Integration**
- PayMongo service initializes without errors
- Currency conversion works between PHP and USD
- Payment methods display correctly for each currency
- Checkout flow completes successfully

### **✅ Mobile Experience**
- All features work on mobile devices
- Touch interactions are responsive
- Payment forms are mobile-optimized
- Currency selector works on small screens

### **✅ Complete User Journey**
- Login → Browse → Cart → Payment → Purchase → History
- Multi-currency pricing displays correctly
- Social authentication creates proper user profiles
- Purchase history shows completed transactions

## 🎉 **Congratulations!**

Your Obu Marketplace authentication system is now fully configured with:

- ✅ **Supabase Authentication** (email/password + social login)
- ✅ **Google OAuth** and **Apple Sign-In** integration
- ✅ **PayMongo Payment Gateway** with multi-currency support
- ✅ **Test Accounts** ready for testing
- ✅ **Mobile-Optimized** authentication flows
- ✅ **Production-Ready** security measures

The marketplace is ready for production use with secure authentication, multi-currency payments, and a complete buyer experience!

## 📞 **Support Resources**

- **Supabase Documentation:** [https://supabase.com/docs](https://supabase.com/docs)
- **PayMongo Documentation:** [https://developers.paymongo.com](https://developers.paymongo.com)
- **Google OAuth Setup:** [https://developers.google.com/identity/protocols/oauth2](https://developers.google.com/identity/protocols/oauth2)
- **Apple Sign-In Setup:** [https://developer.apple.com/sign-in-with-apple/](https://developer.apple.com/sign-in-with-apple/)

For additional support, check the troubleshooting section or run the debug commands provided above.
