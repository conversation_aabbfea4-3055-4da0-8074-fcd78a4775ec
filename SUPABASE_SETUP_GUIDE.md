# 🗄️ Supabase Database Setup Guide for Obu Marketplace

## 📋 Prerequisites

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **Project Access**: Admin access to create and modify database tables

## 🚀 Step-by-Step Setup

### 1. Create Supabase Project

1. **Login to Supabase Dashboard**
   - Go to [app.supabase.com](https://app.supabase.com)
   - Sign in with your account

2. **Create New Project**
   - Click "New Project"
   - Choose your organization
   - Fill in project details:
     - **Name**: `obu-marketplace`
     - **Database Password**: Generate a strong password (save this!)
     - **Region**: Choose closest to your target users
   - Click "Create new project"

3. **Wait for Setup**
   - Project creation takes 1-2 minutes
   - You'll see a dashboard when ready

### 2. Configure Database Schema

1. **Open SQL Editor**
   - In your Supabase dashboard, go to "SQL Editor"
   - Click "New query"

2. **Run Schema Script**
   - Copy the entire content from `supabase-schema.sql`
   - Paste it into the SQL editor
   - Click "Run" to execute
   - This creates all tables, indexes, and triggers

3. **Verify Tables Created**
   - Go to "Table Editor" in the sidebar
   - You should see these tables:
     - `users`
     - `categories`
     - `products`
     - `purchases`
     - `reviews`
     - `cart_items`
     - `user_sessions`

### 3. Insert Sample Data

1. **Run Sample Data Script**
   - In SQL Editor, create a new query
   - Copy content from `supabase-sample-data.sql`
   - Paste and run the script
   - This adds categories, products, and initial ratings

2. **Verify Data**
   - Go to Table Editor
   - Check `categories` table has 4 categories
   - Check `products` table has 12 products
   - All products should have ratings and review counts

### 4. Configure Authentication

1. **Enable Email Authentication**
   - Go to "Authentication" → "Settings"
   - Under "Auth Providers", ensure Email is enabled
   - Set "Enable email confirmations" to OFF for development
   - Set "Enable email change confirmations" to OFF for development

2. **Configure Email Templates (Optional)**
   - Go to "Authentication" → "Email Templates"
   - Customize signup/reset email templates if needed

3. **Set Auth Policies**
   - Go to "Authentication" → "Policies"
   - The schema includes RLS policies for security

### 5. Get Project Credentials

1. **Find Project URL and Keys**
   - Go to "Settings" → "API"
   - Copy these values:
     - **Project URL**: `https://your-project-id.supabase.co`
     - **Anon/Public Key**: `eyJ...` (long string)

2. **Update Frontend Configuration**
   - Open `js/supabase-config.js`
   - Replace placeholders:
     ```javascript
     this.supabaseUrl = 'https://your-project-id.supabase.co';
     this.supabaseKey = 'your-anon-key-here';
     ```

### 6. Include Supabase Client Library

Add this to your HTML files (before your custom scripts):

```html
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
<script src="js/supabase-config.js"></script>
```

### 7. Update Your Application

1. **Modify Storage Manager**
   - Update `js/storage.js` to use Supabase instead of localStorage
   - Add fallback to localStorage for offline functionality

2. **Update Authentication**
   - Modify `js/managers/AuthManager.js` to use Supabase Auth
   - Handle async authentication flows

3. **Test the Integration**
   - Try user registration
   - Test product loading
   - Verify cart functionality
   - Test review system

## 🔧 Database Schema Overview

### Tables Structure

```sql
users (extends Supabase auth.users)
├── id (UUID, Primary Key)
├── name (Text)
├── email (Text, Unique)
├── account_type (Text, Default: 'buyer')
└── timestamps

categories
├── id (Serial, Primary Key)
├── name (Text, Unique)
├── icon (Text)
├── gradient (Text)
├── description (Text)
├── product_count (Integer)
└── timestamps

products
├── id (Serial, Primary Key)
├── title, description (Text)
├── category_id (Foreign Key)
├── price, original_price (Decimal)
├── image_url (Text)
├── rating (Decimal 0-5)
├── review_count (Integer)
├── stock (Integer)
├── featured (Boolean)
└── timestamps

purchases
├── id (UUID, Primary Key)
├── buyer_id (Foreign Key to users)
├── product_id (Foreign Key to products)
├── pricing details (subtotal, tax, total)
├── payment_method (Text)
├── status (Text)
├── download tracking
└── timestamps

reviews
├── id (UUID, Primary Key)
├── user_id (Foreign Key to users)
├── product_id (Foreign Key to products)
├── purchase_id (Foreign Key to purchases)
├── rating (Integer 1-5)
├── review_text (Text)
├── pros, cons (Text, Optional)
├── helpful_votes (Integer)
└── timestamps

cart_items
├── id (UUID, Primary Key)
├── user_id (Foreign Key to users)
├── product_id (Foreign Key to products)
├── quantity (Integer)
└── timestamps
```

### Key Features

- **Row Level Security (RLS)**: Automatic data isolation per user
- **Automatic Triggers**: Update product ratings when reviews change
- **Referential Integrity**: Foreign key constraints maintain data consistency
- **Optimized Indexes**: Fast queries for common operations
- **UUID Primary Keys**: Secure, non-sequential identifiers

## 🔒 Security Configuration

### Row Level Security Policies

The schema includes RLS policies to ensure:
- Users can only see their own purchases, cart items, and reviews
- Products and categories are publicly readable
- Only authenticated users can create purchases and reviews
- Users cannot modify other users' data

### Authentication Flow

1. **Registration**: Creates user in `auth.users` and `public.users`
2. **Login**: Supabase handles JWT tokens automatically
3. **Session Management**: Automatic token refresh
4. **Logout**: Clears session and tokens

## 🧪 Testing Your Setup

### 1. Test Database Connection
```javascript
// In browser console
console.log(await supabaseManager.init());
// Should return true if connected
```

### 2. Test Data Retrieval
```javascript
// Test getting products
const products = await supabaseManager.getProducts();
console.log(products.length); // Should show 12 products
```

### 3. Test Authentication
```javascript
// Test user registration
const result = await supabaseManager.signUp(
    '<EMAIL>', 
    'password123', 
    { name: 'Test User' }
);
console.log(result);
```

## 🚨 Important Notes

1. **Environment Variables**: In production, use environment variables for credentials
2. **Email Confirmation**: Enable email confirmation in production
3. **Rate Limiting**: Configure rate limiting for auth endpoints
4. **Backup Strategy**: Set up automated backups
5. **Monitoring**: Enable logging and monitoring in Supabase dashboard

## 🔄 Migration from localStorage

If you want to migrate existing localStorage data:

1. Export current localStorage data
2. Transform data to match Supabase schema
3. Use Supabase client to insert data
4. Update frontend to use Supabase methods

## 📞 Support

- **Supabase Docs**: [supabase.com/docs](https://supabase.com/docs)
- **Community**: [github.com/supabase/supabase/discussions](https://github.com/supabase/supabase/discussions)
- **Discord**: [discord.supabase.com](https://discord.supabase.com)

Your Obu Marketplace is now ready for production with a robust, scalable database backend! 🎉
