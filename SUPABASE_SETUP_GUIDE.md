# 🚀 Complete Supabase Setup Guide for Obu Marketplace

## 📋 **Prerequisites**
- Supabase account (free tier is sufficient)
- Google Cloud Console account (for Google OAuth)
- Apple Developer account (for Apple Sign-In)

## 🗄️ **Step 1: Create Supabase Project**

1. **Go to [Supabase](https://supabase.com) and create a new project**
2. **Choose your organization and project name**: `obu-marketplace`
3. **Select a region** closest to your users
4. **Generate a strong database password** and save it securely
5. **Wait for project creation** (usually takes 2-3 minutes)

## 📊 **Step 2: Set Up Database Schema**

### **2.1 Run Schema Creation Script**
1. Go to **SQL Editor** in your Supabase dashboard
2. Create a new query and paste the contents of `supabase-complete-schema.sql`
3. Click **Run** to create all tables, indexes, and triggers
4. Verify success - you should see "Success. No rows returned" message

### **2.2 Set Up Row Level Security**
1. Create another new query in SQL Editor
2. Paste the contents of `supabase-rls-policies.sql`
3. Click **Run** to enable RLS and create all security policies
4. Verify all policies are created successfully

### **2.3 Insert Sample Data**
1. Create a third new query in SQL Editor
2. Paste the contents of `supabase-complete-sample-data.sql`
3. Click **Run** to populate your database with sample products and categories
4. Verify data insertion by running: `SELECT COUNT(*) FROM products;`

## 🔐 **Step 3: Configure Authentication**

### **3.1 Enable Email Authentication**
1. Go to **Authentication > Settings** in Supabase dashboard
2. Ensure **Enable email confirmations** is turned ON
3. Set **Site URL** to your domain (e.g., `https://yourdomain.com` or `http://localhost:8000` for development)
4. Add your domain to **Redirect URLs** list

### **3.2 Set Up Google OAuth**

#### **Google Cloud Console Setup:**
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable **Google+ API** and **Google Identity API**
4. Go to **Credentials > Create Credentials > OAuth 2.0 Client IDs**
5. Set **Application type** to "Web application"
6. Add **Authorized redirect URIs**:
   ```
   https://YOUR_SUPABASE_PROJECT_ID.supabase.co/auth/v1/callback
   ```
7. Copy **Client ID** and **Client Secret**

#### **Supabase Configuration:**
1. In Supabase dashboard, go to **Authentication > Providers**
2. Find **Google** and click **Enable**
3. Paste your **Client ID** and **Client Secret**
4. Click **Save**

### **3.3 Set Up Apple Sign-In**

#### **Apple Developer Setup:**
1. Go to [Apple Developer Portal](https://developer.apple.com)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create a new **App ID** with Sign In with Apple capability
4. Create a **Service ID** for web authentication
5. Configure **Return URLs**:
   ```
   https://YOUR_SUPABASE_PROJECT_ID.supabase.co/auth/v1/callback
   ```
6. Generate a **Key** for Sign In with Apple
7. Note down: **Team ID**, **Key ID**, **Service ID**, and download the **Private Key**

#### **Supabase Configuration:**
1. In Supabase dashboard, go to **Authentication > Providers**
2. Find **Apple** and click **Enable**
3. Enter your **Service ID**, **Team ID**, **Key ID**
4. Upload your **Private Key** file
5. Click **Save**

## 🔧 **Step 4: Configure Frontend Integration**

### **4.1 Update Supabase Configuration**
1. Open `js/supabase-integration.js`
2. Replace the placeholder values:
   ```javascript
   this.supabaseUrl = 'https://YOUR_PROJECT_ID.supabase.co';
   this.supabaseKey = 'YOUR_ANON_PUBLIC_KEY';
   ```
3. Get these values from **Settings > API** in your Supabase dashboard

### **4.2 Add Supabase JavaScript Client**
Add this script tag to your HTML files (before other scripts):
```html
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
```

## 🧪 **Step 5: Test Your Setup**

### **5.1 Test Database Connection**
1. Open your application in browser
2. Open browser console and run:
   ```javascript
   supabaseIntegration.testConnection()
   ```
3. Should return `true` if connection is successful

### **5.2 Test Authentication**
1. Try registering a new user with email/password
2. Test Google OAuth login (if configured)
3. Test Apple Sign-In (if configured)
4. Verify user profiles are created in `users` table

### **5.3 Test Complete Functionality**
1. Run comprehensive tests in browser console:
   ```javascript
   window.testComprehensive()
   window.verifyProduction()
   ```
2. Verify all tests pass

## 📋 **Step 6: Production Configuration**

### **6.1 Environment Variables**
Set up environment variables for production:
```bash
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key
GOOGLE_CLIENT_ID=your-google-client-id
APPLE_SERVICE_ID=your-apple-service-id
```

### **6.2 Security Settings**
1. **Enable RLS** on all tables (already done in step 2.2)
2. **Configure CORS** for your production domain
3. **Set up proper redirect URLs** for production
4. **Enable email rate limiting** in Supabase Auth settings
5. **Configure password requirements** (minimum 8 characters)

### **6.3 Monitoring and Backup**
1. **Enable database backups** in Supabase dashboard
2. **Set up monitoring** for API usage and errors
3. **Configure email templates** for authentication emails
4. **Test disaster recovery** procedures

## 🔍 **Step 7: Verification Checklist**

### **Database Setup ✅**
- [ ] All tables created successfully
- [ ] RLS policies enabled and working
- [ ] Sample data inserted correctly
- [ ] Indexes created for performance
- [ ] Triggers working for automatic updates

### **Authentication Setup ✅**
- [ ] Email authentication working
- [ ] Google OAuth configured and tested
- [ ] Apple Sign-In configured and tested
- [ ] User profiles created automatically
- [ ] Buyer-only restrictions enforced

### **Frontend Integration ✅**
- [ ] Supabase client connected
- [ ] Social login buttons working
- [ ] User registration/login flow complete
- [ ] Data synchronization working
- [ ] localStorage fallback functional

### **Security & Performance ✅**
- [ ] RLS policies protecting data
- [ ] CORS configured correctly
- [ ] Rate limiting enabled
- [ ] Database indexes optimized
- [ ] Error handling implemented

## 🚨 **Troubleshooting Common Issues**

### **Connection Issues**
- Verify Supabase URL and API key are correct
- Check CORS settings for your domain
- Ensure network connectivity to Supabase

### **Authentication Issues**
- Verify OAuth redirect URLs are correct
- Check provider credentials (Google/Apple)
- Ensure email confirmation is properly configured

### **Database Issues**
- Check RLS policies if data access fails
- Verify table relationships and foreign keys
- Monitor database logs for errors

### **Performance Issues**
- Check database indexes are created
- Monitor API usage and rate limits
- Optimize queries for large datasets

## 🎉 **Success!**

Your Obu Marketplace is now fully configured with Supabase! The platform includes:

- ✅ **Complete database schema** with all necessary tables
- ✅ **Row Level Security** protecting user data
- ✅ **Social authentication** with Google and Apple
- ✅ **Real-time data synchronization** with localStorage fallback
- ✅ **Production-ready security** and performance optimization

## 📞 **Support**

If you encounter any issues:
1. Check the troubleshooting section above
2. Review Supabase documentation
3. Test with the provided verification scripts
4. Ensure all configuration steps were completed correctly

Your Obu Marketplace is now ready for production use! 🚀
