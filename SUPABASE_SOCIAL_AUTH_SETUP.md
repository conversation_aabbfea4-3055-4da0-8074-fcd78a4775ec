# 🔐 Supabase Social Authentication Setup Guide

## 📋 **Current Issue**

The Obu Marketplace is showing this error when users try to login with Google or Apple:
```json
{"code":400,"error_code":"validation_failed","msg":"Unsupported provider: provider is not enabled"}
```

This occurs because the social authentication providers are not configured in the Supabase dashboard.

## 🎯 **Quick Fix Options**

### **Option 1: Use Email/Password Authentication (Immediate)**
- ✅ **Already Working**: Email/password login is fully functional
- ✅ **No Configuration Needed**: Works with current setup
- ✅ **Full Features**: All marketplace features available
- ✅ **Secure**: Proper authentication and session management

### **Option 2: Configure Social Authentication (Advanced)**
Follow the detailed setup instructions below to enable Google and Apple Sign-In.

## 🚀 **Supabase Social Auth Configuration**

### **Prerequisites**
- Access to Supabase Dashboard: https://supabase.com/dashboard
- Project ID: `rvdjfqlydmnlblpbjjlc`
- Admin access to the Supabase project

### **Step 1: Access Supabase Dashboard**
1. Go to https://supabase.com/dashboard
2. Sign in to your account
3. Select the project: `rvdjfqlydmnlblpbjjlc`
4. Navigate to **Authentication** → **Providers**

### **Step 2: Configure Google OAuth**

#### **2.1 Enable Google Provider**
1. In Supabase Dashboard → Authentication → Providers
2. Find "Google" in the list
3. Toggle **"Enable sign in with Google"** to ON

#### **2.2 Get Google OAuth Credentials**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Navigate to **APIs & Services** → **Credentials**
4. Click **"Create Credentials"** → **"OAuth 2.0 Client IDs"**
5. Choose **"Web application"** as application type
6. Add authorized redirect URIs:
   ```
   https://rvdjfqlydmnlblpbjjlc.supabase.co/auth/v1/callback
   ```
7. Copy the **Client ID** and **Client Secret**

#### **2.3 Configure in Supabase**
1. Back in Supabase Dashboard → Authentication → Providers → Google
2. Paste **Client ID** in the "Client ID" field
3. Paste **Client Secret** in the "Client Secret" field
4. Click **"Save"**

### **Step 3: Configure Apple Sign-In**

#### **3.1 Enable Apple Provider**
1. In Supabase Dashboard → Authentication → Providers
2. Find "Apple" in the list
3. Toggle **"Enable sign in with Apple"** to ON

#### **3.2 Get Apple Credentials**
1. Go to [Apple Developer Portal](https://developer.apple.com/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create a **Service ID** for your app
4. Configure the Service ID with:
   - **Return URLs**: `https://rvdjfqlydmnlblpbjjlc.supabase.co/auth/v1/callback`
5. Create a **Key** for Sign in with Apple
6. Download the key file (.p8)

#### **3.3 Configure in Supabase**
1. Back in Supabase Dashboard → Authentication → Providers → Apple
2. Enter your **Service ID** (Bundle ID)
3. Enter your **Team ID**
4. Enter your **Key ID**
5. Upload or paste the **Private Key** content
6. Click **"Save"**

### **Step 4: Update Site URL Settings**

#### **4.1 Configure Site URL**
1. In Supabase Dashboard → Authentication → Settings
2. Update **Site URL** to:
   ```
   http://localhost:8000
   ```
   (for development)

#### **4.2 Add Additional Redirect URLs**
1. In **Additional Redirect URLs**, add:
   ```
   http://localhost:8000/
   http://localhost:3000/
   https://your-production-domain.com/
   ```

### **Step 5: Test Configuration**

#### **5.1 Test in Browser**
1. Open the Obu Marketplace: http://localhost:8000
2. Click "Login" button
3. Try "Continue with Google" or "Continue with Apple"
4. Should redirect to provider login page

#### **5.2 Test with Debug Console**
```javascript
// Open browser console and run:
testSocialProviders();

// Check status:
getSocialAuthStatus();
```

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Issue 1: "provider is not enabled"**
- **Cause**: Provider not enabled in Supabase dashboard
- **Fix**: Follow Step 2.1 or 3.1 above

#### **Issue 2: "redirect_uri_mismatch"**
- **Cause**: Redirect URL not configured correctly
- **Fix**: Ensure redirect URLs match exactly:
  ```
  https://rvdjfqlydmnlblpbjjlc.supabase.co/auth/v1/callback
  ```

#### **Issue 3: "Invalid client credentials"**
- **Cause**: Wrong Client ID/Secret for Google or wrong Apple credentials
- **Fix**: Double-check credentials in Step 2.3 or 3.3

#### **Issue 4: "Site URL mismatch"**
- **Cause**: Site URL not configured in Supabase
- **Fix**: Update Site URL in Step 4.1

### **Debug Commands**
```javascript
// Test social auth status
window.getSocialAuthStatus();

// Retry provider tests
window.retrySocialAuth('google');
window.retrySocialAuth('apple');

// Show auth system status
window.showAuthStatus();
```

## 🎯 **Current Fallback System**

### **What's Already Working**
- ✅ **Automatic Error Detection**: System detects when providers are not configured
- ✅ **User-Friendly Messages**: Shows helpful error messages instead of raw API errors
- ✅ **Email/Password Fallback**: Fully functional alternative authentication
- ✅ **Configuration Help**: Provides setup instructions when needed
- ✅ **Button State Management**: Disables social buttons when not configured

### **Error Handling Features**
- **Graceful Degradation**: App continues to work without social auth
- **Clear Messaging**: Users understand why social login isn't available
- **Alternative Options**: Email/password registration prominently offered
- **Configuration Guidance**: Step-by-step setup instructions provided

## 📊 **Authentication Options Summary**

| Method | Status | Setup Required | User Experience |
|--------|--------|----------------|-----------------|
| **Email/Password** | ✅ Working | None | Immediate registration/login |
| **Google OAuth** | ⚠️ Needs Config | Google Console + Supabase | One-click login after setup |
| **Apple Sign-In** | ⚠️ Needs Config | Apple Developer + Supabase | One-click login after setup |
| **Node.js Backend** | ✅ Working | None | API-based authentication |

## 🚀 **Recommendations**

### **For Immediate Use**
1. **Use Email/Password Authentication** - Fully functional and secure
2. **Promote Email Registration** - Clear call-to-action for users
3. **Monitor Error Logs** - Track social auth attempts for future configuration

### **For Enhanced User Experience**
1. **Configure Google OAuth** - Most popular social login option
2. **Add Apple Sign-In** - Required for iOS app store if planning mobile app
3. **Test Thoroughly** - Verify all redirect URLs and credentials

### **For Production Deployment**
1. **Update Site URLs** - Configure production domain in Supabase
2. **Enable HTTPS** - Required for social authentication in production
3. **Monitor Analytics** - Track authentication method preferences

## 🎉 **Current System Benefits**

Even without social authentication configured, the Obu Marketplace provides:

- ✅ **Robust Authentication**: Secure email/password system
- ✅ **User Management**: Complete user profiles and session handling
- ✅ **Error Handling**: Graceful fallbacks and clear messaging
- ✅ **Development Ready**: Full backend API with Node.js server
- ✅ **Production Ready**: Scalable authentication architecture

**The marketplace is fully functional with email/password authentication while social auth configuration is optional for enhanced user experience.**
