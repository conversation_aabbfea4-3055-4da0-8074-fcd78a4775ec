<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Obu Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-dark-primary font-inter">
    <!-- Navigation -->
    <nav class="nav-glass backdrop-blur-xl border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="index.html" class="text-3xl font-black bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent glow-text">
                        Obu Marketplace
                    </a>
                    <span class="ml-6 text-white/60 text-lg">Admin Panel</span>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.html" class="text-white/70 hover:text-white transition-colors duration-300">
                        <i class="fas fa-home mr-2"></i>
                        Back to Site
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-black text-white mb-4">
                Admin <span class="bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">Dashboard</span>
            </h1>
            <p class="text-white/70 text-lg">Manage products, categories, and marketplace data</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="feature-card">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-cyan-400 to-blue-500 w-12 h-12 rounded-xl mr-4">
                        <i class="fas fa-box text-lg"></i>
                    </div>
                    <div>
                        <p class="text-white/60 text-sm">Total Products</p>
                        <p id="totalProducts" class="text-2xl font-bold text-white">0</p>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-purple-400 to-pink-500 w-12 h-12 rounded-xl mr-4">
                        <i class="fas fa-tags text-lg"></i>
                    </div>
                    <div>
                        <p class="text-white/60 text-sm">Categories</p>
                        <p id="totalCategories" class="text-2xl font-bold text-white">0</p>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-green-400 to-emerald-500 w-12 h-12 rounded-xl mr-4">
                        <i class="fas fa-users text-lg"></i>
                    </div>
                    <div>
                        <p class="text-white/60 text-sm">Total Users</p>
                        <p id="totalUsers" class="text-2xl font-bold text-white">0</p>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-yellow-400 to-orange-500 w-12 h-12 rounded-xl mr-4">
                        <i class="fas fa-shopping-cart text-lg"></i>
                    </div>
                    <div>
                        <p class="text-white/60 text-sm">Total Purchases</p>
                        <p id="totalOrders" class="text-2xl font-bold text-white">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <button onclick="app.showAddProductModal()" class="feature-card group text-left">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-cyan-400 to-blue-500 w-16 h-16 rounded-xl mr-4">
                        <i class="fas fa-plus text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white mb-1 group-hover:text-cyan-400 transition-colors">Add Product</h3>
                        <p class="text-white/60 text-sm">Create a new product listing</p>
                    </div>
                </div>
            </button>

            <button onclick="showAddCategoryModal()" class="feature-card group text-left">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-purple-400 to-pink-500 w-16 h-16 rounded-xl mr-4">
                        <i class="fas fa-tag text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white mb-1 group-hover:text-purple-400 transition-colors">Add Category</h3>
                        <p class="text-white/60 text-sm">Create a new product category</p>
                    </div>
                </div>
            </button>

            <button onclick="showDataManagement()" class="feature-card group text-left">
                <div class="flex items-center">
                    <div class="feature-icon bg-gradient-to-br from-green-400 to-emerald-500 w-16 h-16 rounded-xl mr-4">
                        <i class="fas fa-database text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-white mb-1 group-hover:text-green-400 transition-colors">Manage Data</h3>
                        <p class="text-white/60 text-sm">Import/export marketplace data</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Management -->
        <div class="feature-card mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-white">Products Management</h2>
                <div class="flex space-x-4">
                    <input type="text" 
                           id="productSearch" 
                           placeholder="Search products..." 
                           class="search-input px-4 py-2 bg-dark-secondary/50 border border-white/10 rounded-lg text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    <select id="categoryFilter" 
                            class="px-4 py-2 bg-dark-secondary/50 border border-white/10 rounded-lg text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                        <option value="">All Categories</option>
                    </select>
                </div>
            </div>
            
            <div id="productsTable" class="overflow-x-auto">
                <!-- Products table will be populated here -->
            </div>
        </div>

        <!-- Categories Management -->
        <div class="feature-card">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-white">Categories Management</h2>
                <button onclick="categoryManager.showCategoryModal()" class="btn-primary px-4 py-2 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>Add Category
                </button>
            </div>
            
            <div id="categoriesTable" class="overflow-x-auto">
                <!-- Categories table will be populated here -->
            </div>
        </div>
    </div>

    <!-- Data Management Modal -->
    <div id="dataModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-card bg-dark-secondary/95 backdrop-blur-xl border border-white/10 rounded-2xl max-w-2xl w-full p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-white">Data Management</h3>
                    <button onclick="document.getElementById('dataModal').classList.add('hidden')" class="text-white/60 hover:text-white transition-colors duration-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div class="space-y-6">
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Export Data</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <button onclick="app.exportData()" class="btn-primary py-3 rounded-xl">
                                <i class="fas fa-download mr-2"></i>Export All Data
                            </button>
                            <button onclick="categoryManager.exportCategories()" class="btn-secondary py-3 rounded-xl">
                                <i class="fas fa-tags mr-2"></i>Export Categories
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Import Data</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-white/80 mb-2">Import Categories</label>
                                <input type="file" 
                                       id="importCategories" 
                                       accept=".json"
                                       class="w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-cyan-400 file:text-dark-primary file:font-semibold hover:file:bg-cyan-300">
                            </div>
                            <button onclick="handleImportCategories()" class="btn-secondary w-full py-3 rounded-xl">
                                <i class="fas fa-upload mr-2"></i>Import Categories
                            </button>
                        </div>
                    </div>
                    
                    <div class="border-t border-white/10 pt-6">
                        <h4 class="text-lg font-semibold text-red-400 mb-4">Danger Zone</h4>
                        <button onclick="clearAllData()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors">
                            <i class="fas fa-trash mr-2"></i>Clear All Data
                        </button>
                        <p class="text-xs text-white/50 mt-2">This action cannot be undone!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/ProductManager.js"></script>
    <script src="js/managers/CategoryManager.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/managers/CartManager.js"></script>
    <script src="js/app.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
