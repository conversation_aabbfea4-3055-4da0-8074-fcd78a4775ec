<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Dashboard - Obu Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-dark-primary font-inter">
    <!-- Navigation -->
    <nav class="nav-glass backdrop-blur-xl border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="index.html" class="text-3xl font-black bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent glow-text">
                        Obu Marketplace
                    </a>
                    <span class="ml-6 text-white/60 text-lg">Seller Dashboard</span>
                </div>
                <div class="flex items-center space-x-6">
                    <button class="relative p-3 text-white/70 hover:text-white transition-all duration-300 hover:scale-110">
                        <i class="fas fa-bell text-xl"></i>
                        <div class="notification-badge">3</div>
                    </button>
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center text-white hover:text-cyan-400 transition-colors duration-300">
                            <i class="fas fa-user-circle text-3xl mr-3"></i>
                            <span id="userName" class="font-semibold">Seller</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-72 bg-dark-secondary/30 backdrop-blur-xl border-r border-white/10 min-h-screen">
            <div class="p-8">
                <nav class="space-y-3">
                    <a href="#dashboard" class="nav-item active flex items-center px-6 py-4 text-cyan-400 bg-cyan-400/10 border border-cyan-400/20 rounded-xl backdrop-blur-sm">
                        <i class="fas fa-chart-bar mr-4 text-lg"></i>
                        <span class="font-semibold">Dashboard</span>
                    </a>
                    <a href="#products" class="nav-item flex items-center px-6 py-4 text-white/70 hover:text-white hover:bg-white/5 rounded-xl transition-all duration-300">
                        <i class="fas fa-box mr-4 text-lg"></i>
                        <span class="font-semibold">Products</span>
                    </a>
                    <a href="#orders" class="nav-item flex items-center px-6 py-4 text-white/70 hover:text-white hover:bg-white/5 rounded-xl transition-all duration-300">
                        <i class="fas fa-shopping-cart mr-4 text-lg"></i>
                        <span class="font-semibold">Orders</span>
                    </a>
                    <a href="#analytics" class="nav-item flex items-center px-6 py-4 text-white/70 hover:text-white hover:bg-white/5 rounded-xl transition-all duration-300">
                        <i class="fas fa-chart-line mr-4 text-lg"></i>
                        <span class="font-semibold">Analytics</span>
                    </a>
                    <a href="#settings" class="nav-item flex items-center px-6 py-4 text-white/70 hover:text-white hover:bg-white/5 rounded-xl transition-all duration-300">
                        <i class="fas fa-cog mr-4 text-lg"></i>
                        <span class="font-semibold">Settings</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Dashboard Overview</h1>
                    <p class="text-gray-600">Welcome back! Here's what's happening with your store.</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="bg-green-100 p-3 rounded-full">
                                <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Total Revenue</p>
                                <p class="text-2xl font-bold text-gray-900">$2,847.50</p>
                                <p class="text-sm text-green-600">+12.5% from last month</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Total Orders</p>
                                <p class="text-2xl font-bold text-gray-900">156</p>
                                <p class="text-sm text-blue-600">+8.2% from last month</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-3 rounded-full">
                                <i class="fas fa-box text-purple-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Active Products</p>
                                <p class="text-2xl font-bold text-gray-900">23</p>
                                <p class="text-sm text-purple-600">3 new this week</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <i class="fas fa-star text-yellow-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Average Rating</p>
                                <p class="text-2xl font-bold text-gray-900">4.8</p>
                                <p class="text-sm text-yellow-600">Based on 89 reviews</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">Recent Orders</h2>
                        <a href="#orders" class="text-purple-600 hover:underline">View all</a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-2">Order ID</th>
                                    <th class="text-left py-2">Product</th>
                                    <th class="text-left py-2">Customer</th>
                                    <th class="text-left py-2">Amount</th>
                                    <th class="text-left py-2">Status</th>
                                    <th class="text-left py-2">Date</th>
                                </tr>
                            </thead>
                            <tbody id="recentOrdersTable">
                                <!-- Orders will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products-section" class="section hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">Products</h1>
                        <p class="text-gray-600">Manage your product listings</p>
                    </div>
                    <button id="addProductBtn" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Add Product
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="productsGrid">
                        <!-- Products will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders-section" class="section hidden">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Orders</h1>
                    <p class="text-gray-600">Manage customer orders and fulfillment</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3">Order ID</th>
                                    <th class="text-left py-3">Product</th>
                                    <th class="text-left py-3">Customer</th>
                                    <th class="text-left py-3">Quantity</th>
                                    <th class="text-left py-3">Total</th>
                                    <th class="text-left py-3">Tax (10%)</th>
                                    <th class="text-left py-3">Status</th>
                                    <th class="text-left py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTable">
                                <!-- Orders will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="section hidden">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Analytics</h1>
                    <p class="text-gray-600">Track your performance and growth</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold mb-4">Revenue Breakdown</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Product Sales</span>
                                <span class="font-semibold">$2,588.64</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Platform Tax (10%)</span>
                                <span class="font-semibold text-red-600">-$258.86</span>
                            </div>
                            <div class="border-t pt-2">
                                <div class="flex justify-between items-center">
                                    <span class="font-semibold">Net Revenue</span>
                                    <span class="font-bold text-green-600">$2,329.78</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold mb-4">Top Categories</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Game Top Up</span>
                                <span class="font-semibold">45%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Gift Cards</span>
                                <span class="font-semibold">30%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Game Items</span>
                                <span class="font-semibold">25%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-2xl w-full p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Add New Product</h3>
                    <button id="closeAddProductModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="addProductForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Product Title</label>
                            <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select name="category" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select category</option>
                                <option value="Game Top Up">Game Top Up</option>
                                <option value="Gaming Accounts">Gaming Accounts</option>
                                <option value="Gift Cards">Gift Cards</option>
                                <option value="Game Items">Game Items</option>
                                <option value="Streaming">Streaming</option>
                                <option value="Software">Software</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Price ($)</label>
                            <input type="number" name="price" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Original Price ($)</label>
                            <input type="number" name="original_price" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Stock Quantity</label>
                            <input type="number" name="stock_quantity" value="1" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"></textarea>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Image URL</label>
                            <input type="url" name="image_url" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="instant_delivery" checked class="mr-2">
                                <span class="text-sm text-gray-700">Instant delivery</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-4 mt-6">
                        <button type="button" id="cancelAddProduct" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                            Add Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
