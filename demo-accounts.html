<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#0f0f23">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Demo Accounts - Obu Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-dark-primary font-inter">
    <!-- Navigation -->
    <nav class="nav-glass backdrop-blur-xl border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="index.html" class="text-3xl font-black bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent glow-text">
                        Obu Marketplace
                    </a>
                    <span class="ml-6 text-white/60 text-lg">Demo Accounts</span>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.html" class="text-white/70 hover:text-white transition-colors duration-300">
                        <i class="fas fa-home mr-2"></i>
                        Back to Site
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-black text-white mb-4">
                Demo <span class="bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">Accounts</span>
            </h1>
            <p class="text-white/70 text-lg">Test the authentication system with these pre-created accounts</p>
        </div>

        <!-- Demo Accounts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <!-- Demo Account 1 -->
            <div class="feature-card">
                <div class="mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white text-center mb-2">Demo User 1</h3>
                    <p class="text-white/60 text-center text-sm">Regular buyer account</p>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="bg-dark-primary/30 rounded-lg p-3">
                        <div class="text-sm text-white/60">Email:</div>
                        <div class="text-white font-mono"><EMAIL></div>
                    </div>
                    <div class="bg-dark-primary/30 rounded-lg p-3">
                        <div class="text-sm text-white/60">Password:</div>
                        <div class="text-white font-mono">password123</div>
                    </div>
                </div>
                
                <button onclick="loginWithDemo('<EMAIL>', 'password123')" 
                        class="btn-primary w-full py-3 rounded-xl font-semibold">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Login as Demo User 1
                </button>
            </div>

            <!-- Demo Account 2 -->
            <div class="feature-card">
                <div class="mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-circle text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white text-center mb-2">Demo User 2</h3>
                    <p class="text-white/60 text-center text-sm">Buyer with purchase history</p>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="bg-dark-primary/30 rounded-lg p-3">
                        <div class="text-sm text-white/60">Email:</div>
                        <div class="text-white font-mono"><EMAIL></div>
                    </div>
                    <div class="bg-dark-primary/30 rounded-lg p-3">
                        <div class="text-sm text-white/60">Password:</div>
                        <div class="text-white font-mono">password123</div>
                    </div>
                </div>
                
                <button onclick="loginWithDemo('<EMAIL>', 'password123')" 
                        class="btn-primary w-full py-3 rounded-xl font-semibold">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Login as Demo User 2
                </button>
            </div>
        </div>

        <!-- Instructions -->
        <div class="feature-card">
            <h3 class="text-xl font-bold text-white mb-4">
                <i class="fas fa-info-circle mr-2 text-cyan-400"></i>
                How to Test
            </h3>
            
            <div class="space-y-4 text-white/80">
                <div class="flex items-start">
                    <div class="w-6 h-6 bg-cyan-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                        <span class="text-dark-primary text-xs font-bold">1</span>
                    </div>
                    <div>
                        <strong>Login:</strong> Click one of the demo account buttons above to automatically log in
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-6 h-6 bg-purple-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                        <span class="text-dark-primary text-xs font-bold">2</span>
                    </div>
                    <div>
                        <strong>Shop:</strong> Browse products, add items to cart, and complete purchases
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-6 h-6 bg-pink-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                        <span class="text-dark-primary text-xs font-bold">3</span>
                    </div>
                    <div>
                        <strong>History:</strong> View your purchase history and download purchased items
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                        <span class="text-dark-primary text-xs font-bold">4</span>
                    </div>
                    <div>
                        <strong>Register:</strong> Create your own account to test the registration system
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-shield-alt text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">Secure Authentication</h4>
                <p class="text-white/60 text-sm">Password hashing and session management</p>
            </div>
            
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">Complete Shopping</h4>
                <p class="text-white/60 text-sm">Cart, checkout, and purchase history</p>
            </div>
            
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-download text-white"></i>
                </div>
                <h4 class="font-semibold text-white mb-2">Digital Delivery</h4>
                <p class="text-white/60 text-sm">Instant downloads and access management</p>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/supabase-integration.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/ProductManager.js"></script>
    <script src="js/managers/CategoryManager.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/managers/CartManager.js"></script>
    <script src="js/managers/ReviewManager.js"></script>
    <script src="js/test-functionality.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Create demo accounts on page load
        document.addEventListener('DOMContentLoaded', function() {
            app.init().then(() => {
                createDemoAccounts();
            });
        });

        function createDemoAccounts() {
            const demoAccounts = [
                {
                    name: 'Demo User 1',
                    email: '<EMAIL>',
                    password: 'password123'
                },
                {
                    name: 'Demo User 2',
                    email: '<EMAIL>',
                    password: 'password123'
                }
            ];

            demoAccounts.forEach(account => {
                if (!storage.getUserByEmail(account.email)) {
                    storage.addUser(account);
                }
            });

            // Add some demo purchases for demo user 2
            const demoUser2 = storage.getUserByEmail('<EMAIL>');
            if (demoUser2) {
                const existingPurchases = storage.getPurchasesByUser(demoUser2.id);
                if (existingPurchases.length === 0) {
                    // Add demo purchases
                    const products = storage.getProducts();
                    if (products.length > 0) {
                        const product = products[0];
                        const purchase = storage.addPurchase({
                            buyerId: demoUser2.id,
                            buyerName: demoUser2.name,
                            buyerEmail: demoUser2.email,
                            productId: product.id,
                            productTitle: product.title,
                            productImage: product.image,
                            subtotal: product.price,
                            tax: Utils.calculateTax(product.price),
                            totalAmount: Utils.calculatePriceWithTax(product.price),
                            paymentMethod: 'credit_card',
                            status: 'completed',
                            downloadUrl: product.downloadUrl,
                            instant: product.instant
                        });

                        // Add demo reviews for various products
                        setTimeout(() => {
                            const existingReviews = storage.getReviews();
                            if (existingReviews.length === 0) {
                                // Add review for the purchased product
                                storage.addReview({
                                    userId: demoUser2.id,
                                    userName: demoUser2.name,
                                    productId: product.id,
                                    purchaseId: purchase.id,
                                    rating: 5,
                                    reviewText: "Absolutely fantastic! The Robux were delivered instantly and the process was super smooth. Highly recommend this service to anyone looking for quick and reliable top-ups.",
                                    pros: "Instant delivery, great customer service, fair pricing",
                                    cons: null,
                                    purchaseDate: purchase.purchaseDate
                                });

                                // Add some additional sample reviews for other products
                                const sampleReviews = [
                                    {
                                        userName: "GamerPro123",
                                        productId: products[1]?.id,
                                        rating: 4,
                                        reviewText: "Great value for money! The Steam wallet code worked perfectly and was delivered within minutes.",
                                        pros: "Fast delivery, reliable service",
                                        cons: "Could be slightly cheaper"
                                    },
                                    {
                                        userName: "MobileGamer",
                                        productId: products[2]?.id,
                                        rating: 5,
                                        reviewText: "Perfect for Mobile Legends! Got my diamonds instantly and was able to buy the new skin I wanted.",
                                        pros: "Instant delivery, works perfectly",
                                        cons: null
                                    },
                                    {
                                        userName: "StreamingFan",
                                        productId: products[4]?.id,
                                        rating: 4,
                                        reviewText: "Netflix account works great! Enjoying 4K streaming on all my devices.",
                                        pros: "High quality streaming, multiple device support",
                                        cons: "Wish it was a longer subscription period"
                                    }
                                ];

                                sampleReviews.forEach((review, index) => {
                                    if (review.productId) {
                                        setTimeout(() => {
                                            storage.addReview({
                                                userId: `demo_user_${index + 3}`,
                                                userName: review.userName,
                                                productId: review.productId,
                                                purchaseId: `demo_purchase_${index + 3}`,
                                                rating: review.rating,
                                                reviewText: review.reviewText,
                                                pros: review.pros,
                                                cons: review.cons,
                                                purchaseDate: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000).toISOString()
                                            });
                                        }, (index + 1) * 50);
                                    }
                                });
                            }
                        }, 100);
                    }
                }
            }
        }

        function loginWithDemo(email, password) {
            // Simulate form submission
            const fakeEvent = {
                target: {
                    querySelector: () => null
                }
            };

            // Create fake form data
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);

            // Override the form data getter
            fakeEvent.target = {
                querySelector: () => null,
                get: (name) => formData.get(name)
            };

            // Manually call login with the demo credentials
            const user = storage.getUserByEmail(email);
            if (user && storage.verifyPassword(password, user.passwordHash)) {
                authManager.currentUser = user;
                storage.setCurrentUser(user);
                authManager.updateUI();
                cartManager.updateCartUI();
                
                Components.createNotification(`Logged in as ${user.name}!`, 'success');
                
                // Redirect to main site after 2 seconds
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                Components.createNotification('Demo account not found. Please refresh the page.', 'error');
            }
        }
    </script>
</body>
</html>
