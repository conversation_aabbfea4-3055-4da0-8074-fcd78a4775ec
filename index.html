<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obu Marketplace - Digital Gaming & Products</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-dark-primary font-inter overflow-x-hidden">
    <!-- Navigation -->
    <nav class="nav-glass sticky top-0 z-50 backdrop-blur-xl border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-3xl font-black bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent glow-text">
                            Obu Marketplace
                        </h1>
                    </div>
                    <div class="hidden md:ml-8 md:flex md:space-x-1">
                        <a href="#" class="nav-link text-white/90 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Home</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Categories</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Sell</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Support</a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-6">
                    <div class="relative">
                        <input type="text" placeholder="Search for games, items, accounts..."
                               class="search-input w-full pl-12 pr-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                        <i class="fas fa-search absolute left-4 top-4 text-white/50"></i>
                        <div class="search-glow"></div>
                    </div>
                </div>

                <!-- User Actions -->
                <div class="flex items-center space-x-4">
                    <button class="cart-btn relative p-3 text-white/70 hover:text-white transition-all duration-300 hover:scale-110">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <div class="cart-glow"></div>
                    </button>
                    <button id="loginBtn" class="btn-primary px-6 py-3 rounded-xl font-semibold transition-all duration-300">
                        Login
                    </button>
                    <button id="registerBtn" class="btn-secondary px-6 py-3 rounded-xl font-semibold transition-all duration-300">
                        Register
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0">
            <div class="hero-bg"></div>
            <div class="floating-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="hero-content">
                <h1 class="hero-title text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight">
                   <br>
                    <span class="text-white glow-text-white">
                        PAPAM's <br>
                        Digital Marketplace
                    </span>
                </h1>
                <p class="hero-subtitle text-xl md:text-2xl lg:text-3xl mb-12 text-white/80 max-w-4xl mx-auto leading-relaxed">
                    Discover and purchase premium gaming items, accounts, and digital products with confidence in our
                    <span class="text-cyan-400 font-semibold">secure marketplace</span>
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <button onclick="document.querySelector('.products-section').scrollIntoView({behavior: 'smooth'})" class="btn-hero-primary group px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105">
                        <span class="relative z-10">Start Shopping</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button onclick="authManager.showRegisterModal()" class="btn-hero-secondary group px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105">
                        <span class="relative z-10">Join Now</span>
                        <div class="btn-glow-secondary"></div>
                    </button>
                </div>

                <!-- Stats -->
                <div class="hero-stats grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto">
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-cyan-400 glow-text">25K+</div>
                        <div class="text-white/60 text-sm">Happy Buyers</div>
                    </div>
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-purple-400 glow-text">100K+</div>
                        <div class="text-white/60 text-sm">Products Sold</div>
                    </div>
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-pink-400 glow-text">99.9%</div>
                        <div class="text-white/60 text-sm">Satisfaction</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Features -->
    <section class="features-section py-24 bg-dark-secondary/30 relative">
        <div class="absolute inset-0 bg-gradient-to-b from-dark-primary via-dark-secondary/20 to-dark-primary"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-black text-white mb-6">
                    Why Choose
                    <span class="bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">Obu Marketplace</span>?
                </h2>
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                    Experience the safest and most reliable platform for digital transactions with cutting-edge technology
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-cyan-400 to-blue-500">
                            <i class="fas fa-shield-alt text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300">
                        Secure Transactions
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Military-grade encryption and advanced security measures protect every transaction with buyer protection guarantee
                    </p>
                </div>

                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-purple-400 to-pink-500">
                            <i class="fas fa-bolt text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-purple-400 transition-colors duration-300">
                        Instant Delivery
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Lightning-fast automated delivery system ensures you get your digital products within seconds of payment
                    </p>
                </div>

                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-green-400 to-emerald-500">
                            <i class="fas fa-headset text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-green-400 transition-colors duration-300">
                        24/7 Premium Support
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Dedicated premium support team available around the clock with average response time under 2 minutes
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products -->
    <section class="products-section py-24 bg-dark-primary relative">
        <div class="absolute inset-0 bg-gradient-to-b from-dark-secondary/20 via-transparent to-dark-secondary/20"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-black text-white mb-6">
                    Popular
                    <span class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Products</span>
                </h2>
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                    Discover the most popular digital products and services in our marketplace
                </p>
            </div>

            <!-- Category Filter -->
            <div class="flex justify-center mb-12">
                <div class="flex flex-wrap gap-3" id="categoryFilter">
                    <!-- Category filters will be populated here -->
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8" id="mainProductsGrid">
                <!-- Products will be populated by JavaScript -->
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button id="loadMoreBtn" class="btn-primary px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105" style="display: none;">
                    <i class="fas fa-plus mr-2"></i>
                    Load More Products
                </button>
            </div>
        </div>
    </section>

    <!-- Modals will be dynamically created by JavaScript -->



    <!-- JavaScript Modules -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/ProductManager.js"></script>
    <script src="js/managers/CategoryManager.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/managers/CartManager.js"></script>
    <script src="js/managers/ReviewManager.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
