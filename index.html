<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obu Marketplace - Digital Gaming & Products</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-dark-primary font-inter overflow-x-hidden">
    <!-- Navigation -->
    <nav class="nav-glass sticky top-0 z-50 backdrop-blur-xl border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-3xl font-black bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent glow-text">
                            Obu Marketplace
                        </h1>
                    </div>
                    <div class="hidden md:ml-8 md:flex md:space-x-1">
                        <a href="#" class="nav-link text-white/90 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Home</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Categories</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Sell</a>
                        <a href="#" class="nav-link text-white/70 hover:text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300">Support</a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-6">
                    <div class="relative">
                        <input type="text" placeholder="Search for games, items, accounts..."
                               class="search-input w-full pl-12 pr-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                        <i class="fas fa-search absolute left-4 top-4 text-white/50"></i>
                        <div class="search-glow"></div>
                    </div>
                </div>

                <!-- User Actions -->
                <div class="flex items-center space-x-4">
                    <button class="cart-btn relative p-3 text-white/70 hover:text-white transition-all duration-300 hover:scale-110">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <div class="cart-glow"></div>
                    </button>
                    <button id="loginBtn" class="btn-primary px-6 py-3 rounded-xl font-semibold transition-all duration-300">
                        Login
                    </button>
                    <button id="registerBtn" class="btn-secondary px-6 py-3 rounded-xl font-semibold transition-all duration-300">
                        Register
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0">
            <div class="hero-bg"></div>
            <div class="floating-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="hero-content">
                <h1 class="hero-title text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight">
                   <br>
                    <span class="text-white glow-text-white">
                        Digital Marketplace
                    </span>
                </h1>
                <p class="hero-subtitle text-xl md:text-2xl lg:text-3xl mb-12 text-white/80 max-w-4xl mx-auto leading-relaxed">
                    Buy and sell gaming items, accounts, and digital products with confidence in our
                    <span class="text-cyan-400 font-semibold">premium ecosystem</span>
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <button class="btn-hero-primary group px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105">
                        <span class="relative z-10">Start Shopping</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button class="btn-hero-secondary group px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105">
                        <span class="relative z-10">Become a Seller</span>
                        <div class="btn-glow-secondary"></div>
                    </button>
                </div>

                <!-- Stats -->
                <div class="hero-stats grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto">
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-cyan-400 glow-text">10K+</div>
                        <div class="text-white/60 text-sm">Active Users</div>
                    </div>
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-purple-400 glow-text">50K+</div>
                        <div class="text-white/60 text-sm">Products</div>
                    </div>
                    <div class="stat-item text-center">
                        <div class="text-3xl font-black text-pink-400 glow-text">99.9%</div>
                        <div class="text-white/60 text-sm">Uptime</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Features -->
    <section class="features-section py-24 bg-dark-secondary/30 relative">
        <div class="absolute inset-0 bg-gradient-to-b from-dark-primary via-dark-secondary/20 to-dark-primary"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-black text-white mb-6">
                    Why Choose
                    <span class="bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">Obu Marketplace</span>?
                </h2>
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                    Experience the safest and most reliable platform for digital transactions with cutting-edge technology
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-cyan-400 to-blue-500">
                            <i class="fas fa-shield-alt text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300">
                        Secure Transactions
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Military-grade encryption and advanced security measures protect every transaction with buyer protection guarantee
                    </p>
                </div>

                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-purple-400 to-pink-500">
                            <i class="fas fa-bolt text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-purple-400 transition-colors duration-300">
                        Instant Delivery
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Lightning-fast automated delivery system ensures you get your digital products within seconds of payment
                    </p>
                </div>

                <div class="feature-card group">
                    <div class="feature-icon-wrapper">
                        <div class="feature-icon bg-gradient-to-br from-green-400 to-emerald-500">
                            <i class="fas fa-headset text-2xl"></i>
                        </div>
                        <div class="feature-glow"></div>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-green-400 transition-colors duration-300">
                        24/7 Premium Support
                    </h3>
                    <p class="text-white/70 leading-relaxed">
                        Dedicated premium support team available around the clock with average response time under 2 minutes
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories -->
    <section class="categories-section py-24 bg-dark-primary relative">
        <div class="absolute inset-0 bg-gradient-to-b from-dark-secondary/20 via-transparent to-dark-secondary/20"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-black text-white mb-6">
                    Popular
                    <span class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Categories</span>
                </h2>
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                    Discover thousands of premium digital products across various gaming and entertainment categories
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6" id="categoriesGrid">
                <!-- Categories will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-card bg-dark-secondary/90 backdrop-blur-xl border border-white/10 rounded-2xl max-w-md w-full p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-white">Login to Your Account</h3>
                    <button id="closeLoginModal" class="text-white/60 hover:text-white transition-colors duration-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="loginForm">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-white/80 mb-3">Email</label>
                        <input type="email" required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>

                    <div class="mb-8">
                        <label class="block text-sm font-medium text-white/80 mb-3">Password</label>
                        <input type="password" required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>

                    <button type="submit" class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300">
                        Login
                    </button>
                </form>

                <p class="text-center mt-6 text-sm text-white/60">
                    Don't have an account?
                    <button id="switchToRegister" class="text-cyan-400 hover:text-cyan-300 font-medium transition-colors duration-200">Register here</button>
                </p>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-card bg-dark-secondary/90 backdrop-blur-xl border border-white/10 rounded-2xl max-w-md w-full p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-white">Create Your Account</h3>
                    <button id="closeRegisterModal" class="text-white/60 hover:text-white transition-colors duration-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="registerForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/80 mb-3">Full Name</label>
                        <input type="text" required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/80 mb-3">Email</label>
                        <input type="email" required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/80 mb-3">Account Type</label>
                        <select required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            <option value="" class="bg-dark-primary text-white">Select account type</option>
                            <option value="buyer" class="bg-dark-primary text-white">Buyer</option>
                            <option value="seller" class="bg-dark-primary text-white">Seller</option>
                            <option value="both" class="bg-dark-primary text-white">Both Buyer & Seller</option>
                        </select>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-white/80 mb-3">Password</label>
                        <input type="password" required class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>

                    <div class="mb-8">
                        <label class="flex items-start">
                            <input type="checkbox" required class="mt-1 mr-3 accent-cyan-400">
                            <span class="text-sm text-white/70 leading-relaxed">I agree to the <span class="text-cyan-400 font-medium">10% transaction fee</span> and terms of service</span>
                        </label>
                    </div>

                    <button type="submit" class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300">
                        Create Account
                    </button>
                </form>

                <p class="text-center mt-6 text-sm text-white/60">
                    Already have an account?
                    <button id="switchToLogin" class="text-cyan-400 hover:text-cyan-300 font-medium transition-colors duration-200">Login here</button>
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/ProductManager.js"></script>
    <script src="js/managers/CategoryManager.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/managers/CartManager.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
