// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.currentProductPage = 1;
        this.productsPerPage = 10;
        this.productFilter = '';
        this.categoryFilter = '';
    }

    // Initialize admin panel
    init() {
        this.updateStats();
        this.renderProductsTable();
        this.renderCategoriesTable();
        this.setupEventListeners();
        this.populateCategoryFilter();
    }

    // Setup event listeners
    setupEventListeners() {
        // Product search
        const productSearch = document.getElementById('productSearch');
        if (productSearch) {
            productSearch.addEventListener('input', Utils.debounce((e) => {
                this.productFilter = e.target.value;
                this.currentProductPage = 1;
                this.renderProductsTable();
            }, 300));
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.categoryFilter = e.target.value;
                this.currentProductPage = 1;
                this.renderProductsTable();
            });
        }
    }

    // Update statistics
    updateStats() {
        const stats = app.getStats();
        
        document.getElementById('totalProducts').textContent = stats.products;
        document.getElementById('totalCategories').textContent = stats.categories;
        document.getElementById('totalUsers').textContent = stats.users;
        document.getElementById('totalOrders').textContent = stats.purchases;
    }

    // Populate category filter dropdown
    populateCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        const categories = storage.getCategories();
        
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = `${category.name} (${category.count})`;
            categoryFilter.appendChild(option);
        });
    }

    // Render products table
    renderProductsTable() {
        let products = storage.getProducts();

        // Apply filters
        if (this.productFilter) {
            products = storage.searchProducts(this.productFilter);
        }

        if (this.categoryFilter) {
            products = products.filter(product => product.category === this.categoryFilter);
        }

        // Sort by newest first
        products = Utils.sortBy(products, 'createdAt', 'desc');

        // Calculate pagination
        const totalPages = Math.ceil(products.length / this.productsPerPage);
        const startIndex = (this.currentProductPage - 1) * this.productsPerPage;
        const endIndex = startIndex + this.productsPerPage;
        const paginatedProducts = products.slice(startIndex, endIndex);

        const tableHTML = `
            <table class="w-full">
                <thead>
                    <tr class="border-b border-white/10">
                        <th class="text-left py-3 text-white/80 font-semibold">Product</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Category</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Price</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Stock</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Status</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${paginatedProducts.length === 0 ? `
                        <tr>
                            <td colspan="6" class="text-center py-8 text-white/60">
                                No products found
                            </td>
                        </tr>
                    ` : paginatedProducts.map(product => `
                        <tr class="border-b border-white/5 hover:bg-white/5 transition-colors">
                            <td class="py-4">
                                <div class="flex items-center">
                                    <img src="${product.image || Utils.createImagePlaceholder(40, 40)}" 
                                         alt="${product.title}" 
                                         class="w-10 h-10 rounded-lg mr-3 object-cover">
                                    <div>
                                        <div class="font-medium text-white">${Utils.truncateText(product.title, 30)}</div>
                                        <div class="text-sm text-white/60">${product.sellerName}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="py-4 text-white/80">${product.category}</td>
                            <td class="py-4">
                                <div class="text-white font-semibold">${Utils.formatPrice(product.price)}</div>
                                ${product.originalPrice && product.originalPrice > product.price ? 
                                    `<div class="text-sm text-white/50 line-through">${Utils.formatPrice(product.originalPrice)}</div>` : ''}
                            </td>
                            <td class="py-4">
                                <span class="px-2 py-1 rounded-full text-xs font-semibold ${
                                    product.stock > 10 ? 'bg-green-400/20 text-green-400' :
                                    product.stock > 0 ? 'bg-yellow-400/20 text-yellow-400' :
                                    'bg-red-400/20 text-red-400'
                                }">
                                    ${product.stock} in stock
                                </span>
                            </td>
                            <td class="py-4">
                                <div class="flex items-center space-x-2">
                                    ${product.featured ? '<span class="px-2 py-1 bg-purple-400/20 text-purple-400 rounded-full text-xs font-semibold">Featured</span>' : ''}
                                    ${product.instant ? '<span class="px-2 py-1 bg-green-400/20 text-green-400 rounded-full text-xs font-semibold">Instant</span>' : ''}
                                </div>
                            </td>
                            <td class="py-4">
                                <div class="flex space-x-2">
                                    <button onclick="adminPanel.editProduct(${product.id})" 
                                            class="text-cyan-400 hover:text-cyan-300 transition-colors" 
                                            title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="adminPanel.toggleProductFeatured(${product.id})" 
                                            class="text-purple-400 hover:text-purple-300 transition-colors" 
                                            title="Toggle Featured">
                                        <i class="fas fa-star"></i>
                                    </button>
                                    <button onclick="adminPanel.deleteProduct(${product.id})" 
                                            class="text-red-400 hover:text-red-300 transition-colors" 
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            ${totalPages > 1 ? `
                <div class="flex items-center justify-between mt-6">
                    <div class="text-white/60 text-sm">
                        Showing ${startIndex + 1}-${Math.min(endIndex, products.length)} of ${products.length} products
                    </div>
                    ${Components.createPagination(this.currentProductPage, totalPages, 'adminPanel.goToProductPage')}
                </div>
            ` : ''}
        `;

        document.getElementById('productsTable').innerHTML = tableHTML;
    }

    // Render categories table
    renderCategoriesTable() {
        const categories = storage.getCategories();

        const tableHTML = `
            <table class="w-full">
                <thead>
                    <tr class="border-b border-white/10">
                        <th class="text-left py-3 text-white/80 font-semibold">Category</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Products</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Description</th>
                        <th class="text-left py-3 text-white/80 font-semibold">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${categories.length === 0 ? `
                        <tr>
                            <td colspan="4" class="text-center py-8 text-white/60">
                                No categories found
                            </td>
                        </tr>
                    ` : categories.map(category => `
                        <tr class="border-b border-white/5 hover:bg-white/5 transition-colors">
                            <td class="py-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br ${category.gradient} rounded-lg flex items-center justify-center mr-3">
                                        <i class="${category.icon} text-white"></i>
                                    </div>
                                    <div class="font-medium text-white">${category.name}</div>
                                </div>
                            </td>
                            <td class="py-4">
                                <span class="px-3 py-1 bg-cyan-400/20 text-cyan-400 rounded-full text-sm font-semibold">
                                    ${category.count} products
                                </span>
                            </td>
                            <td class="py-4 text-white/80">${Utils.truncateText(category.description || 'No description', 50)}</td>
                            <td class="py-4">
                                <div class="flex space-x-2">
                                    <button onclick="categoryManager.showCategoryModal('${category.id}')" 
                                            class="text-cyan-400 hover:text-cyan-300 transition-colors" 
                                            title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="adminPanel.deleteCategory('${category.id}')" 
                                            class="text-red-400 hover:text-red-300 transition-colors" 
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        document.getElementById('categoriesTable').innerHTML = tableHTML;
    }

    // Product pagination
    goToProductPage(page) {
        this.currentProductPage = page;
        this.renderProductsTable();
    }

    // Edit product
    editProduct(productId) {
        const product = storage.getProductById(productId);
        if (!product) return;

        // Show edit product modal (similar to add product but with existing data)
        this.showEditProductModal(product);
    }

    // Show edit product modal
    showEditProductModal(product) {
        const categories = storage.getCategories();
        
        const modalContent = `
            <form id="editProductForm" class="space-y-4">
                <input type="hidden" name="productId" value="${product.id}">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Product Title</label>
                        <input type="text" name="title" value="${product.title}" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Category</label>
                        <select name="category" required 
                                class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            ${categories.map(cat => `<option value="${cat.name}" ${cat.name === product.category ? 'selected' : ''}>${cat.name}</option>`).join('')}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Price ($)</label>
                        <input type="number" name="price" step="0.01" min="0.01" value="${product.price}" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Original Price ($)</label>
                        <input type="number" name="originalPrice" step="0.01" min="0" value="${product.originalPrice || ''}" 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Stock Quantity</label>
                        <input type="number" name="stock" min="0" value="${product.stock}" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Description</label>
                        <textarea name="description" rows="3" required 
                                  class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">${product.description}</textarea>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Image URL</label>
                        <input type="url" name="image" value="${product.image || ''}" 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div class="md:col-span-2 space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="instant" ${product.instant ? 'checked' : ''} class="mr-3 accent-cyan-400">
                            <span class="text-sm text-white/80">Instant delivery</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" name="featured" ${product.featured ? 'checked' : ''} class="mr-3 accent-cyan-400">
                            <span class="text-sm text-white/80">Feature this product</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex space-x-4 pt-4">
                    <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        Update Product
                    </button>
                    <button type="button" onclick="document.getElementById('editProductModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Cancel
                    </button>
                </div>
            </form>
        `;

        // Create modal
        let modal = document.getElementById('editProductModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'editProductModal';
            modal.innerHTML = Components.createModal('editProductModal', 'Edit Product', modalContent);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('.modal-content').innerHTML = modalContent;
        }

        // Setup form handler
        document.getElementById('editProductForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleEditProduct(e);
        });

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
    }

    // Handle edit product form submission
    handleEditProduct(event) {
        const formData = new FormData(event.target);
        const productId = parseInt(formData.get('productId'));
        
        const updates = {
            title: formData.get('title').trim(),
            description: formData.get('description').trim(),
            category: formData.get('category'),
            price: parseFloat(formData.get('price')),
            originalPrice: formData.get('originalPrice') ? parseFloat(formData.get('originalPrice')) : null,
            stock: parseInt(formData.get('stock')),
            image: formData.get('image').trim() || null,
            instant: formData.get('instant') === 'on',
            featured: formData.get('featured') === 'on'
        };

        // Update product
        storage.updateProduct(productId, updates);
        
        // Close modal
        document.getElementById('editProductModal').classList.add('hidden');
        
        // Refresh UI
        this.renderProductsTable();
        this.updateStats();
        this.populateCategoryFilter();
        
        Components.createNotification('Product updated successfully!', 'success');
    }

    // Toggle product featured status
    toggleProductFeatured(productId) {
        const product = storage.getProductById(productId);
        if (product) {
            storage.updateProduct(productId, { featured: !product.featured });
            this.renderProductsTable();
            Components.createNotification(
                `Product ${product.featured ? 'removed from' : 'added to'} featured list`, 
                'success'
            );
        }
    }

    // Delete product
    deleteProduct(productId) {
        const product = storage.getProductById(productId);
        if (!product) return;

        if (confirm(`Are you sure you want to delete "${product.title}"?`)) {
            storage.deleteProduct(productId);
            this.renderProductsTable();
            this.renderCategoriesTable();
            this.updateStats();
            this.populateCategoryFilter();
            Components.createNotification('Product deleted successfully!', 'success');
        }
    }

    // Delete category
    deleteCategory(categoryId) {
        if (categoryManager.deleteCategory(categoryId)) {
            this.renderCategoriesTable();
            this.renderProductsTable();
            this.updateStats();
            this.populateCategoryFilter();
        }
    }
}

// Global functions for admin panel
function showDataManagement() {
    document.getElementById('dataModal').classList.remove('hidden');
}

function showAddCategoryModal() {
    categoryManager.showCategoryModal();
}

function handleImportCategories() {
    const fileInput = document.getElementById('importCategories');
    const file = fileInput.files[0];
    
    if (!file) {
        Components.createNotification('Please select a file to import', 'error');
        return;
    }
    
    categoryManager.importCategories(file);
    fileInput.value = '';
}

function clearAllData() {
    if (confirm('Are you sure you want to clear ALL data? This action cannot be undone!')) {
        if (confirm('This will delete all products, categories, users, and orders. Are you absolutely sure?')) {
            storage.clearAllData();
            adminPanel.updateStats();
            adminPanel.renderProductsTable();
            adminPanel.renderCategoriesTable();
            adminPanel.populateCategoryFilter();
            Components.createNotification('All data cleared successfully!', 'success');
            document.getElementById('dataModal').classList.add('hidden');
        }
    }
}

// Create global admin panel instance
const adminPanel = new AdminPanel();

// Initialize admin panel when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize the main app first
    app.init().then(() => {
        // Then initialize admin panel
        adminPanel.init();
    });
});
