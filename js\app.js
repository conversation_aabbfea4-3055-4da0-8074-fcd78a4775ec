// Main Application Controller
class App {
    constructor() {
        this.isInitialized = false;
        this.currentPage = 'home';
    }

    // Initialize the application
    async init() {
        if (this.isInitialized) return;

        try {
            // Show loading state
            this.showLoadingState();

            // Initialize storage and managers
            await this.initializeManagers();

            // Setup global event listeners
            this.setupGlobalEventListeners();

            // Setup animations and effects
            this.setupAnimationsAndEffects();

            // Render initial content
            this.renderInitialContent();

            // Handle URL parameters
            this.handleUrlParameters();

            // Mark as initialized
            this.isInitialized = true;

            // Hide loading state
            this.hideLoadingState();

            console.log('🚀 Obu Marketplace initialized successfully!');

        } catch (error) {
            console.error('❌ Error initializing application:', error);
            Components.createNotification('Error loading application. Please refresh the page.', 'error');
        }
    }

    // Show loading state
    showLoadingState() {
        document.body.style.opacity = '0';
        
        // Create loading overlay if it doesn't exist
        if (!document.getElementById('loadingOverlay')) {
            const overlay = Utils.createElement('div', 'fixed inset-0 bg-dark-primary flex items-center justify-center z-50');
            overlay.id = 'loadingOverlay';
            overlay.innerHTML = `
                <div class="text-center">
                    <div class="spinner w-12 h-12 border-4 border-white/20 border-t-cyan-400 rounded-full animate-spin mx-auto mb-4"></div>
                    <div class="text-white text-lg font-semibold">Loading Obu Marketplace...</div>
                    <div class="text-white/60 text-sm mt-2">Preparing your premium experience</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
    }

    // Hide loading state
    hideLoadingState() {
        setTimeout(() => {
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            document.body.style.opacity = '1';
            
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.opacity = '0';
                setTimeout(() => overlay.remove(), 500);
            }
        }, 1000);
    }

    // Initialize all managers
    async initializeManagers() {
        // Initialize in order of dependency
        authManager.init();
        categoryManager.init();
        productManager.init();
        cartManager.init();

        // Setup load more button event listener
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                productManager.loadMoreProducts();
            });
        }
    }

    // Setup global event listeners
    setupGlobalEventListeners() {
        // Handle escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            this.handleUrlParameters();
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            Components.createNotification('Connection restored', 'success');
        });

        window.addEventListener('offline', () => {
            Components.createNotification('You are offline. Some features may not work.', 'warning');
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // Refresh data when user returns to tab
                this.refreshData();
            }
        });

        // Global click handler for dynamic elements
        document.addEventListener('click', (e) => {
            this.handleGlobalClick(e);
        });

        // Handle form submissions globally
        document.addEventListener('submit', (e) => {
            this.handleGlobalSubmit(e);
        });
    }

    // Setup animations and effects
    setupAnimationsAndEffects() {
        // Parallax effect for hero section
        this.setupParallaxEffect();

        // Intersection observer for animations
        this.setupIntersectionObserver();

        // Smooth scrolling for anchor links
        this.setupSmoothScrolling();

        // Add hover effects to interactive elements
        this.setupHoverEffects();
    }

    // Setup parallax effect
    setupParallaxEffect() {
        let ticking = false;

        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            const heroContent = document.querySelector('.hero-content');

            if (heroSection && heroContent && scrolled < window.innerHeight) {
                requestAnimationFrame(() => {
                    heroContent.style.transform = `translateY(${scrolled * 0.3}px)`;
                });
            }

            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        });
    }

    // Setup intersection observer for animations
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements that should animate in
        setTimeout(() => {
            const elementsToObserve = document.querySelectorAll('.feature-card, .product-card');
            elementsToObserve.forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
                observer.observe(element);
            });
        }, 500);
    }

    // Setup smooth scrolling
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Setup hover effects
    setupHoverEffects() {
        // Add glow effects to buttons
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.5)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Add hover effects to navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // Render initial content
    renderInitialContent() {
        // Render categories
        categoryManager.renderCategories();

        // Render featured products
        productManager.renderFeaturedProducts();

        // Update cart UI
        cartManager.updateCartUI();
    }

    // Handle URL parameters
    handleUrlParameters() {
        const params = Utils.getUrlParams();

        // Handle category filter
        if (params.category) {
            productManager.filterByCategory(params.category);
        }

        // Handle search query
        if (params.search) {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = params.search;
                productManager.handleSearch(params.search);
            }
        }

        // Handle product view
        if (params.product) {
            const product = storage.getProductById(parseInt(params.product));
            if (product) {
                productManager.showProductModal(product);
            }
        }
    }

    // Handle global clicks
    handleGlobalClick(e) {
        // Close search suggestions when clicking outside
        if (!e.target.closest('.search-input') && !e.target.closest('.search-suggestions')) {
            productManager.hideSearchSuggestions();
        }

        // Handle dynamic button clicks
        if (e.target.matches('[data-action]')) {
            const action = e.target.dataset.action;
            const params = e.target.dataset.params ? JSON.parse(e.target.dataset.params) : {};
            this.handleAction(action, params, e.target);
        }
    }

    // Handle global form submissions
    handleGlobalSubmit(e) {
        // Add loading state to submit buttons
        const submitBtn = e.target.querySelector('button[type="submit"]');
        if (submitBtn && !submitBtn.disabled) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            submitBtn.disabled = true;

            // Reset after 5 seconds as fallback
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        }
    }

    // Handle dynamic actions
    handleAction(action, params, element) {
        switch (action) {
            case 'addToCart':
                ProductManager.addToCart(params.productId);
                break;
            case 'removeFromCart':
                cartManager.removeFromCart(params.productId);
                break;
            case 'showProduct':
                const product = storage.getProductById(params.productId);
                if (product) {
                    productManager.showProductModal(product);
                }
                break;
            case 'filterCategory':
                productManager.filterByCategory(params.category);
                break;
            case 'showCart':
                cartManager.showCart();
                break;
            case 'logout':
                authManager.logout();
                break;
            default:
                console.warn('Unknown action:', action);
        }
    }

    // Close all modals
    closeAllModals() {
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    // Refresh data
    refreshData() {
        // Refresh categories
        categoryManager.loadCategories();
        categoryManager.renderCategories();

        // Refresh cart
        cartManager.loadCart();
        cartManager.updateCartUI();

        // Refresh current user
        authManager.loadCurrentUser();
        authManager.updateUI();
    }

    // Add product (Admin only - not available to regular users)
    showAddProductModal() {
        Components.createNotification('Product management is handled by our admin team', 'info');
    }

    // Render add product modal
    renderAddProductModal() {
        const categories = storage.getCategories();
        
        const modalContent = `
            <form id="addProductForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Product Title</label>
                        <input type="text" name="title" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                               placeholder="Enter product title">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Category</label>
                        <select name="category" required 
                                class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            <option value="">Select category</option>
                            ${categories.map(cat => `<option value="${cat.name}">${cat.name}</option>`).join('')}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Price ($)</label>
                        <input type="number" name="price" step="0.01" min="0.01" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                               placeholder="0.00">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Original Price ($)</label>
                        <input type="number" name="originalPrice" step="0.01" min="0" 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                               placeholder="0.00 (optional)">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-2">Stock Quantity</label>
                        <input type="number" name="stock" min="0" value="1" required 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Description</label>
                        <textarea name="description" rows="3" required 
                                  class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                                  placeholder="Describe your product..."></textarea>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-white/80 mb-2">Image URL</label>
                        <input type="url" name="image" 
                               class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                               placeholder="https://example.com/image.jpg (optional)">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="instant" class="mr-3 accent-cyan-400">
                            <span class="text-sm text-white/80">Instant delivery</span>
                        </label>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="featured" class="mr-3 accent-cyan-400">
                            <span class="text-sm text-white/80">Feature this product</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex space-x-4 pt-4">
                    <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        Add Product
                    </button>
                    <button type="button" onclick="document.getElementById('addProductModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Cancel
                    </button>
                </div>
            </form>
        `;

        // Create modal
        let modal = document.getElementById('addProductModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'addProductModal';
            modal.innerHTML = Components.createModal('addProductModal', 'Add New Product', modalContent);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('.modal-content').innerHTML = modalContent;
        }

        // Setup form handler
        document.getElementById('addProductForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddProduct(e);
        });

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
    }

    // Handle add product form submission
    handleAddProduct(event) {
        const formData = new FormData(event.target);
        
        const productData = {
            title: formData.get('title').trim(),
            description: formData.get('description').trim(),
            category: formData.get('category'),
            price: parseFloat(formData.get('price')),
            originalPrice: formData.get('originalPrice') ? parseFloat(formData.get('originalPrice')) : null,
            stock: parseInt(formData.get('stock')),
            image: formData.get('image').trim() || null,
            instant: formData.get('instant') === 'on',
            featured: formData.get('featured') === 'on',
            sellerId: authManager.currentUser.id,
            sellerName: authManager.currentUser.name,
            rating: 0,
            reviews: 0,
            tags: productData.title.toLowerCase().split(' ').concat(productData.category.toLowerCase().split(' '))
        };

        // Validation
        if (!productData.title || !productData.description || !productData.category) {
            Components.createNotification('Please fill in all required fields', 'error');
            return;
        }

        if (!Utils.validatePrice(productData.price)) {
            Components.createNotification('Please enter a valid price', 'error');
            return;
        }

        if (productData.originalPrice && productData.originalPrice <= productData.price) {
            Components.createNotification('Original price must be higher than current price', 'error');
            return;
        }

        // Add product
        const newProduct = storage.addProduct(productData);
        
        // Close modal
        document.getElementById('addProductModal').classList.add('hidden');
        
        // Refresh UI
        this.refreshData();
        
        Components.createNotification('Product added successfully!', 'success');
    }

    // Get application statistics
    getStats() {
        return {
            users: storage.getUsers().length,
            products: storage.getProducts().length,
            categories: storage.getCategories().length,
            purchases: storage.getPurchases().length,
            storageSize: Utils.formatStorageSize(Utils.getStorageSize())
        };
    }

    // Export all data
    exportData() {
        const data = storage.exportData();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `obu-marketplace-backup-${Utils.formatDate(new Date().toISOString())}.json`;
        a.click();
        URL.revokeObjectURL(url);

        Components.createNotification('Data exported successfully!', 'success');
    }
}

// Create global app instance
const app = new App();

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// Make app globally available for debugging
window.ObuMarketplace = {
    app,
    storage,
    authManager,
    productManager,
    categoryManager,
    cartManager,
    Utils,
    Components,
    CONFIG
};
