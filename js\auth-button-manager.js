// Authentication Button Manager for Obu Marketplace
class AuthButtonManager {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        console.log('🔧 Setting up authentication button manager...');
        
        // Ensure buttons are properly visible and functional
        this.ensureButtonVisibility();
        this.setupEventListeners();
        this.addButtonStyles();
        
        // Listen for auth state changes
        document.addEventListener('authStateChanged', (e) => {
            this.handleAuthStateChange(e.detail);
        });

        this.isInitialized = true;
        console.log('✅ Authentication button manager initialized');
    }

    ensureButtonVisibility() {
        console.log('👁️ Ensuring button visibility...');
        
        // Desktop buttons
        const desktopAuthButtons = document.getElementById('desktopAuthButtons');
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');

        if (desktopAuthButtons) {
            // Ensure container is visible
            desktopAuthButtons.style.display = '';
            desktopAuthButtons.classList.remove('hidden');
            desktopAuthButtons.classList.add('hidden', 'md:flex', 'items-center', 'space-x-4');
            
            console.log('✅ Desktop auth container configured');
        }

        if (loginBtn) {
            loginBtn.style.display = 'inline-flex';
            loginBtn.style.visibility = 'visible';
            loginBtn.style.opacity = '1';
            console.log('✅ Login button visible');
        }

        if (registerBtn) {
            registerBtn.style.display = 'inline-flex';
            registerBtn.style.visibility = 'visible';
            registerBtn.style.opacity = '1';
            console.log('✅ Register button visible');
        }

        // Mobile buttons
        const mobileAuthSection = document.getElementById('mobileAuthSection');
        if (mobileAuthSection) {
            mobileAuthSection.style.display = 'block';
            console.log('✅ Mobile auth section visible');
        }

        // Force a repaint
        if (desktopAuthButtons) {
            desktopAuthButtons.offsetHeight;
        }
    }

    setupEventListeners() {
        console.log('🎧 Setting up button event listeners...');
        
        // Desktop login button
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            // Remove existing listeners
            loginBtn.onclick = null;
            
            // Add new listener
            loginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🔐 Login button clicked');
                if (typeof authManager !== 'undefined') {
                    authManager.showLoginModal();
                } else {
                    console.error('AuthManager not available');
                }
            });
            
            console.log('✅ Login button listener attached');
        }

        // Desktop register button
        const registerBtn = document.getElementById('registerBtn');
        if (registerBtn) {
            // Remove existing listeners
            registerBtn.onclick = null;
            
            // Add new listener
            registerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('📝 Register button clicked');
                if (typeof authManager !== 'undefined') {
                    authManager.showRegisterModal();
                } else {
                    console.error('AuthManager not available');
                }
            });
            
            console.log('✅ Register button listener attached');
        }

        // Mobile buttons are handled by the mobile menu manager
        console.log('✅ Event listeners configured');
    }

    addButtonStyles() {
        // Add any missing button styles
        if (document.getElementById('auth-button-styles')) return;

        const style = document.createElement('style');
        style.id = 'auth-button-styles';
        style.textContent = `
            /* Ensure auth buttons are always visible */
            #desktopAuthButtons {
                z-index: 10;
            }

            #loginBtn, #registerBtn {
                white-space: nowrap;
                user-select: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
            }

            #loginBtn:focus, #registerBtn:focus {
                outline: 2px solid rgba(0, 212, 255, 0.5);
                outline-offset: 2px;
            }

            /* Mobile auth section styling */
            #mobileAuthSection {
                width: 100%;
            }

            #mobileAuthSection button {
                width: 100%;
                text-align: center;
            }

            /* Responsive visibility fixes */
            @media (max-width: 767px) {
                #desktopAuthButtons {
                    display: none !important;
                }
            }

            @media (min-width: 768px) {
                #desktopAuthButtons {
                    display: flex !important;
                }
            }

            /* Button hover states */
            #loginBtn:hover, #registerBtn:hover {
                cursor: pointer;
            }

            /* Loading state */
            .btn-loading {
                opacity: 0.7;
                pointer-events: none;
            }

            .btn-loading::after {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                margin-left: 8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

        document.head.appendChild(style);
        console.log('✅ Button styles added');
    }

    handleAuthStateChange(detail) {
        console.log('🔄 Auth state changed:', detail);
        
        const { isLoggedIn, user } = detail;
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');

        if (isLoggedIn && user) {
            // User is logged in
            if (loginBtn) {
                loginBtn.innerHTML = `
                    <i class="fas fa-user mr-2"></i>
                    ${user.name}
                `;
                loginBtn.className = 'btn-primary px-6 py-3 rounded-xl font-semibold transition-all duration-300 user-menu-trigger';
                loginBtn.onclick = () => authManager.showUserMenu();
            }
            
            if (registerBtn) {
                registerBtn.style.display = 'none';
            }
        } else {
            // User is not logged in
            if (loginBtn) {
                loginBtn.innerHTML = `
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Login
                `;
                loginBtn.className = 'btn-primary px-6 py-3 rounded-xl font-semibold transition-all duration-300';
                loginBtn.onclick = () => authManager.showLoginModal();
            }
            
            if (registerBtn) {
                registerBtn.innerHTML = `
                    <i class="fas fa-user-plus mr-2"></i>
                    Register
                `;
                registerBtn.style.display = 'inline-flex';
                registerBtn.onclick = () => authManager.showRegisterModal();
            }
        }
    }

    // Force button visibility (useful for debugging)
    forceButtonVisibility() {
        console.log('🔧 Forcing button visibility...');
        
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');
        const desktopAuthButtons = document.getElementById('desktopAuthButtons');

        if (desktopAuthButtons) {
            desktopAuthButtons.style.display = 'flex';
            desktopAuthButtons.style.visibility = 'visible';
            desktopAuthButtons.style.opacity = '1';
        }

        if (loginBtn) {
            loginBtn.style.display = 'inline-flex';
            loginBtn.style.visibility = 'visible';
            loginBtn.style.opacity = '1';
            loginBtn.style.pointerEvents = 'auto';
        }

        if (registerBtn) {
            registerBtn.style.display = 'inline-flex';
            registerBtn.style.visibility = 'visible';
            registerBtn.style.opacity = '1';
            registerBtn.style.pointerEvents = 'auto';
        }

        console.log('✅ Button visibility forced');
    }

    // Test button functionality
    testButtons() {
        console.log('🧪 Testing button functionality...');
        
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');

        const results = {
            loginBtnExists: !!loginBtn,
            registerBtnExists: !!registerBtn,
            loginBtnVisible: loginBtn ? window.getComputedStyle(loginBtn).display !== 'none' : false,
            registerBtnVisible: registerBtn ? window.getComputedStyle(registerBtn).display !== 'none' : false,
            loginBtnClickable: loginBtn ? !!loginBtn.onclick || loginBtn.hasAttribute('onclick') : false,
            registerBtnClickable: registerBtn ? !!registerBtn.onclick || registerBtn.hasAttribute('onclick') : false
        };

        console.log('Button test results:', results);
        return results;
    }

    // Get button status
    getButtonStatus() {
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');
        const desktopAuthButtons = document.getElementById('desktopAuthButtons');

        return {
            initialized: this.isInitialized,
            desktopContainer: {
                exists: !!desktopAuthButtons,
                visible: desktopAuthButtons ? window.getComputedStyle(desktopAuthButtons).display !== 'none' : false,
                classes: desktopAuthButtons ? Array.from(desktopAuthButtons.classList) : []
            },
            loginButton: {
                exists: !!loginBtn,
                visible: loginBtn ? window.getComputedStyle(loginBtn).display !== 'none' : false,
                text: loginBtn ? loginBtn.textContent.trim() : '',
                hasClickHandler: loginBtn ? !!loginBtn.onclick : false
            },
            registerButton: {
                exists: !!registerBtn,
                visible: registerBtn ? window.getComputedStyle(registerBtn).display !== 'none' : false,
                text: registerBtn ? registerBtn.textContent.trim() : '',
                hasClickHandler: registerBtn ? !!registerBtn.onclick : false
            }
        };
    }
}

// Create global instance
const authButtonManager = new AuthButtonManager();

// Export for global access
window.authButtonManager = authButtonManager;

// Add global test functions
window.testAuthButtons = () => authButtonManager.testButtons();
window.forceButtonVisibility = () => authButtonManager.forceButtonVisibility();
window.getButtonStatus = () => authButtonManager.getButtonStatus();
