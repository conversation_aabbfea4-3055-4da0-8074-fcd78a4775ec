// Authentication Fix Script - Immediate Setup
(function() {
    'use strict';

    console.log('🔧 Authentication Fix Script Loading...');

    // Wait for DOM and other scripts to load
    function initAuthFix() {
        console.log('🔧 Initializing Authentication Fix...');

        // Ensure test accounts exist
        createTestAccountsIfNeeded();

        // Add debug functions to window
        setupDebugFunctions();

        // Test authentication system
        setTimeout(() => {
            runAuthenticationDiagnostics();
        }, 1000);
    }

    // Create test accounts if they don't exist
    function createTestAccountsIfNeeded() {
        if (typeof storage === 'undefined') {
            console.error('❌ Storage not available');
            return;
        }

        const testAccounts = [
            {
                name: 'Obu Admin',
                email: '<EMAIL>',
                password: 'try465'
            },
            {
                name: 'Obu User',
                email: '<EMAIL>',
                password: 'try465'
            }
        ];

        console.log('👥 Checking/Creating Test Accounts...');

        testAccounts.forEach(account => {
            const existingUser = storage.getUserByEmail(account.email);
            if (!existingUser) {
                console.log(`Creating account: ${account.email}`);
                try {
                    const user = storage.addUser({
                        name: account.name,
                        email: account.email,
                        password: account.password,
                        accountType: 'buyer'
                    });
                    console.log(`✅ Created: ${account.email} (ID: ${user.id})`);
                } catch (error) {
                    console.error(`❌ Failed to create ${account.email}:`, error);
                }
            } else {
                console.log(`✅ Exists: ${account.email} (ID: ${existingUser.id})`);
            }
        });
    }

    // Setup debug functions
    function setupDebugFunctions() {
        // Quick login functions
        window.quickLoginAdmin = function() {
            return quickLogin('<EMAIL>', 'try465');
        };

        window.quickLoginUser = function() {
            return quickLogin('<EMAIL>', 'try465');
        };

        window.debugAuth = function() {
            return runAuthenticationDiagnostics();
        };

        window.fixAuth = function() {
            return fixAuthenticationIssues();
        };

        console.log('🔧 Debug functions added:');
        console.log('  - window.quickLoginAdmin()');
        console.log('  - window.quickLoginUser()');
        console.log('  - window.debugAuth()');
        console.log('  - window.fixAuth()');
    }

    // Quick login function
    function quickLogin(email, password) {
        console.log(`🔐 Quick login attempt: ${email}`);

        if (typeof authManager === 'undefined') {
            console.error('❌ AuthManager not available');
            return false;
        }

        if (typeof storage === 'undefined') {
            console.error('❌ Storage not available');
            return false;
        }

        try {
            // Logout first
            if (authManager.isLoggedIn()) {
                authManager.logout();
            }

            // Get user
            const user = storage.getUserByEmail(email);
            if (!user) {
                console.error(`❌ User not found: ${email}`);
                return false;
            }

            // Verify password
            const isValid = storage.verifyPassword(password, user.passwordHash);
            console.log(`Password verification: ${isValid ? '✅' : '❌'}`);

            if (!isValid) {
                console.error('❌ Invalid password');
                return false;
            }

            // Set current user
            authManager.currentUser = user;
            storage.setCurrentUser(user);
            authManager.updateUI();

            console.log(`✅ Login successful: ${user.name}`);
            
            // Show notification if Components is available
            if (typeof Components !== 'undefined') {
                Components.createNotification(`Welcome back, ${user.name}!`, 'success');
            }

            return true;

        } catch (error) {
            console.error('❌ Login error:', error);
            return false;
        }
    }

    // Run authentication diagnostics
    function runAuthenticationDiagnostics() {
        console.log('🔍 Running Authentication Diagnostics...');
        console.log('='.repeat(50));

        const results = {
            storage: typeof storage !== 'undefined',
            authManager: typeof authManager !== 'undefined',
            components: typeof Components !== 'undefined',
            utils: typeof Utils !== 'undefined'
        };

        console.log('📊 System Components:');
        Object.entries(results).forEach(([key, value]) => {
            console.log(`  ${key}: ${value ? '✅' : '❌'}`);
        });

        if (results.storage && results.authManager) {
            console.log('\n👥 Test Accounts:');
            const adminUser = storage.getUserByEmail('<EMAIL>');
            const regularUser = storage.getUserByEmail('<EMAIL>');
            
            console.log(`  Admin: ${adminUser ? '✅' : '❌'} (${adminUser?.email || 'Not found'})`);
            console.log(`  User: ${regularUser ? '✅' : '❌'} (${regularUser?.email || 'Not found'})`);

            if (adminUser) {
                const adminPasswordTest = storage.verifyPassword('try465', adminUser.passwordHash);
                console.log(`  Admin password test: ${adminPasswordTest ? '✅' : '❌'}`);
            }

            if (regularUser) {
                const userPasswordTest = storage.verifyPassword('try465', regularUser.passwordHash);
                console.log(`  User password test: ${userPasswordTest ? '✅' : '❌'}`);
            }

            console.log('\n🔐 Current Authentication State:');
            console.log(`  Logged in: ${authManager.isLoggedIn() ? '✅' : '❌'}`);
            console.log(`  Current user: ${authManager.currentUser?.name || 'None'}`);
        }

        console.log('\n💡 Quick Test Commands:');
        console.log('  window.quickLoginAdmin() - Login as admin');
        console.log('  window.quickLoginUser() - Login as user');
        console.log('  window.fixAuth() - Fix authentication issues');

        return results;
    }

    // Fix authentication issues
    function fixAuthenticationIssues() {
        console.log('🔧 Fixing Authentication Issues...');

        // Recreate test accounts
        createTestAccountsIfNeeded();

        // Clear any corrupted session
        if (typeof storage !== 'undefined') {
            storage.clearCurrentUser();
        }

        // Reset auth manager
        if (typeof authManager !== 'undefined') {
            authManager.currentUser = null;
            authManager.updateUI();
        }

        console.log('✅ Authentication system reset');
        
        // Run diagnostics again
        setTimeout(() => {
            runAuthenticationDiagnostics();
        }, 500);

        return true;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAuthFix);
    } else {
        // DOM is already ready
        setTimeout(initAuthFix, 100);
    }

})();
