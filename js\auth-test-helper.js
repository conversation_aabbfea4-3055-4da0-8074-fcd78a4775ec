// Authentication Test Helper for Obu Marketplace
class AuthTestHelper {
    constructor() {
        this.testAccounts = {
            admin: {
                name: 'Obu Admin',
                email: '<EMAIL>',
                password: 'try465'
            },
            user: {
                name: 'Obu User',
                email: '<EMAIL>',
                password: 'try465'
            }
        };
    }

    // Quick test of authentication system
    async quickAuthTest() {
        console.log('🧪 Running Quick Authentication Test...');
        console.log('='.repeat(50));

        const results = {
            supabaseConnection: false,
            authManagerLoaded: false,
            socialAuthAvailable: false,
            testAccountsExist: false,
            paymentSystemReady: false
        };

        try {
            // Test Supabase connection
            if (typeof supabaseIntegration !== 'undefined') {
                results.supabaseConnection = supabaseIntegration.isConnected();
                console.log(`✅ Supabase Connection: ${results.supabaseConnection ? 'Connected' : 'Not Connected'}`);
            } else {
                console.log('❌ Supabase Integration: Not Loaded');
            }

            // Test AuthManager
            if (typeof authManager !== 'undefined') {
                results.authManagerLoaded = true;
                console.log('✅ AuthManager: Loaded');
                
                // Test social auth methods
                const hasGoogle = typeof authManager.signInWithGoogle === 'function';
                const hasApple = typeof authManager.signInWithApple === 'function';
                results.socialAuthAvailable = hasGoogle && hasApple;
                console.log(`✅ Social Auth: ${results.socialAuthAvailable ? 'Available' : 'Limited'} (Google: ${hasGoogle}, Apple: ${hasApple})`);
            } else {
                console.log('❌ AuthManager: Not Loaded');
            }

            // Test payment systems
            const hasPayMongo = typeof payMongoService !== 'undefined';
            const hasCurrency = typeof currencyManager !== 'undefined';
            results.paymentSystemReady = hasPayMongo && hasCurrency;
            console.log(`✅ Payment Systems: ${results.paymentSystemReady ? 'Ready' : 'Not Ready'} (PayMongo: ${hasPayMongo}, Currency: ${hasCurrency})`);

            // Test account existence (if Supabase is connected)
            if (results.supabaseConnection) {
                results.testAccountsExist = await this.checkTestAccounts();
                console.log(`✅ Test Accounts: ${results.testAccountsExist ? 'Exist' : 'Need Creation'}`);
            }

        } catch (error) {
            console.error('❌ Test Error:', error.message);
        }

        console.log('\n📊 Test Summary:');
        console.log(`Supabase: ${results.supabaseConnection ? '✅' : '❌'}`);
        console.log(`Auth System: ${results.authManagerLoaded ? '✅' : '❌'}`);
        console.log(`Social Login: ${results.socialAuthAvailable ? '✅' : '❌'}`);
        console.log(`Payment Ready: ${results.paymentSystemReady ? '✅' : '❌'}`);
        console.log(`Test Accounts: ${results.testAccountsExist ? '✅' : '❌'}`);

        return results;
    }

    // Check if test accounts exist
    async checkTestAccounts() {
        try {
            if (!supabaseIntegration.isConnected()) {
                return false;
            }

            const { data, error } = await supabaseIntegration.supabase
                .from('users')
                .select('email')
                .in('email', [this.testAccounts.admin.email, this.testAccounts.user.email]);

            if (error) {
                console.error('Error checking test accounts:', error);
                return false;
            }

            return data && data.length >= 2;
        } catch (error) {
            console.error('Test account check failed:', error);
            return false;
        }
    }

    // Create test accounts
    async createTestAccounts() {
        console.log('👥 Creating Test Accounts...');

        if (!storage) {
            console.error('❌ Storage not available');
            return false;
        }

        const results = [];

        for (const [type, account] of Object.entries(this.testAccounts)) {
            try {
                console.log(`Creating ${type} account: ${account.email}`);

                // Check if account already exists
                const existingUser = storage.getUserByEmail(account.email);
                if (existingUser) {
                    console.log(`✅ ${type} account already exists`);
                    results.push(true);
                    continue;
                }

                // Create account directly using storage
                const userData = {
                    name: account.name,
                    email: account.email,
                    password: account.password,
                    accountType: 'buyer'
                };

                const user = storage.addUser(userData);

                if (user) {
                    console.log(`✅ ${type} account created successfully`);
                    console.log(`   ID: ${user.id}`);
                    console.log(`   Email: ${user.email}`);
                    console.log(`   Password Hash: ${user.passwordHash}`);
                    results.push(true);
                } else {
                    console.log(`❌ Failed to create ${type} account`);
                    results.push(false);
                }

            } catch (error) {
                console.error(`❌ Error creating ${type} account:`, error);
                results.push(false);
            }
        }

        const success = results.every(r => r);
        console.log(`\n📊 Account Creation: ${success ? 'All accounts ready' : 'Some accounts failed'}`);

        // Verify accounts were created
        if (success) {
            console.log('\n🔍 Verifying created accounts:');
            for (const [type, account] of Object.entries(this.testAccounts)) {
                const user = storage.getUserByEmail(account.email);
                if (user) {
                    console.log(`✅ ${type}: ${user.email} (ID: ${user.id})`);
                } else {
                    console.log(`❌ ${type}: Not found`);
                }
            }
        }

        return success;
    }

    // Test login with test accounts
    async testLogin(accountType = 'user') {
        console.log(`🔐 Testing Login with ${accountType} account...`);

        const account = this.testAccounts[accountType];
        if (!account) {
            console.error('❌ Invalid account type');
            return false;
        }

        try {
            // Logout first if logged in
            if (authManager.isLoggedIn()) {
                authManager.logout();
            }

            // Check if account exists
            const user = storage.getUserByEmail(account.email);
            if (!user) {
                console.error(`❌ Account ${account.email} does not exist. Creating it...`);
                await this.createTestAccounts();
                const newUser = storage.getUserByEmail(account.email);
                if (!newUser) {
                    console.error(`❌ Failed to create account ${account.email}`);
                    return false;
                }
            }

            // Test password verification directly
            const passwordVerification = storage.verifyPassword(account.password, user.passwordHash);
            console.log(`Password verification test: ${passwordVerification ? '✅' : '❌'}`);

            if (!passwordVerification) {
                console.error('❌ Password verification failed in direct test');
                console.log('Debug info:', {
                    inputPassword: account.password,
                    inputHash: storage.simpleHash(account.password),
                    storedHash: user.passwordHash
                });
                return false;
            }

            // Simulate form submission
            const mockEvent = {
                target: {
                    elements: {
                        email: { value: account.email },
                        password: { value: account.password }
                    }
                }
            };

            // Create mock FormData
            const mockFormData = new FormData();
            mockFormData.set('email', account.email);
            mockFormData.set('password', account.password);

            mockEvent.target = {
                ...mockEvent.target,
                get: (name) => mockFormData.get(name)
            };

            // Attempt login
            await authManager.handleLogin(mockEvent);

            if (authManager.isLoggedIn()) {
                console.log(`✅ Login successful for ${account.email}`);
                console.log(`User: ${authManager.currentUser.name}`);
                console.log(`Account Type: ${authManager.currentUser.accountType}`);
                return true;
            } else {
                console.log(`❌ Login failed for ${account.email}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Login error for ${account.email}:`, error);
            return false;
        }
    }

    // Test complete user journey
    async testUserJourney() {
        console.log('🚀 Testing Complete User Journey...');
        console.log('='.repeat(50));

        const steps = [
            'Login',
            'Browse Products',
            'Add to Cart',
            'Currency Conversion',
            'Checkout Process',
            'Purchase History'
        ];

        const results = {};

        try {
            // Step 1: Login
            console.log('\n1️⃣ Testing Login...');
            results.login = await this.testLogin('user');

            // Step 2: Browse Products
            console.log('\n2️⃣ Testing Product Browsing...');
            const products = storage.getProducts();
            results.browseProducts = products && products.length > 0;
            console.log(`Products available: ${products?.length || 0}`);

            // Step 3: Add to Cart
            console.log('\n3️⃣ Testing Cart Functionality...');
            if (products && products.length > 0) {
                cartManager.addToCart(products[0].id);
                const cartItems = storage.getCartItems();
                results.addToCart = cartItems.length > 0;
                console.log(`Items in cart: ${cartItems.length}`);
            } else {
                results.addToCart = false;
            }

            // Step 4: Currency Conversion
            console.log('\n4️⃣ Testing Currency Conversion...');
            if (typeof currencyManager !== 'undefined') {
                const converted = currencyManager.convertAmount(100, 'USD', 'PHP');
                results.currencyConversion = converted > 0;
                console.log(`100 USD = ${converted} PHP`);
            } else {
                results.currencyConversion = false;
            }

            // Step 5: Checkout Process
            console.log('\n5️⃣ Testing Checkout Process...');
            const cartSummary = cartManager.getCartSummary();
            results.checkoutProcess = cartSummary && cartSummary.total > 0;
            console.log(`Cart total: ${cartManager.formatPrice(cartSummary?.total || 0)}`);

            // Step 6: Purchase History
            console.log('\n6️⃣ Testing Purchase History...');
            const purchases = storage.getPurchasesByUser(authManager.currentUser?.id);
            results.purchaseHistory = Array.isArray(purchases);
            console.log(`Purchase history entries: ${purchases?.length || 0}`);

        } catch (error) {
            console.error('❌ User journey test error:', error);
        }

        // Display results
        console.log('\n📊 User Journey Test Results:');
        steps.forEach((step, index) => {
            const key = step.toLowerCase().replace(/\s+/g, '');
            const passed = results[key] || false;
            console.log(`${index + 1}. ${step}: ${passed ? '✅' : '❌'}`);
        });

        const passedSteps = Object.values(results).filter(Boolean).length;
        const totalSteps = steps.length;
        console.log(`\nOverall: ${passedSteps}/${totalSteps} steps passed (${Math.round(passedSteps/totalSteps*100)}%)`);

        return results;
    }

    // Display test account information
    showTestAccounts() {
        console.log('👥 Test Accounts Information:');
        console.log('='.repeat(40));
        
        Object.entries(this.testAccounts).forEach(([type, account]) => {
            console.log(`\n${type.toUpperCase()} ACCOUNT:`);
            console.log(`Email: ${account.email}`);
            console.log(`Password: ${account.password}`);
            console.log(`Name: ${account.name}`);
        });

        console.log('\n📝 Usage Instructions:');
        console.log('1. Use these accounts to test login functionality');
        console.log('2. Both accounts have buyer-only permissions');
        console.log('3. Accounts work with both localStorage and Supabase');
        console.log('4. Test social login with your own Google/Apple accounts');
    }

    // Manual login test (bypasses form)
    async manualLogin(email, password) {
        console.log(`🔐 Manual login test for: ${email}`);

        try {
            // Check if user exists
            const user = storage.getUserByEmail(email);
            if (!user) {
                console.error(`❌ User not found: ${email}`);
                return false;
            }

            // Verify password
            const isValidPassword = storage.verifyPassword(password, user.passwordHash);
            console.log(`Password verification: ${isValidPassword ? '✅' : '❌'}`);

            if (!isValidPassword) {
                console.error('❌ Invalid password');
                return false;
            }

            // Set user as current user
            authManager.currentUser = user;
            storage.setCurrentUser(user);
            authManager.updateUI();

            console.log(`✅ Manual login successful for ${user.name}`);
            return true;

        } catch (error) {
            console.error('❌ Manual login error:', error);
            return false;
        }
    }

    // Get system status
    getSystemStatus() {
        return {
            authManager: typeof authManager !== 'undefined',
            supabaseIntegration: typeof supabaseIntegration !== 'undefined',
            payMongoService: typeof payMongoService !== 'undefined',
            currencyManager: typeof currencyManager !== 'undefined',
            currentUser: authManager?.currentUser || null,
            isLoggedIn: authManager?.isLoggedIn() || false,
            supabaseConnected: supabaseIntegration?.isConnected() || false,
            payMongoReady: payMongoService?.isAvailable() || false
        };
    }
}

// Create global instance
const authTestHelper = new AuthTestHelper();

// Add to window for console access
window.authTestHelper = authTestHelper;
window.quickAuthTest = () => authTestHelper.quickAuthTest();
window.createTestAccounts = () => authTestHelper.createTestAccounts();
window.testUserJourney = () => authTestHelper.testUserJourney();
window.showTestAccounts = () => authTestHelper.showTestAccounts();
window.testLogin = (accountType) => authTestHelper.testLogin(accountType);
window.manualLogin = (email, password) => authTestHelper.manualLogin(email, password);

// Quick test functions for specific accounts
window.testAdminLogin = () => authTestHelper.manualLogin('<EMAIL>', 'try465');
window.testUserLogin = () => authTestHelper.manualLogin('<EMAIL>', 'try465');

// Auto-display test accounts info when script loads
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('🧪 Auth Test Helper Loaded');
        console.log('Available commands:');
        console.log('- window.quickAuthTest() - Quick system test');
        console.log('- window.createTestAccounts() - Create test accounts');
        console.log('- window.testUserJourney() - Test complete user flow');
        console.log('- window.showTestAccounts() - Show test account info');
    }, 2000);
});
