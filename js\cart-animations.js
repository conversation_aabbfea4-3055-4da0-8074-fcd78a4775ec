// Cart Animation System for Obu Marketplace
class CartAnimations {
    constructor() {
        this.isAnimating = false;
        this.init();
    }

    init() {
        // Add CSS for animations if not already added
        this.addAnimationCSS();
        console.log('✅ Cart animations initialized');
    }

    // Add CSS for cart animations
    addAnimationCSS() {
        if (document.getElementById('cart-animation-styles')) return;

        const style = document.createElement('style');
        style.id = 'cart-animation-styles';
        style.textContent = `
            /* Cart Animation Styles */
            .cart-animation-item {
                position: fixed;
                pointer-events: none;
                z-index: 9999;
                transition: none;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 212, 255, 0.4);
                border: 2px solid rgba(0, 212, 255, 0.6);
            }

            .cart-bounce {
                animation: cartBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }

            .cart-glow-pulse {
                animation: cartGlowPulse 0.8s ease-out;
            }

            .cart-counter-pop {
                animation: counterPop 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }

            @keyframes cartBounce {
                0% { transform: scale(1); }
                50% { transform: scale(1.2) rotate(5deg); }
                100% { transform: scale(1) rotate(0deg); }
            }

            @keyframes cartGlowPulse {
                0% { 
                    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
                    background: rgba(0, 212, 255, 0.1);
                }
                50% {
                    box-shadow: 0 0 0 10px rgba(0, 212, 255, 0.3);
                    background: rgba(0, 212, 255, 0.2);
                }
                100% { 
                    box-shadow: 0 0 0 20px rgba(0, 212, 255, 0);
                    background: rgba(0, 212, 255, 0);
                }
            }

            @keyframes counterPop {
                0% { transform: scale(1); }
                50% { transform: scale(1.5); }
                100% { transform: scale(1); }
            }

            @keyframes flyToCart {
                0% {
                    opacity: 1;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.8;
                    transform: scale(0.8);
                }
                100% {
                    opacity: 0;
                    transform: scale(0.3);
                }
            }

            /* Success notification animation */
            .cart-success-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(16, 185, 129, 0.9);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 500;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;

        document.head.appendChild(style);
    }

    // Animate product flying to cart
    animateAddToCart(productElement, productData) {
        if (this.isAnimating) return;
        this.isAnimating = true;

        console.log('🎬 Starting add to cart animation');

        try {
            // Get product image or create a placeholder
            const productImg = productElement.querySelector('img');
            const cartBtn = document.querySelector('.cart-btn');

            if (!cartBtn) {
                console.warn('Cart button not found, skipping animation');
                this.isAnimating = false;
                return;
            }

            // Create flying element
            const flyingElement = this.createFlyingElement(productImg, productData);
            
            // Get positions
            const startPos = this.getElementPosition(productElement);
            const endPos = this.getElementPosition(cartBtn);

            // Start animation
            this.performFlyAnimation(flyingElement, startPos, endPos);

            // Animate cart button
            this.animateCartButton(cartBtn);

            // Show success notification
            this.showSuccessNotification(productData);

        } catch (error) {
            console.error('Cart animation error:', error);
        } finally {
            setTimeout(() => {
                this.isAnimating = false;
            }, 1000);
        }
    }

    // Create flying element
    createFlyingElement(productImg, productData) {
        const flyingElement = document.createElement('div');
        flyingElement.className = 'cart-animation-item';
        
        if (productImg) {
            flyingElement.innerHTML = `<img src="${productImg.src}" alt="${productData.title}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">`;
        } else {
            flyingElement.innerHTML = `
                <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple)); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-shopping-bag text-white text-xl"></i>
                </div>
            `;
        }

        document.body.appendChild(flyingElement);
        return flyingElement;
    }

    // Get element position
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
        };
    }

    // Perform flying animation
    performFlyAnimation(flyingElement, startPos, endPos) {
        // Set initial position
        flyingElement.style.left = startPos.x + 'px';
        flyingElement.style.top = startPos.y + 'px';

        // Calculate control point for curved path
        const controlX = (startPos.x + endPos.x) / 2;
        const controlY = Math.min(startPos.y, endPos.y) - 100;

        // Animate along curved path
        const duration = 800;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Cubic bezier curve
            const t = progress;
            const x = Math.pow(1 - t, 2) * startPos.x + 2 * (1 - t) * t * controlX + Math.pow(t, 2) * endPos.x;
            const y = Math.pow(1 - t, 2) * startPos.y + 2 * (1 - t) * t * controlY + Math.pow(t, 2) * endPos.y;

            flyingElement.style.left = x + 'px';
            flyingElement.style.top = y + 'px';

            // Scale and fade
            const scale = 1 - progress * 0.7;
            const opacity = 1 - progress;
            flyingElement.style.transform = `scale(${scale})`;
            flyingElement.style.opacity = opacity;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Animation complete
                flyingElement.remove();
            }
        };

        requestAnimationFrame(animate);
    }

    // Animate cart button
    animateCartButton(cartBtn) {
        // Bounce animation
        cartBtn.classList.add('cart-bounce');
        
        // Glow pulse
        const glowElement = cartBtn.querySelector('.cart-glow');
        if (glowElement) {
            glowElement.classList.add('cart-glow-pulse');
        }

        // Update cart counter with animation
        this.animateCartCounter();

        // Remove animation classes
        setTimeout(() => {
            cartBtn.classList.remove('cart-bounce');
            if (glowElement) {
                glowElement.classList.remove('cart-glow-pulse');
            }
        }, 800);
    }

    // Animate cart counter
    animateCartCounter() {
        const cartBtn = document.querySelector('.cart-btn');
        if (!cartBtn) return;

        // Find or create counter
        let counter = cartBtn.querySelector('.cart-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'cart-counter notification-badge';
            cartBtn.appendChild(counter);
        }

        // Update counter
        const cartItems = storage.getCartItems();
        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
        
        if (totalItems > 0) {
            counter.textContent = totalItems > 99 ? '99+' : totalItems;
            counter.style.display = 'flex';
            counter.classList.add('cart-counter-pop');
            
            setTimeout(() => {
                counter.classList.remove('cart-counter-pop');
            }, 400);
        } else {
            counter.style.display = 'none';
        }
    }

    // Show success notification
    showSuccessNotification(productData) {
        const notification = document.createElement('div');
        notification.className = 'cart-success-notification';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>Added "${productData.title}" to cart!</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // Update cart counter (called from cart manager)
    updateCartCounter() {
        this.animateCartCounter();
    }

    // Animate cart opening
    animateCartOpen() {
        const cartBtn = document.querySelector('.cart-btn');
        if (cartBtn) {
            cartBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                cartBtn.style.transform = 'scale(1)';
            }, 150);
        }
    }
}

// Create global instance
const cartAnimations = new CartAnimations();

// Export for global access
window.cartAnimations = cartAnimations;
