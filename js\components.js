// Reusable UI Components
class Components {
    // Product Card Component
    static createProductCard(product) {
        const discount = Utils.calculateDiscount(product.originalPrice, product.price);
        const finalPrice = Utils.calculatePriceWithTax(product.price);
        const taxAmount = Utils.calculateTax(product.price);

        return `
            <div class="product-card group" data-product-id="${product.id}">
                <div class="relative overflow-hidden rounded-t-2xl">
                    <img src="${product.image || Utils.createImagePlaceholder()}" 
                         alt="${product.title}" 
                         class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                         onerror="this.src='${Utils.createImagePlaceholder()}'">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    ${product.instant ? '<span class="absolute top-3 left-3 bg-gradient-to-r from-green-400 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">⚡ Instant</span>' : ''}
                    ${discount > 0 ? `<span class="absolute top-3 right-3 bg-gradient-to-r from-red-400 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">-${discount}%</span>` : ''}
                    ${product.stock <= 5 && product.stock > 0 ? '<span class="absolute bottom-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">Low Stock</span>' : ''}
                    ${product.stock === 0 ? '<span class="absolute bottom-3 left-3 bg-gradient-to-r from-gray-400 to-gray-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">Out of Stock</span>' : ''}
                </div>
                
                <div class="p-6">
                    <h3 class="font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300 line-clamp-2">
                        ${product.title}
                    </h3>
                    <p class="text-sm text-white/60 mb-3">${product.category}</p>
                    
                    ${product.rating > 0 ? `
                        <div class="flex items-center mb-3">
                            <div class="rating text-yellow-400">
                                ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                            </div>
                            <span class="text-sm text-white/60 ml-2">(${product.reviews})</span>
                        </div>
                    ` : ''}
                    
                    <div class="seller-badge mb-4">${product.storeName || 'Obu Store'}</div>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <span class="price text-xl font-bold" data-price="${product.price}" data-currency="USD">${this.formatProductPrice(product.price)}</span>
                            ${product.originalPrice && product.originalPrice > product.price ? `<span class="price-original text-sm ml-2" data-price="${product.originalPrice}" data-currency="USD">${this.formatProductPrice(product.originalPrice)}</span>` : ''}
                            ${this.createCurrencyConversionInfo(product.price)}
                        </div>
                        <div class="text-sm text-white/60">
                            Stock: ${product.stock}
                        </div>
                    </div>
                    
                    <div class="tax-info mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Final price: <span class="font-semibold">${Utils.formatPrice(finalPrice)}</span> 
                        <span class="text-xs">(+${Utils.formatPrice(taxAmount)} tax)</span>
                    </div>
                    
                    <div class="space-y-3">
                        <button onclick="ProductManager.addToCart(${product.id})"
                                class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 ${product.stock === 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                                ${product.stock === 0 ? 'disabled' : ''}>
                            <i class="fas fa-shopping-cart mr-2"></i>
                            ${product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                        </button>

                        ${product.reviews > 0 ? `
                            <button class="view-reviews-btn w-full py-2 bg-white/10 hover:bg-white/20 text-white rounded-xl font-medium transition-all duration-300"
                                    data-product-id="${product.id}">
                                <i class="fas fa-star mr-2"></i>
                                View ${product.reviews} Review${product.reviews !== 1 ? 's' : ''}
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // Category Card Component
    static createCategoryCard(category) {
        return `
            <div class="category-card group" onclick="CategoryManager.browseCategory('${category.name}')">
                <div class="feature-icon-wrapper mb-6">
                    <div class="feature-icon bg-gradient-to-br ${category.gradient} w-20 h-20 rounded-2xl">
                        <i class="${category.icon} text-white text-3xl"></i>
                    </div>
                    <div class="feature-glow"></div>
                </div>
                <h3 class="text-lg font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                    ${category.name}
                </h3>
                <p class="text-sm text-white/60 mb-2">${category.count} items</p>
                ${category.description ? `<p class="text-xs text-white/40">${category.description}</p>` : ''}
            </div>
        `;
    }

    // Modal Component with enhanced mobile support
    static createModal(id, title, content, actions = '') {
        return `
            <div id="${id}" class="fixed inset-0 bg-black/80 backdrop-blur-sm hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-2 md:p-4">
                    <div class="modal-card bg-dark-secondary/95 backdrop-blur-xl border border-white/10 rounded-2xl w-full max-w-md max-h-[95vh] md:max-h-[90vh] overflow-y-auto p-4 md:p-8">
                        <div class="flex justify-between items-center mb-4 md:mb-6">
                            <h3 class="text-lg md:text-2xl font-bold text-white">${title}</h3>
                            <button class="modal-close text-white/60 hover:text-white transition-colors duration-200 p-2 -m-2">
                                <i class="fas fa-times text-lg md:text-xl"></i>
                            </button>
                        </div>

                        <div class="modal-content">
                            ${content}
                        </div>

                        ${actions ? `<div class="modal-actions mt-4 md:mt-6">${actions}</div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // Notification Component
    static createNotification(message, type = 'info', duration = CONFIG.UI.NOTIFICATION_DURATION) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };

        const notification = Utils.createElement('div', `alert alert-${type} fixed top-6 right-6 z-50 max-w-sm transform translate-x-full`);
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${icons[type]} mr-3 text-lg"></i>
                <span class="font-medium">${message}</span>
                <button class="ml-auto text-current opacity-70 hover:opacity-100" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Slide in animation
        setTimeout(() => {
            notification.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, duration);

        return notification;
    }

    // Loading Spinner Component
    static createLoadingSpinner(size = 'normal') {
        const sizes = {
            small: 'w-4 h-4',
            normal: 'w-8 h-8',
            large: 'w-12 h-12'
        };

        return `
            <div class="flex items-center justify-center">
                <div class="spinner ${sizes[size]} border-4 border-white/20 border-t-cyan-400 rounded-full animate-spin"></div>
            </div>
        `;
    }

    // Search Results Component
    static createSearchResults(products, query) {
        if (products.length === 0) {
            return `
                <div class="text-center py-12">
                    <i class="fas fa-search text-4xl text-white/30 mb-4"></i>
                    <h3 class="text-xl font-semibold text-white mb-2">No products found</h3>
                    <p class="text-white/60">Try adjusting your search terms for "${query}"</p>
                </div>
            `;
        }

        return `
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-white mb-2">
                    Search Results for "${query}" (${products.length} found)
                </h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                ${products.map(product => this.createProductCard(product)).join('')}
            </div>
        `;
    }

    // Cart Item Component with mobile optimization
    static createCartItem(item, product) {
        const subtotal = product.price * item.quantity;
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;

        return `
            <div class="cart-item flex flex-col md:flex-row md:items-center p-4 bg-dark-secondary/30 rounded-xl border border-white/10 gap-4" data-item-id="${item.id}">
                <div class="flex items-center gap-4 flex-1">
                    <img src="${product.image || Utils.createImagePlaceholder(80, 80)}"
                         alt="${product.title}"
                         class="w-16 h-16 md:w-20 md:h-20 object-cover rounded-lg flex-shrink-0">

                    <div class="flex-1 min-w-0">
                        <h4 class="font-semibold text-white mb-1 truncate">${product.title}</h4>
                        <p class="text-sm text-white/60 mb-2">${product.category}</p>
                        <div class="text-right md:hidden">
                            <div class="text-white font-semibold">${Utils.formatPrice(total)}</div>
                            <div class="text-xs text-white/60">+${Utils.formatPrice(tax)} tax</div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between md:justify-end gap-4">
                    <div class="flex items-center space-x-2">
                        <button onclick="cartManager.updateQuantity(${item.productId}, ${item.quantity - 1})"
                                class="w-10 h-10 md:w-8 md:h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors touch-manipulation"
                                ${item.quantity <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-minus text-xs"></i>
                        </button>
                        <span class="text-white font-medium w-8 text-center">${item.quantity}</span>
                        <button onclick="cartManager.updateQuantity(${item.productId}, ${item.quantity + 1})"
                                class="w-10 h-10 md:w-8 md:h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors touch-manipulation">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                    </div>

                    <div class="text-right hidden md:block">
                        <div class="text-white font-semibold">${Utils.formatPrice(total)}</div>
                        <div class="text-xs text-white/60">+${Utils.formatPrice(tax)} tax</div>
                    </div>

                    <button onclick="cartManager.removeFromCart(${item.productId})"
                            class="w-10 h-10 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors touch-manipulation">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // Pagination Component
    static createPagination(currentPage, totalPages, onPageChange) {
        if (totalPages <= 1) return '';

        let pages = [];
        const maxVisible = 5;
        
        if (totalPages <= maxVisible) {
            pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        } else {
            const start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            const end = Math.min(totalPages, start + maxVisible - 1);
            pages = Array.from({ length: end - start + 1 }, (_, i) => start + i);
        }

        return `
            <div class="flex items-center justify-center space-x-2 mt-8">
                <button onclick="${onPageChange}(${currentPage - 1})" 
                        class="px-3 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
                        ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                ${pages.map(page => `
                    <button onclick="${onPageChange}(${page})" 
                            class="px-4 py-2 rounded-lg transition-colors ${page === currentPage ? 'bg-cyan-400 text-dark-primary font-semibold' : 'bg-white/10 hover:bg-white/20 text-white'}">
                        ${page}
                    </button>
                `).join('')}
                
                <button onclick="${onPageChange}(${currentPage + 1})" 
                        class="px-3 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
                        ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    // Filter Component
    static createFilterSection(categories, selectedCategory = '', onCategoryChange = 'ProductManager.filterByCategory') {
        return `
            <div class="filter-section mb-8">
                <h3 class="text-lg font-semibold text-white mb-4">Filter by Category</h3>
                <div class="flex flex-wrap gap-2">
                    <button onclick="${onCategoryChange}('')" 
                            class="filter-btn px-4 py-2 rounded-lg transition-colors ${selectedCategory === '' ? 'bg-cyan-400 text-dark-primary' : 'bg-white/10 text-white hover:bg-white/20'}">
                        All Categories
                    </button>
                    ${categories.map(category => `
                        <button onclick="${onCategoryChange}('${category.name}')" 
                                class="filter-btn px-4 py-2 rounded-lg transition-colors ${selectedCategory === category.name ? 'bg-cyan-400 text-dark-primary' : 'bg-white/10 text-white hover:bg-white/20'}">
                            ${category.name} (${category.count})
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Empty State Component
    static createEmptyState(title, message, actionText = '', actionHandler = '') {
        return `
            <div class="empty-state text-center py-16">
                <i class="fas fa-box-open text-6xl text-white/20 mb-6"></i>
                <h3 class="text-2xl font-semibold text-white mb-4">${title}</h3>
                <p class="text-white/60 mb-6 max-w-md mx-auto">${message}</p>
                ${actionText && actionHandler ? `
                    <button onclick="${actionHandler}" class="btn-primary px-6 py-3 rounded-xl font-semibold">
                        ${actionText}
                    </button>
                ` : ''}
            </div>
        `;
    }

    // Format product price with currency support
    static formatProductPrice(price) {
        if (typeof currencyManager !== 'undefined') {
            const convertedPrice = currencyManager.convertToCurrentCurrency(price, 'USD');
            return currencyManager.formatCurrency(convertedPrice);
        }
        return Utils.formatPrice(price);
    }

    // Create currency conversion info
    static createCurrencyConversionInfo(price) {
        if (typeof currencyManager === 'undefined') {
            return '';
        }

        const conversionInfo = currencyManager.getConversionInfo(price, 'USD');
        if (!conversionInfo) {
            return '';
        }

        return `
            <div class="text-xs text-white/50 mt-1">
                <i class="fas fa-exchange-alt mr-1"></i>
                ${currencyManager.formatCurrency(price, 'USD')} → ${currencyManager.formatCurrency(conversionInfo.convertedAmount)}
            </div>
        `;
    }
}
