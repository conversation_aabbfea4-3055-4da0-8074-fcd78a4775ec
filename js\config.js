// Configuration and Constants
const CONFIG = {
    APP_NAME: 'Obu Marketplace',
    VERSION: '1.0.0',
    TAX_RATE: 0.10, // 10% tax
    CURRENCY: 'USD',
    CURRENCY_SYMBOL: '$',
    
    // LocalStorage Keys
    STORAGE_KEYS: {
        USERS: 'obu_users',
        PRODUCTS: 'obu_products',
        PURCHASES: 'obu_purchases',
        CART: 'obu_cart',
        CURRENT_USER: 'obu_current_user',
        CATEGORIES: 'obu_categories',
        USER_SESSION: 'obu_user_session'
    },

    // Authentication Settings
    AUTH: {
        SESSION_DURATION: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
        PASSWORD_MIN_LENGTH: 8,
        REQUIRE_EMAIL_VERIFICATION: false // For demo purposes
    },
    
    // Simplified Categories
    DEFAULT_CATEGORIES: [
        {
            id: 1,
            name: 'Gaming',
            icon: 'fas fa-gamepad',
            gradient: 'from-cyan-400 to-blue-500',
            count: 0,
            description: 'Games, accounts, and in-game items'
        },
        {
            id: 2,
            name: 'Digital Cards',
            icon: 'fas fa-credit-card',
            gradient: 'from-green-400 to-emerald-500',
            count: 0,
            description: 'Gift cards and digital vouchers'
        },
        {
            id: 3,
            name: 'Streaming',
            icon: 'fas fa-play-circle',
            gradient: 'from-purple-400 to-pink-500',
            count: 0,
            description: 'Entertainment and streaming services'
        },
        {
            id: 4,
            name: 'Software',
            icon: 'fas fa-laptop-code',
            gradient: 'from-indigo-400 to-blue-500',
            count: 0,
            description: 'Software and digital tools'
        }
    ],
    
    // Sample Products for Initial Setup (Admin-managed products)
    SAMPLE_PRODUCTS: [
        {
            id: 1,
            title: 'Roblox 1000 Robux',
            description: 'Get 1000 Robux instantly delivered to your account. Perfect for purchasing premium items, game passes, and avatar accessories.',
            category: 'Gaming',
            categoryId: 1,
            price: 12.99,
            originalPrice: 15.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.8,
            reviews: 245,
            instant: true,
            stock: 50,
            tags: ['roblox', 'robux', 'gaming', 'instant'],
            featured: true,
            downloadUrl: 'https://example.com/download/roblox-code',
            createdAt: new Date().toISOString()
        },
        {
            id: 2,
            title: 'Steam Wallet $50',
            description: 'Steam Wallet Gift Card worth $50 USD. Can be used to purchase games, DLC, and in-game items on Steam.',
            category: 'Digital Cards',
            categoryId: 2,
            price: 45.00,
            originalPrice: 50.00,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Digital Store',
            rating: 4.9,
            reviews: 189,
            instant: true,
            stock: 30,
            tags: ['steam', 'gift card', 'gaming'],
            featured: true,
            downloadUrl: 'https://example.com/download/steam-wallet-code',
            createdAt: new Date().toISOString()
        },
        {
            id: 3,
            title: 'Mobile Legends 2000 Diamonds',
            description: 'Mobile Legends Bang Bang 2000 Diamonds top-up. Get premium skins, heroes, and battle passes.',
            category: 'Gaming',
            categoryId: 1,
            price: 28.50,
            originalPrice: 32.00,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Mobile Gaming',
            rating: 4.7,
            reviews: 156,
            instant: true,
            stock: 25,
            tags: ['mobile legends', 'diamonds', 'mobile gaming'],
            featured: true,
            downloadUrl: 'https://example.com/download/ml-diamonds-code',
            createdAt: new Date().toISOString()
        },
        {
            id: 4,
            title: 'Valorant VP 2000',
            description: 'Valorant Points for purchasing weapon skins, battle passes, and premium content.',
            category: 'Gaming',
            categoryId: 1,
            price: 19.99,
            originalPrice: 24.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.6,
            reviews: 98,
            instant: true,
            stock: 40,
            tags: ['valorant', 'vp', 'riot games'],
            featured: false,
            downloadUrl: 'https://example.com/download/valorant-vp-code',
            createdAt: new Date().toISOString()
        },
        {
            id: 5,
            title: 'Netflix Premium 1 Month',
            description: 'Netflix Premium subscription for 1 month. Enjoy 4K streaming on multiple devices.',
            category: 'Streaming',
            categoryId: 3,
            price: 15.99,
            originalPrice: 17.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Digital Store',
            rating: 4.9,
            reviews: 312,
            instant: true,
            stock: 100,
            tags: ['netflix', 'streaming', 'premium'],
            featured: false,
            downloadUrl: 'https://example.com/download/netflix-account',
            createdAt: new Date().toISOString()
        },
        {
            id: 6,
            title: 'Fortnite V-Bucks 2800',
            description: 'Get 2800 V-Bucks for Fortnite. Purchase Battle Passes, skins, emotes, and more exclusive content.',
            category: 'Gaming',
            categoryId: 1,
            price: 19.99,
            originalPrice: 24.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.7,
            reviews: 189,
            instant: true,
            stock: 75,
            tags: ['fortnite', 'v-bucks', 'battle royale', 'epic games'],
            featured: true,
            downloadUrl: 'https://example.com/download/fortnite-vbucks',
            createdAt: new Date().toISOString()
        },
        {
            id: 7,
            title: 'PlayStation Store $25',
            description: 'PlayStation Store Gift Card worth $25. Perfect for games, DLC, and PlayStation Plus subscriptions.',
            category: 'Digital Cards',
            categoryId: 2,
            price: 22.50,
            originalPrice: 25.00,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Digital Store',
            rating: 4.9,
            reviews: 156,
            instant: true,
            stock: 40,
            tags: ['playstation', 'ps5', 'ps4', 'gift card'],
            featured: false,
            downloadUrl: 'https://example.com/download/ps-store-card',
            createdAt: new Date().toISOString()
        },
        {
            id: 8,
            title: 'Spotify Premium 3 Months',
            description: 'Spotify Premium subscription for 3 months. Ad-free music, offline downloads, and unlimited skips.',
            category: 'Streaming',
            categoryId: 3,
            price: 29.99,
            originalPrice: 35.97,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Digital Store',
            rating: 4.8,
            reviews: 203,
            instant: true,
            stock: 60,
            tags: ['spotify', 'music', 'streaming', 'premium'],
            featured: false,
            downloadUrl: 'https://example.com/download/spotify-premium',
            createdAt: new Date().toISOString()
        },
        {
            id: 9,
            title: 'Minecraft Java Edition',
            description: 'Minecraft Java Edition account with full access. Build, explore, and create in the ultimate sandbox game.',
            category: 'Gaming',
            categoryId: 1,
            price: 26.95,
            originalPrice: 29.95,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.9,
            reviews: 312,
            instant: true,
            stock: 25,
            tags: ['minecraft', 'java', 'sandbox', 'mojang'],
            featured: true,
            downloadUrl: 'https://example.com/download/minecraft-account',
            createdAt: new Date().toISOString()
        },
        {
            id: 10,
            title: 'Adobe Creative Cloud 1 Month',
            description: 'Adobe Creative Cloud subscription for 1 month. Access to Photoshop, Illustrator, Premiere Pro, and more.',
            category: 'Software',
            categoryId: 4,
            price: 52.99,
            originalPrice: 59.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Software Store',
            rating: 4.6,
            reviews: 89,
            instant: true,
            stock: 15,
            tags: ['adobe', 'creative', 'photoshop', 'design'],
            featured: false,
            downloadUrl: 'https://example.com/download/adobe-cc',
            createdAt: new Date().toISOString()
        },
        {
            id: 11,
            title: 'League of Legends 3500 RP',
            description: 'League of Legends Riot Points for purchasing champions, skins, and other in-game content.',
            category: 'Gaming',
            categoryId: 1,
            price: 24.99,
            originalPrice: 27.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.8,
            reviews: 267,
            instant: true,
            stock: 55,
            tags: ['league of legends', 'riot points', 'lol', 'moba'],
            featured: false,
            downloadUrl: 'https://example.com/download/lol-rp',
            createdAt: new Date().toISOString()
        },
        {
            id: 12,
            title: 'Xbox Game Pass Ultimate 1 Month',
            description: 'Xbox Game Pass Ultimate for 1 month. Access to hundreds of games on Xbox and PC, plus Xbox Live Gold.',
            category: 'Gaming',
            categoryId: 1,
            price: 14.99,
            originalPrice: 16.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            adminManaged: true,
            storeName: 'Obu Gaming Store',
            rating: 4.9,
            reviews: 445,
            instant: true,
            stock: 80,
            tags: ['xbox', 'game pass', 'microsoft', 'subscription'],
            featured: true,
            downloadUrl: 'https://example.com/download/xbox-gamepass',
            createdAt: new Date().toISOString()
        }
    ],
    
    // Animation Settings
    ANIMATIONS: {
        DURATION: {
            FAST: 200,
            NORMAL: 300,
            SLOW: 500
        },
        EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
        STAGGER_DELAY: 100
    },
    
    // UI Settings
    UI: {
        ITEMS_PER_PAGE: 12,
        SEARCH_MIN_LENGTH: 2,
        NOTIFICATION_DURATION: 4000,
        MODAL_ANIMATION_DURATION: 300
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
