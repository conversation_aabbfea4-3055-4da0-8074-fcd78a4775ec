// Configuration and Constants
const CONFIG = {
    APP_NAME: 'Obu Marketplace',
    VERSION: '1.0.0',
    TAX_RATE: 0.10, // 10% tax
    CURRENCY: 'USD',
    CURRENCY_SYMBOL: '$',
    
    // LocalStorage Keys
    STORAGE_KEYS: {
        USERS: 'obu_users',
        PRODUCTS: 'obu_products',
        ORDERS: 'obu_orders',
        CART: 'obu_cart',
        CURRENT_USER: 'obu_current_user',
        CATEGORIES: 'obu_categories'
    },
    
    // Default Categories
    DEFAULT_CATEGORIES: [
        { 
            id: 1,
            name: 'Game Top Up', 
            icon: 'fas fa-gamepad', 
            gradient: 'from-cyan-400 to-blue-500', 
            count: 0,
            description: 'In-game currency and credits'
        },
        { 
            id: 2,
            name: 'Gaming Accounts', 
            icon: 'fas fa-user-circle', 
            gradient: 'from-purple-400 to-pink-500', 
            count: 0,
            description: 'Pre-leveled gaming accounts'
        },
        { 
            id: 3,
            name: 'Gift Cards', 
            icon: 'fas fa-gift', 
            gradient: 'from-green-400 to-emerald-500', 
            count: 0,
            description: 'Digital gift cards and vouchers'
        },
        { 
            id: 4,
            name: 'Game Items', 
            icon: 'fas fa-sword', 
            gradient: 'from-red-400 to-pink-500', 
            count: 0,
            description: 'Weapons, skins, and collectibles'
        },
        { 
            id: 5,
            name: 'Streaming', 
            icon: 'fas fa-video', 
            gradient: 'from-pink-400 to-purple-500', 
            count: 0,
            description: 'Streaming service subscriptions'
        },
        { 
            id: 6,
            name: 'Software', 
            icon: 'fas fa-desktop', 
            gradient: 'from-indigo-400 to-blue-500', 
            count: 0,
            description: 'Software licenses and tools'
        }
    ],
    
    // Sample Products for Initial Setup
    SAMPLE_PRODUCTS: [
        {
            id: 1,
            title: 'Roblox 1000 Robux',
            description: 'Get 1000 Robux instantly delivered to your account. Perfect for purchasing premium items, game passes, and avatar accessories.',
            category: 'Game Top Up',
            categoryId: 1,
            price: 12.99,
            originalPrice: 15.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            sellerId: 1,
            sellerName: 'GameStore Pro',
            rating: 4.8,
            reviews: 245,
            instant: true,
            stock: 50,
            tags: ['roblox', 'robux', 'gaming', 'instant'],
            featured: true,
            createdAt: new Date().toISOString()
        },
        {
            id: 2,
            title: 'Steam Wallet $50',
            description: 'Steam Wallet Gift Card worth $50 USD. Can be used to purchase games, DLC, and in-game items on Steam.',
            category: 'Gift Cards',
            categoryId: 3,
            price: 45.00,
            originalPrice: 50.00,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            sellerId: 2,
            sellerName: 'Digital Hub',
            rating: 4.9,
            reviews: 189,
            instant: true,
            stock: 30,
            tags: ['steam', 'gift card', 'gaming'],
            featured: true,
            createdAt: new Date().toISOString()
        },
        {
            id: 3,
            title: 'Mobile Legends 2000 Diamonds',
            description: 'Mobile Legends Bang Bang 2000 Diamonds top-up. Get premium skins, heroes, and battle passes.',
            category: 'Game Top Up',
            categoryId: 1,
            price: 28.50,
            originalPrice: 32.00,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            sellerId: 1,
            sellerName: 'Gaming Central',
            rating: 4.7,
            reviews: 156,
            instant: true,
            stock: 25,
            tags: ['mobile legends', 'diamonds', 'mobile gaming'],
            featured: true,
            createdAt: new Date().toISOString()
        },
        {
            id: 4,
            title: 'Valorant VP 2000',
            description: 'Valorant Points for purchasing weapon skins, battle passes, and premium content.',
            category: 'Game Top Up',
            categoryId: 1,
            price: 19.99,
            originalPrice: 24.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            sellerId: 3,
            sellerName: 'Pro Gaming Store',
            rating: 4.6,
            reviews: 98,
            instant: true,
            stock: 40,
            tags: ['valorant', 'vp', 'riot games'],
            featured: false,
            createdAt: new Date().toISOString()
        },
        {
            id: 5,
            title: 'Netflix Premium 1 Month',
            description: 'Netflix Premium subscription for 1 month. Enjoy 4K streaming on multiple devices.',
            category: 'Streaming',
            categoryId: 5,
            price: 15.99,
            originalPrice: 17.99,
            image: 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
            sellerId: 2,
            sellerName: 'Digital Hub',
            rating: 4.9,
            reviews: 312,
            instant: true,
            stock: 100,
            tags: ['netflix', 'streaming', 'premium'],
            featured: false,
            createdAt: new Date().toISOString()
        }
    ],
    
    // Animation Settings
    ANIMATIONS: {
        DURATION: {
            FAST: 200,
            NORMAL: 300,
            SLOW: 500
        },
        EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
        STAGGER_DELAY: 100
    },
    
    // UI Settings
    UI: {
        ITEMS_PER_PAGE: 12,
        SEARCH_MIN_LENGTH: 2,
        NOTIFICATION_DURATION: 4000,
        MODAL_ANIMATION_DURATION: 300
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
