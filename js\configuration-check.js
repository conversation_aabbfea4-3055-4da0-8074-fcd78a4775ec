// Configuration Check for Obu Marketplace Supabase Integration
class ConfigurationCheck {
    constructor() {
        this.checks = [];
    }

    // Run all configuration checks
    async runConfigurationCheck() {
        console.log('⚙️ CHECKING OBU MARKETPLACE CONFIGURATION');
        console.log('='.repeat(55));
        
        this.checks = [];
        
        this.checkScriptLoading();
        this.checkSupabaseCredentials();
        await this.checkSupabaseConnection();
        this.checkAuthManagerIntegration();
        this.checkFrontendIntegration();
        
        this.displayConfigurationResults();
        return this.getConfigurationStatus();
    }

    // Check if all required scripts are loaded
    checkScriptLoading() {
        console.log('\n📜 Checking Script Loading...');
        
        // Check Supabase client
        this.addCheck('Supabase client loaded', typeof supabase !== 'undefined');
        
        // Check integration classes
        this.addCheck('SupabaseIntegration class', typeof supabaseIntegration !== 'undefined');
        this.addCheck('AuthManager class', typeof authManager !== 'undefined');
        this.addCheck('ProductManager class', typeof productManager !== 'undefined');
        this.addCheck('CartManager class', typeof cartManager !== 'undefined');
        this.addCheck('ReviewManager class', typeof reviewManager !== 'undefined');
        
        // Check utility classes
        this.addCheck('Storage class', typeof storage !== 'undefined');
        this.addCheck('Utils class', typeof Utils !== 'undefined');
        this.addCheck('Components class', typeof Components !== 'undefined');
        this.addCheck('CONFIG object', typeof CONFIG !== 'undefined');
    }

    // Check Supabase credentials configuration
    checkSupabaseCredentials() {
        console.log('\n🔑 Checking Supabase Credentials...');
        
        if (typeof supabaseIntegration !== 'undefined') {
            // Check URL format
            const urlValid = supabaseIntegration.supabaseUrl && 
                           supabaseIntegration.supabaseUrl.includes('supabase.co') &&
                           supabaseIntegration.supabaseUrl.startsWith('https://');
            this.addCheck('Supabase URL format', urlValid);
            
            // Check if URL is not placeholder
            const urlNotPlaceholder = !supabaseIntegration.supabaseUrl.includes('YOUR_');
            this.addCheck('Supabase URL configured', urlNotPlaceholder);
            
            // Check API key format (JWT structure)
            const keyValid = supabaseIntegration.supabaseKey && 
                           supabaseIntegration.supabaseKey.includes('.') &&
                           supabaseIntegration.supabaseKey.length > 100;
            this.addCheck('API key format', keyValid);
            
            // Check if key is not placeholder
            const keyNotPlaceholder = !supabaseIntegration.supabaseKey.includes('YOUR_');
            this.addCheck('API key configured', keyNotPlaceholder);
            
            // Display configured values (partially hidden for security)
            if (urlValid && urlNotPlaceholder) {
                console.log(`✅ URL: ${supabaseIntegration.supabaseUrl}`);
            }
            if (keyValid && keyNotPlaceholder) {
                const hiddenKey = supabaseIntegration.supabaseKey.substring(0, 20) + '...' + 
                                supabaseIntegration.supabaseKey.substring(supabaseIntegration.supabaseKey.length - 10);
                console.log(`✅ Key: ${hiddenKey}`);
            }
        } else {
            this.addCheck('SupabaseIntegration available', false);
        }
    }

    // Check Supabase connection
    async checkSupabaseConnection() {
        console.log('\n🔗 Checking Supabase Connection...');
        
        if (typeof supabaseIntegration === 'undefined') {
            this.addCheck('Connection test skipped', false, 'SupabaseIntegration not available');
            return;
        }

        try {
            // Initialize connection
            const initialized = await supabaseIntegration.init();
            this.addCheck('Supabase initialization', initialized);
            
            if (initialized) {
                // Test connection
                const connected = supabaseIntegration.isConnected();
                this.addCheck('Connection established', connected);
                
                if (connected) {
                    // Test database access
                    const dbTest = await supabaseIntegration.testConnection();
                    this.addCheck('Database access', dbTest);
                }
            }
        } catch (error) {
            this.addCheck('Connection error', false, error.message);
        }
    }

    // Check AuthManager integration
    checkAuthManagerIntegration() {
        console.log('\n🔐 Checking AuthManager Integration...');
        
        if (typeof authManager !== 'undefined') {
            // Check social auth methods
            this.addCheck('Google sign-in method', typeof authManager.signInWithGoogle === 'function');
            this.addCheck('Apple sign-in method', typeof authManager.signInWithApple === 'function');
            this.addCheck('Social auth initialization', typeof authManager.initializeSocialAuth === 'function');
            
            // Check if AuthManager has been initialized
            this.addCheck('AuthManager initialized', authManager.currentUser !== undefined);
            
            // Check authentication state
            const isLoggedIn = authManager.isLoggedIn();
            this.addCheck('Current auth state', true, isLoggedIn ? 'User logged in' : 'No user logged in');
        } else {
            this.addCheck('AuthManager available', false);
        }
    }

    // Check frontend integration
    checkFrontendIntegration() {
        console.log('\n🎨 Checking Frontend Integration...');
        
        // Check HTML elements
        this.addCheck('Login button exists', document.getElementById('loginBtn') !== null);
        this.addCheck('Register button exists', document.getElementById('registerBtn') !== null);
        
        // Check for social login buttons in modals (they're created dynamically)
        this.addCheck('Social login methods available', 
            typeof authManager !== 'undefined' && 
            typeof authManager.signInWithGoogle === 'function');
        
        // Check viewport configuration
        const viewport = document.querySelector('meta[name="viewport"]');
        this.addCheck('Mobile viewport configured', viewport !== null);
        
        // Check theme color
        const themeColor = document.querySelector('meta[name="theme-color"]');
        this.addCheck('Theme color configured', themeColor !== null);
        
        // Check if products are loaded
        if (typeof storage !== 'undefined') {
            const products = storage.getProducts();
            this.addCheck('Sample products loaded', products && products.length > 0);
        }
        
        // Check test functions availability
        this.addCheck('Test functions available', 
            typeof window.testSupabaseConnection === 'function' &&
            typeof window.testObuMarketplace === 'function');
    }

    // Helper method to add check results
    addCheck(checkName, passed, details = '') {
        const result = {
            name: checkName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        };
        
        this.checks.push(result);
        
        const icon = passed ? '✅' : '❌';
        const detailsText = details ? ` (${details})` : '';
        console.log(`${icon} ${checkName}${detailsText}`);
    }

    // Display configuration results
    displayConfigurationResults() {
        const passed = this.checks.filter(c => c.passed).length;
        const total = this.checks.length;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n' + '='.repeat(55));
        console.log('🎯 CONFIGURATION CHECK RESULTS');
        console.log('='.repeat(55));
        console.log(`Total Checks: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Configuration Score: ${percentage}%`);
        console.log('='.repeat(55));
        
        if (percentage === 100) {
            console.log('🎉 PERFECT! Everything is configured correctly.');
        } else if (percentage >= 90) {
            console.log('✅ EXCELLENT! Minor issues to address.');
        } else if (percentage >= 80) {
            console.log('✅ GOOD! Some configuration needed.');
        } else if (percentage >= 70) {
            console.log('⚠️ FAIR! Several issues to fix.');
        } else {
            console.log('🚨 POOR! Major configuration problems.');
        }
        
        // Show failed checks
        const failed = this.checks.filter(c => !c.passed);
        if (failed.length > 0) {
            console.log('\n❌ Failed Checks:');
            failed.forEach(check => {
                const details = check.details ? ` - ${check.details}` : '';
                console.log(`  • ${check.name}${details}`);
            });
            
            console.log('\n💡 Next Steps:');
            if (failed.some(c => c.name.includes('Supabase'))) {
                console.log('  • Check Supabase URL and API key configuration');
                console.log('  • Verify Supabase project is active and accessible');
                console.log('  • Run database schema scripts if not done yet');
            }
            if (failed.some(c => c.name.includes('script') || c.name.includes('class'))) {
                console.log('  • Check that all JavaScript files are loaded correctly');
                console.log('  • Verify script order in HTML files');
            }
        }
        
        console.log('\n' + '='.repeat(55));
    }

    // Get configuration status
    getConfigurationStatus() {
        const passed = this.checks.filter(c => c.passed).length;
        const total = this.checks.length;
        const percentage = Math.round((passed / total) * 100);
        
        return {
            configured: percentage >= 80,
            percentage: percentage,
            totalChecks: total,
            passedChecks: passed,
            failedChecks: total - passed,
            status: percentage >= 95 ? 'PERFECT' : 
                   percentage >= 90 ? 'EXCELLENT' : 
                   percentage >= 80 ? 'GOOD' : 
                   percentage >= 70 ? 'FAIR' : 'POOR',
            readyForProduction: percentage >= 90
        };
    }
}

// Create global instance
const configurationCheck = new ConfigurationCheck();

// Add to window for console access
window.checkConfiguration = () => configurationCheck.runConfigurationCheck();

// Auto-run configuration check
document.addEventListener('DOMContentLoaded', async () => {
    setTimeout(async () => {
        console.log('⚙️ Configuration Check Script Loaded');
        console.log('Run window.checkConfiguration() for full check');
        
        // Auto-run configuration check
        await configurationCheck.runConfigurationCheck();
    }, 2000);
});
