// Final Production Readiness Verification Script
class ProductionVerification {
    constructor() {
        this.verificationResults = [];
        this.criticalIssues = [];
        this.warnings = [];
    }

    // Run complete production verification
    async runProductionVerification() {
        console.log('🚀 STARTING PRODUCTION READINESS VERIFICATION');
        console.log('='.repeat(60));
        
        this.verificationResults = [];
        this.criticalIssues = [];
        this.warnings = [];

        // Core System Verification
        await this.verifyCoreSystems();
        await this.verifyAuthentication();
        await this.verifyUserInterface();
        await this.verifyMobileOptimization();
        await this.verifyDataIntegrity();
        await this.verifySecurityMeasures();
        
        // Generate final report
        this.generateFinalReport();
        
        return this.getFinalStatus();
    }

    // Verify core systems
    async verifyCoreSystems() {
        console.log('\n🔧 Verifying Core Systems...');
        
        // Check all manager classes
        this.checkExists('AuthManager', typeof authManager !== 'undefined', 'critical');
        this.checkExists('ProductManager', typeof productManager !== 'undefined', 'critical');
        this.checkExists('CartManager', typeof cartManager !== 'undefined', 'critical');
        this.checkExists('ReviewManager', typeof reviewManager !== 'undefined', 'critical');
        
        // Check storage system
        this.checkExists('Storage System', typeof storage !== 'undefined', 'critical');
        this.checkFunction('Storage.getProducts', storage?.getProducts, 'critical');
        this.checkFunction('Storage.addUser', storage?.addUser, 'critical');
        this.checkFunction('Storage.addPurchase', storage?.addPurchase, 'critical');
        
        // Check utilities
        this.checkExists('Utils', typeof Utils !== 'undefined', 'critical');
        this.checkExists('Components', typeof Components !== 'undefined', 'critical');
        this.checkExists('CONFIG', typeof CONFIG !== 'undefined', 'critical');
        
        // Test basic functionality
        try {
            const products = storage.getProducts();
            this.checkCondition('Products loaded', products && products.length > 0, 'critical');
            
            const categories = storage.getCategories();
            this.checkCondition('Categories loaded', categories && categories.length > 0, 'critical');
        } catch (error) {
            this.addCriticalIssue('Core system error: ' + error.message);
        }
    }

    // Verify authentication system
    async verifyAuthentication() {
        console.log('\n🔐 Verifying Authentication System...');
        
        // Check authentication methods
        this.checkFunction('AuthManager.showLoginModal', authManager?.showLoginModal, 'critical');
        this.checkFunction('AuthManager.showRegisterModal', authManager?.showRegisterModal, 'critical');
        this.checkFunction('AuthManager.handleLogin', authManager?.handleLogin, 'critical');
        this.checkFunction('AuthManager.handleRegister', authManager?.handleRegister, 'critical');
        this.checkFunction('AuthManager.logout', authManager?.logout, 'critical');
        
        // Check social authentication
        this.checkFunction('AuthManager.signInWithGoogle', authManager?.signInWithGoogle, 'warning');
        this.checkFunction('AuthManager.signInWithApple', authManager?.signInWithApple, 'warning');
        
        // Check Supabase integration
        this.checkExists('Supabase Integration', typeof supabaseIntegration !== 'undefined', 'warning');
        
        // Test validation functions
        this.checkFunction('Utils.validateEmail', Utils?.validateEmail, 'critical');
        this.checkFunction('Utils.validatePassword', Utils?.validatePassword, 'critical');
        
        // Test validation
        try {
            const emailValid = Utils.validateEmail('<EMAIL>');
            const emailInvalid = Utils.validateEmail('invalid-email');
            this.checkCondition('Email validation works', emailValid && !emailInvalid, 'critical');
            
            const passwordWeak = Utils.validatePassword('123');
            const passwordStrong = Utils.validatePassword('strongpass123');
            this.checkCondition('Password validation works', !passwordWeak.isValid && passwordStrong.isValid, 'critical');
        } catch (error) {
            this.addCriticalIssue('Validation error: ' + error.message);
        }
    }

    // Verify user interface
    async verifyUserInterface() {
        console.log('\n🎨 Verifying User Interface...');
        
        // Check modal system
        this.checkFunction('Components.createModal', Components?.createModal, 'critical');
        this.checkFunction('Components.createProductCard', Components?.createProductCard, 'critical');
        this.checkFunction('Components.createNotification', Components?.createNotification, 'critical');
        
        // Check dark theme elements
        const darkElements = document.querySelectorAll('[class*="dark-"], [class*="bg-dark"]');
        this.checkCondition('Dark theme elements present', darkElements.length > 0, 'warning');
        
        // Check gradient elements
        const gradientElements = document.querySelectorAll('[class*="gradient"], [class*="from-"], [class*="to-"]');
        this.checkCondition('Gradient elements present', gradientElements.length > 0, 'warning');
        
        // Check responsive classes
        const responsiveElements = document.querySelectorAll('[class*="md:"], [class*="lg:"], [class*="sm:"]');
        this.checkCondition('Responsive classes present', responsiveElements.length > 0, 'critical');
        
        // Test modal creation
        try {
            const modal = Components.createModal('testModal', 'Test', 'Content');
            this.checkCondition('Modal creation works', modal && modal.includes('modal-card'), 'critical');
        } catch (error) {
            this.addCriticalIssue('Modal creation error: ' + error.message);
        }
    }

    // Verify mobile optimization
    async verifyMobileOptimization() {
        console.log('\n📱 Verifying Mobile Optimization...');
        
        // Check viewport configuration
        const viewport = document.querySelector('meta[name="viewport"]');
        this.checkCondition('Viewport meta tag exists', viewport !== null, 'critical');
        this.checkCondition('Viewport prevents scaling', viewport?.content.includes('user-scalable=no'), 'warning');
        
        // Check touch-friendly elements
        const touchElements = document.querySelectorAll('.touch-manipulation, [class*="min-h-"]');
        this.checkCondition('Touch-friendly elements present', touchElements.length > 0, 'critical');
        
        // Check mobile-specific CSS
        const mobileClasses = document.querySelectorAll('[class*="flex-col"], [class*="md:flex-row"]');
        this.checkCondition('Mobile layout classes present', mobileClasses.length > 0, 'warning');
        
        // Check theme color
        const themeColor = document.querySelector('meta[name="theme-color"]');
        this.checkCondition('Theme color meta tag exists', themeColor !== null, 'warning');
        
        // Check PWA readiness
        const appleWebApp = document.querySelector('meta[name="apple-mobile-web-app-capable"]');
        this.checkCondition('Apple web app meta tag exists', appleWebApp !== null, 'warning');
    }

    // Verify data integrity
    async verifyDataIntegrity() {
        console.log('\n💾 Verifying Data Integrity...');
        
        try {
            // Test data structures
            const products = storage.getProducts();
            const categories = storage.getCategories();
            const users = storage.getUsers();
            const purchases = storage.getPurchases();
            const reviews = storage.getReviews();
            
            this.checkCondition('Products array structure', Array.isArray(products), 'critical');
            this.checkCondition('Categories array structure', Array.isArray(categories), 'critical');
            this.checkCondition('Users array structure', Array.isArray(users), 'critical');
            this.checkCondition('Purchases array structure', Array.isArray(purchases), 'critical');
            this.checkCondition('Reviews array structure', Array.isArray(reviews), 'critical');
            
            // Test data relationships
            if (products.length > 0) {
                const product = products[0];
                this.checkCondition('Product has required fields', 
                    product.id && product.title && product.price && product.category, 'critical');
            }
            
            if (categories.length > 0) {
                const category = categories[0];
                this.checkCondition('Category has required fields',
                    category.name && category.icon && category.gradient, 'critical');
            }
            
            // Test localStorage functionality
            const testKey = 'verification_test_' + Date.now();
            const testData = { test: true, timestamp: Date.now() };
            storage.setItem(testKey, testData);
            const retrieved = storage.getItem(testKey);
            this.checkCondition('localStorage read/write works', 
                JSON.stringify(retrieved) === JSON.stringify(testData), 'critical');
            storage.removeItem(testKey);
            
        } catch (error) {
            this.addCriticalIssue('Data integrity error: ' + error.message);
        }
    }

    // Verify security measures
    async verifySecurityMeasures() {
        console.log('\n🔒 Verifying Security Measures...');
        
        // Check buyer-only restrictions
        this.checkCondition('Buyer-only system enforced', 
            !document.querySelector('[data-seller-only]'), 'critical');
        
        // Check password hashing
        this.checkFunction('Storage.hashPassword', storage?.hashPassword, 'critical');
        this.checkFunction('Storage.verifyPassword', storage?.verifyPassword, 'critical');
        
        // Check input validation
        this.checkFunction('Utils.sanitizeInput', Utils?.sanitizeInput, 'warning');
        
        // Check HTTPS readiness (in production)
        if (location.protocol === 'https:') {
            this.checkCondition('HTTPS enabled', true, 'critical');
        } else {
            this.addWarning('Application should be served over HTTPS in production');
        }
        
        // Check for potential XSS vulnerabilities
        const userInputElements = document.querySelectorAll('input, textarea');
        this.checkCondition('User input elements present', userInputElements.length > 0, 'warning');
    }

    // Helper methods
    checkExists(name, condition, severity) {
        this.addResult(name + ' exists', condition, severity);
    }

    checkFunction(name, func, severity) {
        this.addResult(name + ' is function', typeof func === 'function', severity);
    }

    checkCondition(name, condition, severity) {
        this.addResult(name, condition, severity);
    }

    addResult(name, passed, severity) {
        const result = { name, passed, severity, timestamp: new Date().toISOString() };
        this.verificationResults.push(result);
        
        if (!passed) {
            if (severity === 'critical') {
                this.addCriticalIssue(name);
            } else {
                this.addWarning(name);
            }
        }
        
        const icon = passed ? '✅' : (severity === 'critical' ? '❌' : '⚠️');
        console.log(`${icon} ${name}`);
    }

    addCriticalIssue(issue) {
        this.criticalIssues.push(issue);
    }

    addWarning(warning) {
        this.warnings.push(warning);
    }

    // Generate final report
    generateFinalReport() {
        const total = this.verificationResults.length;
        const passed = this.verificationResults.filter(r => r.passed).length;
        const critical = this.verificationResults.filter(r => !r.passed && r.severity === 'critical').length;
        const warnings = this.verificationResults.filter(r => !r.passed && r.severity === 'warning').length;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n' + '='.repeat(60));
        console.log('🎯 PRODUCTION READINESS VERIFICATION REPORT');
        console.log('='.repeat(60));
        console.log(`Total Checks: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Critical Issues: ${critical}`);
        console.log(`Warnings: ${warnings}`);
        console.log(`Success Rate: ${percentage}%`);
        console.log('='.repeat(60));
        
        if (critical === 0 && percentage >= 90) {
            console.log('🎉 PRODUCTION READY! System passed all critical checks.');
        } else if (critical === 0 && percentage >= 80) {
            console.log('✅ MOSTLY READY! Address warnings before production.');
        } else if (critical > 0) {
            console.log('🚨 NOT READY! Critical issues must be resolved.');
        } else {
            console.log('⚠️ NEEDS WORK! Multiple issues require attention.');
        }
        
        if (this.criticalIssues.length > 0) {
            console.log('\n🚨 CRITICAL ISSUES:');
            this.criticalIssues.forEach(issue => console.log(`  - ${issue}`));
        }
        
        if (this.warnings.length > 0) {
            console.log('\n⚠️ WARNINGS:');
            this.warnings.forEach(warning => console.log(`  - ${warning}`));
        }
        
        console.log('\n' + '='.repeat(60));
    }

    // Get final status
    getFinalStatus() {
        const critical = this.criticalIssues.length;
        const total = this.verificationResults.length;
        const passed = this.verificationResults.filter(r => r.passed).length;
        const percentage = Math.round((passed / total) * 100);
        
        return {
            isProductionReady: critical === 0 && percentage >= 90,
            criticalIssues: critical,
            totalChecks: total,
            passedChecks: passed,
            successRate: percentage,
            warnings: this.warnings.length,
            status: critical === 0 ? (percentage >= 90 ? 'READY' : 'MOSTLY_READY') : 'NOT_READY'
        };
    }
}

// Create global instance
const productionVerification = new ProductionVerification();

// Add to window for console access
window.verifyProduction = () => productionVerification.runProductionVerification();

// Auto-run verification when script loads (optional)
console.log('🔍 Production Verification Script Loaded');
console.log('Run window.verifyProduction() to start verification');
