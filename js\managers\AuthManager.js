// Authentication Management System
class AuthManager {
    constructor() {
        this.currentUser = null;
    }

    // Initialize authentication
    init() {
        this.loadCurrentUser();
        this.setupEventListeners();
        this.updateUI();
        this.initializeSocialAuth();
    }

    // Initialize social authentication
    async initializeSocialAuth() {
        // Check for Supabase auth state changes
        if (typeof supabaseIntegration !== 'undefined') {
            try {
                await supabaseIntegration.init();

                // Check for existing session
                if (supabaseIntegration.isConnected()) {
                    const { data: { session } } = await supabaseIntegration.supabase.auth.getSession();
                    if (session && session.user && !this.currentUser) {
                        await this.handleSocialLoginCallback(session.user, session);
                    }
                }
            } catch (error) {
                console.log('Supabase not configured, using localStorage only');
            }
        }
    }

    // Load current user from storage
    loadCurrentUser() {
        this.currentUser = storage.getCurrentUser();
    }

    // Setup event listeners
    setupEventListeners() {
        // Login/Register button handlers
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');

        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                if (this.currentUser) {
                    this.showUserMenu();
                } else {
                    this.showLoginModal();
                }
            });
        }

        if (registerBtn) {
            registerBtn.addEventListener('click', () => this.showRegisterModal());
        }
    }

    // Show login modal
    showLoginModal() {
        const modalContent = `
            <form id="loginForm">
                <div class="mb-6">
                    <label class="block text-sm font-medium text-white/80 mb-3">Email</label>
                    <input type="email" 
                           name="email" 
                           required 
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Enter your email">
                </div>
                
                <div class="mb-8">
                    <label class="block text-sm font-medium text-white/80 mb-3">Password</label>
                    <input type="password" 
                           name="password" 
                           required 
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Enter your password">
                </div>
                
                <button type="submit" class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300">
                    Login
                </button>
            </form>

            <!-- Social Login Section -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-white/10"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-dark-secondary text-white/60">Or continue with</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 gap-3">
                    <button type="button" onclick="authManager.signInWithGoogle()"
                            class="w-full inline-flex justify-center py-3 px-4 border border-white/10 rounded-xl shadow-sm bg-white/5 text-sm font-medium text-white hover:bg-white/10 transition-all duration-300">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continue with Google
                    </button>

                    <button type="button" onclick="authManager.signInWithApple()"
                            class="w-full inline-flex justify-center py-3 px-4 border border-white/10 rounded-xl shadow-sm bg-white/5 text-sm font-medium text-white hover:bg-white/10 transition-all duration-300">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        Continue with Apple
                    </button>
                </div>
            </div>

            <p class="text-center mt-6 text-sm text-white/60">
                Don't have an account?
                <button id="switchToRegister" class="text-cyan-400 hover:text-cyan-300 font-medium transition-colors duration-200">Register here</button>
            </p>
        `;

        this.showModal('loginModal', 'Login to Your Account', modalContent);
        
        // Setup form handler
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e);
        });

        // Setup switch to register
        document.getElementById('switchToRegister').addEventListener('click', () => {
            this.hideModal('loginModal');
            setTimeout(() => this.showRegisterModal(), 100);
        });
    }

    // Show register modal (Buyer-only system)
    showRegisterModal() {
        const modalContent = `
            <form id="registerForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-white/80 mb-3">Full Name</label>
                    <input type="text"
                           name="name"
                           required
                           minlength="2"
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Enter your full name">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-white/80 mb-3">Email Address</label>
                    <input type="email"
                           name="email"
                           required
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Enter your email address">
                    <p class="text-xs text-white/50 mt-1">We'll use this for order confirmations and account recovery</p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-white/80 mb-3">Password</label>
                    <input type="password"
                           name="password"
                           required
                           minlength="${CONFIG.AUTH.PASSWORD_MIN_LENGTH}"
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Create a strong password">
                    <p class="text-xs text-white/50 mt-1">Minimum ${CONFIG.AUTH.PASSWORD_MIN_LENGTH} characters required</p>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-white/80 mb-3">Confirm Password</label>
                    <input type="password"
                           name="confirmPassword"
                           required
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Confirm your password">
                </div>

                <div class="mb-8">
                    <label class="flex items-start">
                        <input type="checkbox"
                               name="agreeTerms"
                               required
                               class="mt-1 mr-3 accent-cyan-400">
                        <span class="text-sm text-white/70 leading-relaxed">
                            I agree to the <span class="text-cyan-400 font-medium">Terms of Service</span> and understand that a <span class="text-cyan-400 font-medium">10% transaction fee</span> applies to all purchases
                        </span>
                    </label>
                </div>

                <button type="submit" class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Buyer Account
                </button>
            </form>

            <!-- Social Registration Section -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-white/10"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-dark-secondary text-white/60">Or sign up with</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 gap-3">
                    <button type="button" onclick="authManager.signInWithGoogle()"
                            class="w-full inline-flex justify-center py-3 px-4 border border-white/10 rounded-xl shadow-sm bg-white/5 text-sm font-medium text-white hover:bg-white/10 transition-all duration-300">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Sign up with Google
                    </button>

                    <button type="button" onclick="authManager.signInWithApple()"
                            class="w-full inline-flex justify-center py-3 px-4 border border-white/10 rounded-xl shadow-sm bg-white/5 text-sm font-medium text-white hover:bg-white/10 transition-all duration-300">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        Sign up with Apple
                    </button>
                </div>
            </div>

            <p class="text-center mt-6 text-sm text-white/60">
                Already have an account?
                <button id="switchToLogin" class="text-cyan-400 hover:text-cyan-300 font-medium transition-colors duration-200">Login here</button>
            </p>
        `;

        this.showModal('registerModal', 'Create Your Account', modalContent);
        
        // Setup form handler
        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister(e);
        });

        // Setup switch to login
        document.getElementById('switchToLogin').addEventListener('click', () => {
            this.hideModal('registerModal');
            setTimeout(() => this.showLoginModal(), 100);
        });
    }

    // Generic modal handler
    showModal(modalId, title, content) {
        let modal = document.getElementById(modalId);
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = modalId;
            modal.innerHTML = Components.createModal(modalId, title, content);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('h3').textContent = title;
            modal.querySelector('.modal-content').innerHTML = content;
        }

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.hideModal(modalId));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal(modalId);
            }
        });

        modal.classList.remove('hidden');
        modal.querySelector('.modal-card').classList.add('modal-enter');
    }

    // Hide modal
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // Handle login
    handleLogin(event) {
        const formData = new FormData(event.target);
        const email = formData.get('email').trim().toLowerCase();
        const password = formData.get('password');

        // Validation
        if (!Utils.validateEmail(email)) {
            Components.createNotification('Please enter a valid email address', 'error');
            return;
        }

        if (!password) {
            Components.createNotification('Password is required', 'error');
            return;
        }

        // Check user credentials
        const user = storage.getUserByEmail(email);
        if (!user) {
            Components.createNotification('No account found with this email address', 'error');
            return;
        }

        // Verify password
        if (!storage.verifyPassword(password, user.passwordHash)) {
            Components.createNotification('Invalid password', 'error');
            return;
        }

        // Login successful
        this.currentUser = user;
        storage.setCurrentUser(user);
        this.updateUI();
        this.hideModal('loginModal');

        // Update cart UI after login
        cartManager.updateCartUI();

        Components.createNotification(`Welcome back, ${user.name}!`, 'success');
    }

    // Handle registration
    handleRegister(event) {
        const formData = new FormData(event.target);
        const userData = {
            name: formData.get('name').trim(),
            email: formData.get('email').trim().toLowerCase(),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword')
        };

        // Validation
        if (!userData.name || userData.name.length < 2) {
            Components.createNotification('Please enter a valid full name (minimum 2 characters)', 'error');
            return;
        }

        if (!Utils.validateEmail(userData.email)) {
            Components.createNotification('Please enter a valid email address', 'error');
            return;
        }

        if (userData.password !== userData.confirmPassword) {
            Components.createNotification('Passwords do not match', 'error');
            return;
        }

        const passwordValidation = Utils.validatePassword(userData.password);
        if (!passwordValidation.isValid) {
            Components.createNotification(`Password must be at least ${CONFIG.AUTH.PASSWORD_MIN_LENGTH} characters long`, 'error');
            return;
        }

        // Check if user already exists
        if (storage.getUserByEmail(userData.email)) {
            Components.createNotification('An account with this email already exists', 'error');
            return;
        }

        // Create user (buyer-only system)
        const newUser = storage.addUser(userData);
        this.currentUser = newUser;
        storage.setCurrentUser(newUser);

        this.updateUI();
        this.hideModal('registerModal');

        // Update cart UI after registration
        cartManager.updateCartUI();

        Components.createNotification(`Welcome to Obu Marketplace, ${newUser.name}! Your buyer account has been created successfully.`, 'success');
    }

    // Show user menu
    showUserMenu() {
        const existingMenu = document.querySelector('.user-dropdown');
        if (existingMenu) {
            existingMenu.remove();
            return;
        }

        const menu = Utils.createElement('div', 'user-dropdown absolute right-0 mt-2 w-48 bg-dark-secondary/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-lg z-50');
        menu.innerHTML = `
            <div class="py-2">
                <div class="px-4 py-2 border-b border-white/10">
                    <div class="text-sm font-medium text-white">${this.currentUser.name}</div>
                    <div class="text-xs text-white/60">${this.currentUser.email}</div>
                    <div class="text-xs text-cyan-400">Buyer Account</div>
                    <div class="text-xs text-white/50">Member since ${Utils.formatDate(this.currentUser.createdAt)}</div>
                </div>
                <a href="#" onclick="authManager.showProfile()" class="block px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/5 transition-colors">
                    <i class="fas fa-user mr-2"></i>Profile Settings
                </a>
                <a href="#" onclick="cartManager.showCart()" class="block px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/5 transition-colors">
                    <i class="fas fa-shopping-cart mr-2"></i>My Cart
                </a>
                <a href="#" onclick="authManager.showPurchaseHistory()" class="block px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/5 transition-colors">
                    <i class="fas fa-receipt mr-2"></i>My Purchases
                </a>
                <hr class="my-2 border-white/10">
                <a href="#" onclick="authManager.logout()" class="block px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-400/10 transition-colors">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </a>
            </div>
        `;

        // Position menu
        const loginBtn = document.getElementById('loginBtn');
        loginBtn.parentElement.style.position = 'relative';
        loginBtn.parentElement.appendChild(menu);

        // Remove menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function removeMenu(e) {
                if (!menu.contains(e.target) && !loginBtn.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', removeMenu);
                }
            });
        }, 100);
    }

    // Logout user
    logout() {
        this.currentUser = null;
        storage.clearCurrentUser();
        this.updateUI();
        
        // Clear cart
        storage.clearCart();
        CartManager.updateCartUI();
        
        Components.createNotification('Logged out successfully', 'success');
        
        // Remove user menu if open
        const userMenu = document.querySelector('.user-dropdown');
        if (userMenu) {
            userMenu.remove();
        }
    }

    // Update UI based on authentication state
    updateUI() {
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');

        if (this.currentUser) {
            if (loginBtn) {
                loginBtn.textContent = this.currentUser.name;
                loginBtn.onclick = () => this.showUserMenu();
            }
            if (registerBtn) {
                registerBtn.style.display = 'none';
            }
        } else {
            if (loginBtn) {
                loginBtn.textContent = 'Login';
                loginBtn.onclick = () => this.showLoginModal();
            }
            if (registerBtn) {
                registerBtn.style.display = 'block';
            }
        }
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Check if user has specific account type
    hasAccountType(type) {
        if (!this.currentUser) return false;
        return this.currentUser.accountType === type || this.currentUser.accountType === 'both';
    }

    // Require authentication for actions
    requireAuth(callback, message = 'Please login to continue') {
        if (this.isLoggedIn()) {
            callback();
        } else {
            Components.createNotification(message, 'warning');
            this.showLoginModal();
        }
    }

    // Require specific account type (not needed in buyer-only system)
    requireAccountType(type, callback, message = `This action requires a ${type} account`) {
        this.requireAuth(() => {
            if (this.hasAccountType(type)) {
                callback();
            } else {
                Components.createNotification(message, 'warning');
            }
        });
    }

    // Show purchase history
    showPurchaseHistory() {
        if (!this.isLoggedIn()) {
            Components.createNotification('Please login to view your purchases', 'warning');
            this.showLoginModal();
            return;
        }

        const purchases = storage.getPurchasesByUser(this.currentUser.id);

        if (purchases.length === 0) {
            this.showEmptyPurchaseHistory();
            return;
        }

        // Sort purchases by date (newest first)
        purchases.sort((a, b) => new Date(b.purchaseDate) - new Date(a.purchaseDate));

        const modalContent = `
            <div class="purchase-history max-h-96 overflow-y-auto">
                <div class="space-y-4">
                    ${purchases.map(purchase => this.createPurchaseCard(purchase)).join('')}
                </div>
            </div>

            <div class="mt-6 pt-4 border-t border-white/10">
                <div class="flex justify-between items-center text-sm text-white/60">
                    <span>Total Purchases: ${purchases.length}</span>
                    <span>Total Spent: ${Utils.formatPrice(purchases.reduce((sum, p) => sum + p.totalAmount, 0))}</span>
                </div>
            </div>
        `;

        this.showModal('purchaseHistoryModal', `My Purchases (${purchases.length})`, modalContent);
    }

    // Create purchase card
    createPurchaseCard(purchase) {
        const product = storage.getProductById(purchase.productId);
        const canDownload = purchase.downloadAttempts < purchase.maxDownloads;
        const hasReviewed = storage.hasUserReviewedProduct(purchase.buyerId, purchase.productId);
        const canReview = !hasReviewed && purchase.status === 'completed';

        return `
            <div class="purchase-card bg-dark-primary/30 rounded-xl p-4 border border-white/10">
                <div class="flex items-start space-x-4">
                    <img src="${product?.image || Utils.createImagePlaceholder(60, 60)}"
                         alt="${purchase.productTitle}"
                         class="w-15 h-15 rounded-lg object-cover">

                    <div class="flex-1">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-white">${purchase.productTitle}</h4>
                            <span class="px-2 py-1 bg-green-400/20 text-green-400 rounded-full text-xs font-semibold">
                                ${purchase.status}
                            </span>
                        </div>

                        <div class="text-sm text-white/60 mb-2">
                            <i class="fas fa-calendar mr-1"></i>
                            ${Utils.formatDateTime(purchase.purchaseDate)}
                        </div>

                        <div class="flex justify-between items-center mb-3">
                            <div class="text-sm">
                                <div class="text-white/80">Subtotal: ${Utils.formatPrice(purchase.subtotal)}</div>
                                <div class="text-white/60">Tax (10%): ${Utils.formatPrice(purchase.tax)}</div>
                                <div class="text-white font-semibold">Total: ${Utils.formatPrice(purchase.totalAmount)}</div>
                            </div>
                            <div class="text-right text-sm text-white/60">
                                Order #${purchase.id.slice(-8).toUpperCase()}
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div class="text-xs text-white/50">
                                Downloads: ${purchase.downloadAttempts}/${purchase.maxDownloads}
                                ${purchase.lastDownload ? `• Last: ${Utils.timeAgo(purchase.lastDownload)}` : ''}
                            </div>

                            <div class="flex space-x-2">
                                ${product && canDownload ? `
                                    <button onclick="authManager.downloadProduct('${purchase.id}')"
                                            class="btn-primary px-3 py-2 rounded-lg text-xs font-semibold">
                                        <i class="fas fa-download mr-1"></i>
                                        Download
                                    </button>
                                ` : ''}

                                ${canReview ? `
                                    <button class="write-review-btn btn-secondary px-3 py-2 rounded-lg text-xs font-semibold"
                                            data-purchase-id="${purchase.id}">
                                        <i class="fas fa-star mr-1"></i>
                                        Write Review
                                    </button>
                                ` : hasReviewed ? `
                                    <span class="text-xs text-green-400 px-3 py-2">
                                        <i class="fas fa-check mr-1"></i>
                                        Reviewed
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Show empty purchase history
    showEmptyPurchaseHistory() {
        const modalContent = `
            <div class="text-center py-12">
                <i class="fas fa-receipt text-6xl text-white/20 mb-6"></i>
                <h3 class="text-2xl font-semibold text-white mb-4">No purchases yet</h3>
                <p class="text-white/60 mb-6">Start shopping to see your purchase history here!</p>
                <button onclick="document.getElementById('purchaseHistoryModal').classList.add('hidden')"
                        class="btn-primary px-6 py-3 rounded-xl font-semibold">
                    Start Shopping
                </button>
            </div>
        `;

        this.showModal('purchaseHistoryModal', 'My Purchases', modalContent);
    }

    // Download product
    downloadProduct(purchaseId) {
        const purchase = storage.getPurchases().find(p => p.id === purchaseId);
        if (!purchase) {
            Components.createNotification('Purchase not found', 'error');
            return;
        }

        const product = storage.getProductById(purchase.productId);
        if (!product) {
            Components.createNotification('Product no longer available', 'error');
            return;
        }

        if (purchase.downloadAttempts >= purchase.maxDownloads) {
            Components.createNotification('Download limit reached for this purchase', 'error');
            return;
        }

        // Record download attempt
        if (storage.recordDownload(purchaseId)) {
            // Simulate download
            Components.createNotification('Download started! Check your downloads folder.', 'success');

            // In a real app, you would redirect to the actual download URL
            if (product.downloadUrl) {
                // window.open(product.downloadUrl, '_blank');
                console.log('Download URL:', product.downloadUrl);
            }

            // Refresh purchase history if modal is open
            const modal = document.getElementById('purchaseHistoryModal');
            if (modal && !modal.classList.contains('hidden')) {
                setTimeout(() => this.showPurchaseHistory(), 500);
            }
        } else {
            Components.createNotification('Download failed. Please try again.', 'error');
        }
    }

    // Show profile settings
    showProfile() {
        const modalContent = `
            <div class="profile-settings">
                <div class="mb-6">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white">${this.currentUser.name}</h3>
                            <p class="text-white/60">${this.currentUser.email}</p>
                            <p class="text-xs text-cyan-400">Buyer Account</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="bg-dark-primary/30 rounded-xl p-4">
                        <h4 class="font-semibold text-white mb-2">Account Information</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-white/60">Member Since:</span>
                                <span class="text-white">${Utils.formatDate(this.currentUser.createdAt)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-white/60">Last Login:</span>
                                <span class="text-white">${this.currentUser.lastLogin ? Utils.formatDateTime(this.currentUser.lastLogin) : 'First time'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-white/60">Total Purchases:</span>
                                <span class="text-white">${storage.getPurchasesByUser(this.currentUser.id).length}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-dark-primary/30 rounded-xl p-4">
                        <h4 class="font-semibold text-white mb-2">Quick Actions</h4>
                        <div class="space-y-2">
                            <button onclick="authManager.showPurchaseHistory(); document.getElementById('profileModal').classList.add('hidden')"
                                    class="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/5 rounded-lg transition-colors">
                                <i class="fas fa-receipt mr-2"></i>View Purchase History
                            </button>
                            <button onclick="cartManager.showCart(); document.getElementById('profileModal').classList.add('hidden')"
                                    class="w-full text-left px-3 py-2 text-white/80 hover:text-white hover:bg-white/5 rounded-lg transition-colors">
                                <i class="fas fa-shopping-cart mr-2"></i>View Shopping Cart
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-4 border-t border-white/10">
                    <button onclick="document.getElementById('profileModal').classList.add('hidden')"
                            class="btn-secondary w-full py-3 rounded-xl font-semibold">
                        Close
                    </button>
                </div>
            </div>
        `;

        this.showModal('profileModal', 'Profile Settings', modalContent);
    }

    // Social Authentication Methods
    async signInWithGoogle() {
        try {
            // Check if Supabase integration is available
            if (typeof supabaseIntegration !== 'undefined' && supabaseIntegration.isConnected()) {
                const result = await supabaseIntegration.signInWithGoogle();

                if (result.success) {
                    Components.createNotification('Redirecting to Google...', 'info');
                } else {
                    throw new Error(result.error);
                }
            } else {
                // Fallback for localStorage-only mode
                Components.createNotification('Social login requires Supabase configuration', 'warning');
                this.showRegisterModal();
            }
        } catch (error) {
            console.error('Google sign-in error:', error);
            Components.createNotification('Google sign-in failed. Please try again.', 'error');
        }
    }

    async signInWithApple() {
        try {
            // Check if Supabase integration is available
            if (typeof supabaseIntegration !== 'undefined' && supabaseIntegration.isConnected()) {
                const result = await supabaseIntegration.signInWithApple();

                if (result.success) {
                    Components.createNotification('Redirecting to Apple...', 'info');
                } else {
                    throw new Error(result.error);
                }
            } else {
                // Fallback for localStorage-only mode
                Components.createNotification('Social login requires Supabase configuration', 'warning');
                this.showRegisterModal();
            }
        } catch (error) {
            console.error('Apple sign-in error:', error);
            Components.createNotification('Apple sign-in failed. Please try again.', 'error');
        }
    }

    // Handle social login callback
    async handleSocialLoginCallback(user, session) {
        try {
            // Check if user profile exists
            let userProfile = await supabaseManager.syncUserProfile();

            if (!userProfile) {
                // Create user profile for social login
                const userData = {
                    name: user.user_metadata?.full_name || user.email.split('@')[0],
                    email: user.email,
                    accountType: 'buyer'
                };

                await supabaseManager.createUserProfile(user, userData);
                userProfile = await supabaseManager.syncUserProfile();
            }

            // Set current user
            this.currentUser = {
                id: user.id,
                name: userProfile.name,
                email: userProfile.email,
                accountType: 'buyer',
                createdAt: userProfile.created_at,
                lastLogin: new Date().toISOString()
            };

            // Update localStorage for compatibility
            storage.setCurrentUser(this.currentUser);

            this.updateUI();
            cartManager.updateCartUI();

            Components.createNotification(`Welcome, ${this.currentUser.name}!`, 'success');

        } catch (error) {
            console.error('Social login callback error:', error);
            Components.createNotification('Login completed but profile setup failed', 'error');
        }
    }

    // Enhanced logout for social auth
    async logout() {
        try {
            // Sign out from Supabase if connected
            if (typeof supabaseIntegration !== 'undefined' && supabaseIntegration.isConnected()) {
                await supabaseIntegration.signOut();
            }

            // Clear local state
            this.currentUser = null;
            storage.clearCurrentUser();
            this.updateUI();

            // Clear cart
            storage.clearCart();
            cartManager.updateCartUI();

            Components.createNotification('Logged out successfully', 'success');

            // Remove user menu if open
            const userMenu = document.querySelector('.user-dropdown');
            if (userMenu) {
                userMenu.remove();
            }
        } catch (error) {
            console.error('Logout error:', error);
            // Still clear local state even if Supabase logout fails
            this.currentUser = null;
            storage.clearCurrentUser();
            this.updateUI();
            Components.createNotification('Logged out locally', 'warning');
        }
    }

    // Verify all authentication methods work correctly
    verifyAuthSystem() {
        const tests = {
            hasCurrentUser: this.currentUser !== null,
            hasValidSession: storage.getCurrentUser() !== null,
            canShowModals: typeof this.showLoginModal === 'function',
            canValidatePasswords: typeof Utils.validatePassword === 'function',
            canValidateEmails: typeof Utils.validateEmail === 'function'
        };

        console.log('🔐 Auth System Verification:', tests);
        return Object.values(tests).every(test => test);
    }
}

// Create global instance
const authManager = new AuthManager();
