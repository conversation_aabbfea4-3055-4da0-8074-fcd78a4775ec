// Cart Management System
class CartManager {
    constructor() {
        this.cart = [];
    }

    // Initialize cart management
    init() {
        this.loadCart();
        this.updateCartUI();
        this.setupEventListeners();
    }

    // Load cart from storage
    loadCart() {
        this.cart = storage.getCart();
    }

    // Setup event listeners
    setupEventListeners() {
        // Cart button click
        const cartBtn = document.querySelector('.cart-btn');
        if (cartBtn) {
            cartBtn.addEventListener('click', () => this.showCart());
        }
    }

    // Add item to cart with animation support
    addToCart(productId, quantity = 1, sourceElement = null) {
        if (!authManager.isLoggedIn()) {
            Components.createNotification('Please login to add items to cart', 'warning');
            authManager.showLoginModal();
            return;
        }

        const product = storage.getProductById(productId);
        if (!product) {
            Components.createNotification('Product not found', 'error');
            return;
        }

        if (product.stock <= 0) {
            Components.createNotification('Product is out of stock', 'error');
            return;
        }

        // Check if adding quantity exceeds stock
        const existingItem = this.cart.find(item => item.productId === productId);
        const currentQuantity = existingItem ? existingItem.quantity : 0;

        if (currentQuantity + quantity > product.stock) {
            Components.createNotification(`Only ${product.stock} items available in stock`, 'error');
            return;
        }

        this.cart = storage.addToCart(productId, quantity);
        this.updateCartUI();

        // Trigger cart animation if available and source element provided
        if (typeof cartAnimations !== 'undefined' && sourceElement) {
            cartAnimations.animateAddToCart(sourceElement, product);
        } else {
            // Fallback notification
            Components.createNotification('Item added to cart!', 'success');
        }
    }

    // Remove item from cart
    removeFromCart(productId) {
        this.cart = storage.removeFromCart(productId);
        this.updateCartUI();
        Components.createNotification('Item removed from cart', 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && !cartModal.classList.contains('hidden')) {
            this.renderCartModal();
        }
    }

    // Update item quantity
    updateQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }

        const product = storage.getProductById(productId);
        if (newQuantity > product.stock) {
            Components.createNotification(`Only ${product.stock} items available`, 'error');
            return;
        }

        const itemIndex = this.cart.findIndex(item => item.productId === productId);
        if (itemIndex !== -1) {
            this.cart[itemIndex].quantity = newQuantity;
            storage.setCart(this.cart);
            this.updateCartUI();
            
            // Refresh cart modal if open
            const cartModal = document.getElementById('cartModal');
            if (cartModal && !cartModal.classList.contains('hidden')) {
                this.renderCartModal();
            }
        }
    }

    // Clear entire cart
    clearCart() {
        this.cart = storage.clearCart();
        this.updateCartUI();
        Components.createNotification('Cart cleared', 'info');
    }

    // Update cart UI (badge, etc.)
    updateCartUI() {
        const cartBtn = document.querySelector('.cart-btn');
        if (!cartBtn) return;

        const existingBadge = cartBtn.querySelector('.notification-badge');
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);

        if (totalItems > 0) {
            if (!existingBadge) {
                const badge = Utils.createElement('span', 'notification-badge');
                badge.textContent = totalItems;
                cartBtn.appendChild(badge);
            } else {
                existingBadge.textContent = totalItems;
            }
        } else if (existingBadge) {
            existingBadge.remove();
        }
    }

    // Show cart modal
    showCart() {
        if (!authManager.isLoggedIn()) {
            Components.createNotification('Please login to view your cart', 'warning');
            authManager.showLoginModal();
            return;
        }

        this.renderCartModal();
    }

    // Render cart modal
    renderCartModal() {
        this.loadCart(); // Refresh cart data

        if (this.cart.length === 0) {
            this.showEmptyCart();
            return;
        }

        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        // Calculate totals with currency conversion
        const currentCurrency = typeof currencyManager !== 'undefined' ? currencyManager.getCurrentCurrency() : 'USD';
        const subtotal = cartItems.reduce((sum, { item, product }) => {
            let productPrice = product.price;
            if (typeof currencyManager !== 'undefined') {
                productPrice = currencyManager.convertToCurrentCurrency(product.price, 'USD');
            }
            return sum + (productPrice * item.quantity);
        }, 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;

        const modalContent = `
            <div class="cart-modal">
                <div class="max-h-96 overflow-y-auto mb-6">
                    <div class="space-y-4">
                        ${cartItems.map(({ item, product }) => Components.createCartItem(item, product)).join('')}
                    </div>
                </div>
                
                <div class="border-t border-white/10 pt-6">
                    <div class="space-y-2 mb-6">
                        <div class="flex justify-between text-white/80">
                            <span>Subtotal:</span>
                            <span>${this.formatPrice(subtotal)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Tax (10%):</span>
                            <span>${this.formatPrice(tax)}</span>
                        </div>
                        <div class="flex justify-between text-xl font-bold text-white border-t border-white/10 pt-2">
                            <span>Total:</span>
                            <span class="text-cyan-400">${this.formatPrice(total)}</span>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <button onclick="cartManager.proceedToPayMongoCheckout()"
                                class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300">
                            <i class="fas fa-credit-card mr-2"></i>
                            Pay with PayMongo
                        </button>
                        <div class="flex space-x-4">
                            <button onclick="cartManager.proceedToCheckout()"
                                    class="btn-secondary flex-1 py-3 rounded-xl font-semibold">
                                <i class="fas fa-wallet mr-2"></i>
                                Traditional Checkout
                            </button>
                            <button onclick="cartManager.clearCart(); document.getElementById('cartModal').classList.add('hidden')"
                                    class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                                Clear Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal('cartModal', `Shopping Cart (${this.cart.length} items)`, modalContent);
    }

    // Show empty cart
    showEmptyCart() {
        const modalContent = `
            <div class="text-center py-12">
                <i class="fas fa-shopping-cart text-6xl text-white/20 mb-6"></i>
                <h3 class="text-2xl font-semibold text-white mb-4">Your cart is empty</h3>
                <p class="text-white/60 mb-6">Add some products to get started!</p>
                <button onclick="document.getElementById('cartModal').classList.add('hidden')" 
                        class="btn-primary px-6 py-3 rounded-xl font-semibold">
                    Continue Shopping
                </button>
            </div>
        `;

        this.showModal('cartModal', 'Shopping Cart', modalContent);
    }

    // Generic modal handler
    showModal(modalId, title, content) {
        let modal = document.getElementById(modalId);
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = modalId;
            modal.innerHTML = Components.createModal(modalId, title, content);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('h3').textContent = title;
            modal.querySelector('.modal-content').innerHTML = content;
        }

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
        modal.querySelector('.modal-card').classList.add('modal-enter');
    }

    // Proceed to checkout
    proceedToCheckout() {
        if (this.cart.length === 0) {
            Components.createNotification('Your cart is empty', 'warning');
            return;
        }

        // Validate stock availability
        const unavailableItems = [];
        for (const cartItem of this.cart) {
            const product = storage.getProductById(cartItem.productId);
            if (!product || product.stock < cartItem.quantity) {
                unavailableItems.push(product ? product.title : 'Unknown Product');
            }
        }

        if (unavailableItems.length > 0) {
            Components.createNotification(
                `Some items are no longer available: ${unavailableItems.join(', ')}`, 
                'error'
            );
            this.renderCartModal(); // Refresh cart to show updated stock
            return;
        }

        this.showCheckoutModal();
    }

    // Show checkout modal
    showCheckoutModal() {
        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        const subtotal = cartItems.reduce((sum, { item, product }) => sum + (product.price * item.quantity), 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;

        const modalContent = `
            <div class="checkout-modal">
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-white mb-4">Order Summary</h4>
                    <div class="bg-dark-primary/30 rounded-xl p-4 mb-4 max-h-48 overflow-y-auto">
                        ${cartItems.map(({ item, product }) => `
                            <div class="flex justify-between items-center py-2 border-b border-white/10 last:border-b-0">
                                <div>
                                    <div class="text-white font-medium">${product.title}</div>
                                    <div class="text-sm text-white/60">Qty: ${item.quantity} × ${Utils.formatPrice(product.price)}</div>
                                </div>
                                <div class="text-white font-semibold">
                                    ${Utils.formatPrice(product.price * item.quantity)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-white/80">
                            <span>Subtotal:</span>
                            <span>${Utils.formatPrice(subtotal)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Tax (10%):</span>
                            <span>${Utils.formatPrice(tax)}</span>
                        </div>
                        <div class="flex justify-between text-xl font-bold text-white border-t border-white/10 pt-2">
                            <span>Total:</span>
                            <span class="text-cyan-400">${Utils.formatPrice(total)}</span>
                        </div>
                    </div>
                </div>
                
                <form id="checkoutForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/80 mb-3">Payment Method</label>
                        <select name="paymentMethod" 
                                required 
                                class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            <option value="" class="bg-dark-primary text-white">Select payment method</option>
                            <option value="credit_card" class="bg-dark-primary text-white">Credit Card</option>
                            <option value="paypal" class="bg-dark-primary text-white">PayPal</option>
                            <option value="crypto" class="bg-dark-primary text-white">Cryptocurrency</option>
                        </select>
                    </div>
                    
                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" 
                                   name="agreeTerms" 
                                   required 
                                   class="mt-1 mr-3 accent-cyan-400">
                            <span class="text-sm text-white/70 leading-relaxed">
                                I agree to the terms of service and understand that a 10% transaction fee is included in the total price.
                            </span>
                        </label>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                            <i class="fas fa-lock mr-2"></i>
                            Complete Order
                        </button>
                        <button type="button" 
                                onclick="document.getElementById('checkoutModal').classList.add('hidden')" 
                                class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        `;

        // Hide cart modal
        const cartModal = document.getElementById('cartModal');
        if (cartModal) {
            cartModal.classList.add('hidden');
        }

        this.showModal('checkoutModal', 'Checkout', modalContent);

        // Setup form handler
        document.getElementById('checkoutForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processOrder(e);
        });
    }

    // Process order (Buyer-only system)
    processOrder(event) {
        const formData = new FormData(event.target);
        const paymentMethod = formData.get('paymentMethod');

        if (!paymentMethod) {
            Components.createNotification('Please select a payment method', 'error');
            return;
        }

        // Simulate order processing
        const loadingBtn = event.target.querySelector('button[type="submit"]');
        const originalText = loadingBtn.innerHTML;
        loadingBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing Payment...';
        loadingBtn.disabled = true;

        setTimeout(() => {
            try {
                // Create individual purchases for each cart item
                const purchases = [];

                this.cart.forEach(cartItem => {
                    const product = storage.getProductById(cartItem.productId);
                    if (!product) return;

                    // Create separate purchase for each quantity
                    for (let i = 0; i < cartItem.quantity; i++) {
                        const subtotal = product.price;
                        const tax = Utils.calculateTax(subtotal);
                        const totalAmount = subtotal + tax;

                        const purchase = storage.addPurchase({
                            buyerId: authManager.currentUser.id,
                            buyerName: authManager.currentUser.name,
                            buyerEmail: authManager.currentUser.email,
                            productId: product.id,
                            productTitle: product.title,
                            productImage: product.image,
                            subtotal,
                            tax,
                            totalAmount,
                            paymentMethod,
                            status: 'completed',
                            downloadUrl: product.downloadUrl || null,
                            instant: product.instant || false
                        });

                        purchases.push(purchase);
                    }

                    // Update product stock
                    storage.updateProduct(cartItem.productId, {
                        stock: product.stock - cartItem.quantity
                    });
                });

                // Clear cart
                this.clearCart();

                // Close modals
                document.getElementById('checkoutModal').classList.add('hidden');

                // Show success message with purchase details
                const totalAmount = purchases.reduce((sum, p) => sum + p.totalAmount, 0);
                Components.createNotification(
                    `Payment successful! ${purchases.length} item(s) purchased for ${Utils.formatPrice(totalAmount)}`,
                    'success'
                );

                // Show purchase confirmation modal
                setTimeout(() => {
                    this.showPurchaseConfirmation(purchases);
                }, 1000);

            } catch (error) {
                console.error('Purchase processing error:', error);
                Components.createNotification('Purchase failed. Please try again.', 'error');
            }

            // Reset button
            loadingBtn.innerHTML = originalText;
            loadingBtn.disabled = false;

        }, 2500); // Slightly longer for more realistic payment processing
    }

    // Show purchase confirmation
    showPurchaseConfirmation(purchases) {
        const totalAmount = purchases.reduce((sum, p) => sum + p.totalAmount, 0);
        const instantItems = purchases.filter(p => p.instant);

        const modalContent = `
            <div class="purchase-confirmation text-center">
                <div class="mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-white text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-2">Purchase Successful!</h3>
                    <p class="text-white/70">Thank you for your purchase. Your items are ready!</p>
                </div>

                <div class="bg-dark-primary/30 rounded-xl p-6 mb-6">
                    <div class="space-y-3">
                        <div class="flex justify-between text-white/80">
                            <span>Items Purchased:</span>
                            <span class="font-semibold">${purchases.length}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Total Amount:</span>
                            <span class="font-semibold text-green-400">${Utils.formatPrice(totalAmount)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Payment Method:</span>
                            <span class="font-semibold capitalize">${purchases[0].paymentMethod.replace('_', ' ')}</span>
                        </div>
                        ${instantItems.length > 0 ? `
                            <div class="flex justify-between text-white/80">
                                <span>Instant Items:</span>
                                <span class="font-semibold text-cyan-400">${instantItems.length} ready for download</span>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <div class="space-y-4">
                    <button onclick="authManager.showPurchaseHistory(); document.getElementById('purchaseConfirmationModal').classList.add('hidden')"
                            class="btn-primary w-full py-3 rounded-xl font-semibold">
                        <i class="fas fa-receipt mr-2"></i>
                        View My Purchases
                    </button>

                    <button onclick="document.getElementById('purchaseConfirmationModal').classList.add('hidden')"
                            class="btn-secondary w-full py-3 rounded-xl font-semibold">
                        Continue Shopping
                    </button>
                </div>

                <p class="text-xs text-white/50 mt-4">
                    A confirmation email has been sent to ${authManager.currentUser.email}
                </p>
            </div>
        `;

        this.showModal('purchaseConfirmationModal', 'Purchase Complete', modalContent);
    }

    // Format price with currency support
    formatPrice(amount) {
        if (typeof currencyManager !== 'undefined') {
            return currencyManager.formatCurrency(amount);
        }
        return Utils.formatPrice(amount);
    }

    // Get cart summary with currency support
    getCartSummary() {
        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        const currentCurrency = typeof currencyManager !== 'undefined' ? currencyManager.getCurrentCurrency() : 'USD';
        const subtotal = cartItems.reduce((sum, { item, product }) => {
            let productPrice = product.price;
            if (typeof currencyManager !== 'undefined') {
                productPrice = currencyManager.convertToCurrentCurrency(product.price, 'USD');
            }
            return sum + (productPrice * item.quantity);
        }, 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;
        const itemCount = this.cart.reduce((sum, item) => sum + item.quantity, 0);

        return {
            items: cartItems,
            subtotal,
            tax,
            total,
            currency: currentCurrency,
            itemCount
        };
    }

    // Proceed to PayMongo checkout
    proceedToPayMongoCheckout() {
        if (this.cart.length === 0) {
            Components.createNotification('Your cart is empty', 'warning');
            return;
        }

        if (!authManager.isLoggedIn()) {
            Components.createNotification('Please login to continue with checkout', 'warning');
            authManager.showLoginModal();
            return;
        }

        // Check if PayMongo is available
        if (typeof payMongoService === 'undefined' || !payMongoService.isAvailable()) {
            Components.createNotification('PayMongo payment service is not available. Please try traditional checkout.', 'warning');
            this.proceedToCheckout();
            return;
        }

        const summary = this.getCartSummary();

        // Hide cart modal
        document.getElementById('cartModal').classList.add('hidden');

        // Show PayMongo checkout
        payMongoCheckout.showCheckoutModal(summary);
    }

    // Show purchase confirmation modal
    showPurchaseConfirmation(purchases) {
        const modalContent = `
            <div class="text-center py-8">
                <div class="mb-6">
                    <div class="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-3xl text-green-400"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-2">Payment Successful!</h3>
                    <p class="text-white/70">Your purchase has been completed successfully.</p>
                </div>

                <div class="bg-dark-primary/30 rounded-xl p-4 mb-6">
                    <h4 class="text-lg font-semibold text-white mb-4">Purchase Details</h4>
                    <div class="space-y-3">
                        ${purchases.map(purchase => `
                            <div class="flex justify-between items-center py-2 border-b border-white/10 last:border-b-0">
                                <div class="text-left">
                                    <div class="text-white font-medium">${purchase.productTitle}</div>
                                    <div class="text-sm text-white/60">Order #${purchase.id.substring(0, 8)}</div>
                                </div>
                                <div class="text-white font-semibold">
                                    ${this.formatPrice(purchase.totalAmount)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="flex space-x-4">
                    <button onclick="document.getElementById('purchaseConfirmationModal').classList.add('hidden')"
                            class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        Continue Shopping
                    </button>
                    <button onclick="authManager.showPurchaseHistory()"
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        View Orders
                    </button>
                </div>
            </div>
        `;

        this.showModal('purchaseConfirmationModal', 'Purchase Complete', modalContent);
    }
}

// Create global instance
const cartManager = new CartManager();
