// Cart Management System
class CartManager {
    constructor() {
        this.cart = [];
    }

    // Initialize cart management
    init() {
        this.loadCart();
        this.updateCartUI();
        this.setupEventListeners();
    }

    // Load cart from storage
    loadCart() {
        this.cart = storage.getCart();
    }

    // Setup event listeners
    setupEventListeners() {
        // Cart button click
        const cartBtn = document.querySelector('.cart-btn');
        if (cartBtn) {
            cartBtn.addEventListener('click', () => this.showCart());
        }
    }

    // Add item to cart
    addToCart(productId, quantity = 1) {
        if (!authManager.isLoggedIn()) {
            Components.createNotification('Please login to add items to cart', 'warning');
            authManager.showLoginModal();
            return;
        }

        const product = storage.getProductById(productId);
        if (!product) {
            Components.createNotification('Product not found', 'error');
            return;
        }

        if (product.stock <= 0) {
            Components.createNotification('Product is out of stock', 'error');
            return;
        }

        // Check if adding quantity exceeds stock
        const existingItem = this.cart.find(item => item.productId === productId);
        const currentQuantity = existingItem ? existingItem.quantity : 0;
        
        if (currentQuantity + quantity > product.stock) {
            Components.createNotification(`Only ${product.stock} items available in stock`, 'error');
            return;
        }

        this.cart = storage.addToCart(productId, quantity);
        this.updateCartUI();
        Components.createNotification('Item added to cart!', 'success');
    }

    // Remove item from cart
    removeFromCart(productId) {
        this.cart = storage.removeFromCart(productId);
        this.updateCartUI();
        Components.createNotification('Item removed from cart', 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && !cartModal.classList.contains('hidden')) {
            this.renderCartModal();
        }
    }

    // Update item quantity
    updateQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }

        const product = storage.getProductById(productId);
        if (newQuantity > product.stock) {
            Components.createNotification(`Only ${product.stock} items available`, 'error');
            return;
        }

        const itemIndex = this.cart.findIndex(item => item.productId === productId);
        if (itemIndex !== -1) {
            this.cart[itemIndex].quantity = newQuantity;
            storage.setCart(this.cart);
            this.updateCartUI();
            
            // Refresh cart modal if open
            const cartModal = document.getElementById('cartModal');
            if (cartModal && !cartModal.classList.contains('hidden')) {
                this.renderCartModal();
            }
        }
    }

    // Clear entire cart
    clearCart() {
        this.cart = storage.clearCart();
        this.updateCartUI();
        Components.createNotification('Cart cleared', 'info');
    }

    // Update cart UI (badge, etc.)
    updateCartUI() {
        const cartBtn = document.querySelector('.cart-btn');
        if (!cartBtn) return;

        const existingBadge = cartBtn.querySelector('.notification-badge');
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);

        if (totalItems > 0) {
            if (!existingBadge) {
                const badge = Utils.createElement('span', 'notification-badge');
                badge.textContent = totalItems;
                cartBtn.appendChild(badge);
            } else {
                existingBadge.textContent = totalItems;
            }
        } else if (existingBadge) {
            existingBadge.remove();
        }
    }

    // Show cart modal
    showCart() {
        if (!authManager.isLoggedIn()) {
            Components.createNotification('Please login to view your cart', 'warning');
            authManager.showLoginModal();
            return;
        }

        this.renderCartModal();
    }

    // Render cart modal
    renderCartModal() {
        this.loadCart(); // Refresh cart data

        if (this.cart.length === 0) {
            this.showEmptyCart();
            return;
        }

        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        const subtotal = cartItems.reduce((sum, { item, product }) => sum + (product.price * item.quantity), 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;

        const modalContent = `
            <div class="cart-modal">
                <div class="max-h-96 overflow-y-auto mb-6">
                    <div class="space-y-4">
                        ${cartItems.map(({ item, product }) => Components.createCartItem(item, product)).join('')}
                    </div>
                </div>
                
                <div class="border-t border-white/10 pt-6">
                    <div class="space-y-2 mb-6">
                        <div class="flex justify-between text-white/80">
                            <span>Subtotal:</span>
                            <span>${Utils.formatPrice(subtotal)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Tax (10%):</span>
                            <span>${Utils.formatPrice(tax)}</span>
                        </div>
                        <div class="flex justify-between text-xl font-bold text-white border-t border-white/10 pt-2">
                            <span>Total:</span>
                            <span class="text-cyan-400">${Utils.formatPrice(total)}</span>
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button onclick="cartManager.proceedToCheckout()" 
                                class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                            <i class="fas fa-credit-card mr-2"></i>
                            Proceed to Checkout
                        </button>
                        <button onclick="cartManager.clearCart(); document.getElementById('cartModal').classList.add('hidden')" 
                                class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                            Clear Cart
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.showModal('cartModal', `Shopping Cart (${this.cart.length} items)`, modalContent);
    }

    // Show empty cart
    showEmptyCart() {
        const modalContent = `
            <div class="text-center py-12">
                <i class="fas fa-shopping-cart text-6xl text-white/20 mb-6"></i>
                <h3 class="text-2xl font-semibold text-white mb-4">Your cart is empty</h3>
                <p class="text-white/60 mb-6">Add some products to get started!</p>
                <button onclick="document.getElementById('cartModal').classList.add('hidden')" 
                        class="btn-primary px-6 py-3 rounded-xl font-semibold">
                    Continue Shopping
                </button>
            </div>
        `;

        this.showModal('cartModal', 'Shopping Cart', modalContent);
    }

    // Generic modal handler
    showModal(modalId, title, content) {
        let modal = document.getElementById(modalId);
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = modalId;
            modal.innerHTML = Components.createModal(modalId, title, content);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('h3').textContent = title;
            modal.querySelector('.modal-content').innerHTML = content;
        }

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
        modal.querySelector('.modal-card').classList.add('modal-enter');
    }

    // Proceed to checkout
    proceedToCheckout() {
        if (this.cart.length === 0) {
            Components.createNotification('Your cart is empty', 'warning');
            return;
        }

        // Validate stock availability
        const unavailableItems = [];
        for (const cartItem of this.cart) {
            const product = storage.getProductById(cartItem.productId);
            if (!product || product.stock < cartItem.quantity) {
                unavailableItems.push(product ? product.title : 'Unknown Product');
            }
        }

        if (unavailableItems.length > 0) {
            Components.createNotification(
                `Some items are no longer available: ${unavailableItems.join(', ')}`, 
                'error'
            );
            this.renderCartModal(); // Refresh cart to show updated stock
            return;
        }

        this.showCheckoutModal();
    }

    // Show checkout modal
    showCheckoutModal() {
        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        const subtotal = cartItems.reduce((sum, { item, product }) => sum + (product.price * item.quantity), 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;

        const modalContent = `
            <div class="checkout-modal">
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-white mb-4">Order Summary</h4>
                    <div class="bg-dark-primary/30 rounded-xl p-4 mb-4 max-h-48 overflow-y-auto">
                        ${cartItems.map(({ item, product }) => `
                            <div class="flex justify-between items-center py-2 border-b border-white/10 last:border-b-0">
                                <div>
                                    <div class="text-white font-medium">${product.title}</div>
                                    <div class="text-sm text-white/60">Qty: ${item.quantity} × ${Utils.formatPrice(product.price)}</div>
                                </div>
                                <div class="text-white font-semibold">
                                    ${Utils.formatPrice(product.price * item.quantity)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-white/80">
                            <span>Subtotal:</span>
                            <span>${Utils.formatPrice(subtotal)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Tax (10%):</span>
                            <span>${Utils.formatPrice(tax)}</span>
                        </div>
                        <div class="flex justify-between text-xl font-bold text-white border-t border-white/10 pt-2">
                            <span>Total:</span>
                            <span class="text-cyan-400">${Utils.formatPrice(total)}</span>
                        </div>
                    </div>
                </div>
                
                <form id="checkoutForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/80 mb-3">Payment Method</label>
                        <select name="paymentMethod" 
                                required 
                                class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            <option value="" class="bg-dark-primary text-white">Select payment method</option>
                            <option value="credit_card" class="bg-dark-primary text-white">Credit Card</option>
                            <option value="paypal" class="bg-dark-primary text-white">PayPal</option>
                            <option value="crypto" class="bg-dark-primary text-white">Cryptocurrency</option>
                        </select>
                    </div>
                    
                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" 
                                   name="agreeTerms" 
                                   required 
                                   class="mt-1 mr-3 accent-cyan-400">
                            <span class="text-sm text-white/70 leading-relaxed">
                                I agree to the terms of service and understand that a 10% transaction fee is included in the total price.
                            </span>
                        </label>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                            <i class="fas fa-lock mr-2"></i>
                            Complete Order
                        </button>
                        <button type="button" 
                                onclick="document.getElementById('checkoutModal').classList.add('hidden')" 
                                class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        `;

        // Hide cart modal
        const cartModal = document.getElementById('cartModal');
        if (cartModal) {
            cartModal.classList.add('hidden');
        }

        this.showModal('checkoutModal', 'Checkout', modalContent);

        // Setup form handler
        document.getElementById('checkoutForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processOrder(e);
        });
    }

    // Process order
    processOrder(event) {
        const formData = new FormData(event.target);
        const paymentMethod = formData.get('paymentMethod');

        if (!paymentMethod) {
            Components.createNotification('Please select a payment method', 'error');
            return;
        }

        // Simulate order processing
        const loadingBtn = event.target.querySelector('button[type="submit"]');
        const originalText = loadingBtn.innerHTML;
        loadingBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        loadingBtn.disabled = true;

        setTimeout(() => {
            // Create orders for each seller
            const ordersByseller = Utils.groupBy(this.cart.map(item => {
                const product = storage.getProductById(item.productId);
                return { ...item, product };
            }), 'product.sellerId');

            const orders = [];
            Object.entries(ordersByseller).forEach(([sellerId, items]) => {
                const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
                const tax = Utils.calculateTax(subtotal);
                const total = subtotal + tax;

                const order = storage.addOrder({
                    buyerId: authManager.currentUser.id,
                    sellerId: parseInt(sellerId),
                    items: items.map(item => ({
                        productId: item.productId,
                        quantity: item.quantity,
                        price: item.product.price,
                        title: item.product.title
                    })),
                    subtotal,
                    tax,
                    total,
                    paymentMethod,
                    status: 'paid'
                });

                orders.push(order);

                // Update product stock
                items.forEach(item => {
                    const product = storage.getProductById(item.productId);
                    if (product) {
                        storage.updateProduct(item.productId, {
                            stock: product.stock - item.quantity
                        });
                    }
                });
            });

            // Clear cart
            this.clearCart();

            // Close modals
            document.getElementById('checkoutModal').classList.add('hidden');

            // Show success message
            Components.createNotification(
                `Order completed successfully! ${orders.length} order(s) created.`, 
                'success'
            );

            // Reset button
            loadingBtn.innerHTML = originalText;
            loadingBtn.disabled = false;

        }, 2000);
    }

    // Get cart summary
    getCartSummary() {
        const cartItems = this.cart.map(item => {
            const product = storage.getProductById(item.productId);
            return product ? { item, product } : null;
        }).filter(Boolean);

        const subtotal = cartItems.reduce((sum, { item, product }) => sum + (product.price * item.quantity), 0);
        const tax = Utils.calculateTax(subtotal);
        const total = subtotal + tax;
        const itemCount = this.cart.reduce((sum, item) => sum + item.quantity, 0);

        return {
            items: cartItems,
            subtotal,
            tax,
            total,
            itemCount
        };
    }
}

// Create global instance
const cartManager = new CartManager();
