// Category Management System
class CategoryManager {
    constructor() {
        this.categories = [];
    }

    // Initialize category management
    init() {
        this.loadCategories();
        // Categories are now used for filtering only, not displayed as main content
    }

    // Load categories from storage
    loadCategories() {
        this.categories = storage.getCategories();
        storage.updateCategoryCounts(); // Ensure counts are up to date
    }

    // Render categories on homepage
    renderCategories() {
        const container = document.getElementById('categoriesGrid');
        if (!container) return;

        this.loadCategories(); // Refresh categories with updated counts

        if (this.categories.length === 0) {
            container.innerHTML = Components.createEmptyState(
                'No Categories Available',
                'Categories will appear here as products are added.',
                'Add Product',
                'ProductManager.showAddProductModal()'
            );
            return;
        }

        container.innerHTML = this.categories
            .map(category => Components.createCategoryCard(category))
            .join('');

        // Add staggered animation
        const cards = container.querySelectorAll('.category-card');
        Utils.staggerAnimation(cards, Utils.slideIn);
    }

    // Browse products by category
    static browseCategory(categoryName) {
        // Update URL to reflect category filter
        Utils.updateUrlParams({ category: categoryName });
        
        // Filter products by category
        productManager.filterByCategory(categoryName);
        
        // Scroll to products section or navigate to products page
        const productsSection = document.getElementById('productsSection') || 
                               document.getElementById('featuredProducts')?.closest('section');
        
        if (productsSection) {
            productsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Show notification
        Components.createNotification(`Browsing ${categoryName} products`, 'info');
    }

    // Add new category
    addCategory(categoryData) {
        const newCategory = {
            id: Utils.generateRandomId(),
            name: categoryData.name,
            icon: categoryData.icon || 'fas fa-tag',
            gradient: categoryData.gradient || Utils.getRandomColor(),
            count: 0,
            description: categoryData.description || '',
            createdAt: new Date().toISOString()
        };

        this.categories.push(newCategory);
        storage.setCategories(this.categories);
        this.renderCategories();
        
        Components.createNotification('Category added successfully!', 'success');
        return newCategory;
    }

    // Update category
    updateCategory(categoryId, updates) {
        const index = this.categories.findIndex(cat => cat.id === categoryId);
        if (index !== -1) {
            this.categories[index] = { 
                ...this.categories[index], 
                ...updates, 
                updatedAt: new Date().toISOString() 
            };
            storage.setCategories(this.categories);
            this.renderCategories();
            Components.createNotification('Category updated successfully!', 'success');
            return this.categories[index];
        }
        return null;
    }

    // Delete category
    deleteCategory(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        if (!category) return false;

        // Check if category has products
        const productsInCategory = storage.getProductsByCategory(category.name);
        if (productsInCategory.length > 0) {
            Components.createNotification(
                `Cannot delete category "${category.name}" - it contains ${productsInCategory.length} products`, 
                'error'
            );
            return false;
        }

        this.categories = this.categories.filter(cat => cat.id !== categoryId);
        storage.setCategories(this.categories);
        this.renderCategories();
        
        Components.createNotification('Category deleted successfully!', 'success');
        return true;
    }

    // Get category by name
    getCategoryByName(name) {
        return this.categories.find(cat => cat.name === name);
    }

    // Get category by ID
    getCategoryById(id) {
        return this.categories.find(cat => cat.id === id);
    }

    // Show category management modal (for admin/sellers)
    showCategoryModal(categoryId = null) {
        const isEdit = categoryId !== null;
        const category = isEdit ? this.getCategoryById(categoryId) : null;

        const modalContent = `
            <form id="categoryForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Category Name</label>
                    <input type="text" 
                           name="name" 
                           value="${category?.name || ''}"
                           required 
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="Enter category name">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Description</label>
                    <textarea name="description" 
                              rows="3"
                              class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                              placeholder="Enter category description">${category?.description || ''}</textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Icon Class</label>
                    <input type="text" 
                           name="icon" 
                           value="${category?.icon || 'fas fa-tag'}"
                           class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                           placeholder="e.g., fas fa-gamepad">
                    <p class="text-xs text-white/50 mt-1">Use FontAwesome icon classes</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Gradient Colors</label>
                    <select name="gradient" 
                            class="modal-input w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                        <option value="from-cyan-400 to-blue-500" ${category?.gradient === 'from-cyan-400 to-blue-500' ? 'selected' : ''}>Cyan to Blue</option>
                        <option value="from-purple-400 to-pink-500" ${category?.gradient === 'from-purple-400 to-pink-500' ? 'selected' : ''}>Purple to Pink</option>
                        <option value="from-green-400 to-emerald-500" ${category?.gradient === 'from-green-400 to-emerald-500' ? 'selected' : ''}>Green to Emerald</option>
                        <option value="from-red-400 to-pink-500" ${category?.gradient === 'from-red-400 to-pink-500' ? 'selected' : ''}>Red to Pink</option>
                        <option value="from-yellow-400 to-orange-500" ${category?.gradient === 'from-yellow-400 to-orange-500' ? 'selected' : ''}>Yellow to Orange</option>
                        <option value="from-indigo-400 to-blue-500" ${category?.gradient === 'from-indigo-400 to-blue-500' ? 'selected' : ''}>Indigo to Blue</option>
                    </select>
                </div>
                
                <div class="flex space-x-4">
                    <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        ${isEdit ? 'Update Category' : 'Add Category'}
                    </button>
                    <button type="button" 
                            onclick="document.getElementById('categoryModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Cancel
                    </button>
                </div>
            </form>
        `;

        // Create or update modal
        let modal = document.getElementById('categoryModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'categoryModal';
            modal.innerHTML = Components.createModal(
                'categoryModal',
                isEdit ? 'Edit Category' : 'Add New Category',
                modalContent
            );
            document.body.appendChild(modal);
        } else {
            modal.querySelector('.modal-content').innerHTML = modalContent;
            modal.querySelector('h3').textContent = isEdit ? 'Edit Category' : 'Add New Category';
        }

        // Setup form handler
        const form = document.getElementById('categoryForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCategorySubmit(e, categoryId);
        });

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.classList.add('hidden');
            });
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
    }

    // Handle category form submission
    handleCategorySubmit(event, categoryId = null) {
        const formData = new FormData(event.target);
        const categoryData = {
            name: formData.get('name').trim(),
            description: formData.get('description').trim(),
            icon: formData.get('icon').trim(),
            gradient: formData.get('gradient')
        };

        // Validation
        if (!categoryData.name) {
            Components.createNotification('Category name is required', 'error');
            return;
        }

        // Check for duplicate names (excluding current category if editing)
        const existingCategory = this.getCategoryByName(categoryData.name);
        if (existingCategory && (!categoryId || existingCategory.id !== categoryId)) {
            Components.createNotification('Category name already exists', 'error');
            return;
        }

        // Add or update category
        if (categoryId) {
            this.updateCategory(categoryId, categoryData);
        } else {
            this.addCategory(categoryData);
        }

        // Close modal
        document.getElementById('categoryModal').classList.add('hidden');
    }

    // Get categories for dropdown/select elements
    getCategoriesForSelect() {
        return this.categories.map(cat => ({
            value: cat.name,
            label: `${cat.name} (${cat.count})`,
            id: cat.id
        }));
    }

    // Export categories data
    exportCategories() {
        const data = {
            categories: this.categories,
            exportedAt: new Date().toISOString(),
            version: CONFIG.VERSION
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `obu-categories-${Utils.formatDate(new Date().toISOString())}.json`;
        a.click();
        URL.revokeObjectURL(url);

        Components.createNotification('Categories exported successfully!', 'success');
    }

    // Import categories data
    importCategories(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.categories && Array.isArray(data.categories)) {
                    this.categories = data.categories;
                    storage.setCategories(this.categories);
                    this.renderCategories();
                    Components.createNotification('Categories imported successfully!', 'success');
                } else {
                    Components.createNotification('Invalid categories file format', 'error');
                }
            } catch (error) {
                Components.createNotification('Error reading categories file', 'error');
            }
        };
        reader.readAsText(file);
    }
}

// Create global instance
const categoryManager = new CategoryManager();
