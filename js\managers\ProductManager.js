// Product Management System
class ProductManager {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 8; // Show 8 products initially
        this.currentFilter = '';
        this.currentSort = 'featured'; // Show featured products first
        this.searchQuery = '';
        this.displayedProducts = 0;
    }

    // Initialize product management
    init() {
        this.renderMainProducts();
        this.renderCategoryFilter();
        this.setupEventListeners();
    }

    // Event Listeners
    setupEventListeners() {
        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.handleSearch(e.target.value);
            }, 300));
        }

        // Sort functionality
        const sortSelect = document.querySelector('#sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.renderProducts();
            });
        }
    }

    // Render featured products on homepage
    renderFeaturedProducts() {
        const featuredProducts = storage.getFeaturedProducts();
        const container = document.getElementById('featuredProducts');
        
        if (!container) return;

        if (featuredProducts.length === 0) {
            container.innerHTML = Components.createEmptyState(
                'No Featured Products',
                'Check back later for featured products and deals!',
                'Browse All Products',
                'ProductManager.showAllProducts()'
            );
            return;
        }

        container.innerHTML = featuredProducts
            .slice(0, 6) // Show max 6 featured products
            .map(product => Components.createProductCard(product))
            .join('');

        // Add staggered animation
        const cards = container.querySelectorAll('.product-card');
        Utils.staggerAnimation(cards, Utils.slideIn);
    }

    // Render all products with pagination and filtering
    renderProducts(containerId = 'productsContainer') {
        const container = document.getElementById(containerId);
        if (!container) return;

        let products = storage.getProducts();

        // Apply filters
        if (this.currentFilter) {
            products = products.filter(product => product.category === this.currentFilter);
        }

        // Apply search
        if (this.searchQuery) {
            products = storage.searchProducts(this.searchQuery);
        }

        // Apply sorting
        products = this.sortProducts(products);

        // Calculate pagination
        const totalPages = Math.ceil(products.length / this.itemsPerPage);
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedProducts = products.slice(startIndex, endIndex);

        // Render products
        if (paginatedProducts.length === 0) {
            container.innerHTML = Components.createEmptyState(
                'No Products Found',
                this.searchQuery ? `No products match "${this.searchQuery}"` : 'No products available in this category',
                'Clear Filters',
                'ProductManager.clearFilters()'
            );
            return;
        }

        const productsHTML = `
            <div class="products-header mb-8">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-white">
                        ${this.searchQuery ? `Search Results (${products.length})` : 
                          this.currentFilter ? `${this.currentFilter} (${products.length})` : 
                          `All Products (${products.length})`}
                    </h2>
                    <div class="flex items-center space-x-4">
                        <select id="sortSelect" class="bg-dark-secondary border border-white/10 rounded-lg px-4 py-2 text-white">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="rating">Highest Rated</option>
                            <option value="popular">Most Popular</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                ${paginatedProducts.map(product => Components.createProductCard(product)).join('')}
            </div>
            
            ${Components.createPagination(this.currentPage, totalPages, 'ProductManager.goToPage')}
        `;

        container.innerHTML = productsHTML;

        // Update sort select value
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.value = this.currentSort;
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.renderProducts(containerId);
            });
        }

        // Add animations
        const cards = container.querySelectorAll('.product-card');
        Utils.staggerAnimation(cards, Utils.slideIn);
    }

    // Sort products based on criteria
    sortProducts(products) {
        let sorted;
        switch (this.currentSort) {
            case 'featured':
                // Sort by featured first, then by rating
                sorted = products.sort((a, b) => {
                    if (a.featured && !b.featured) return -1;
                    if (!a.featured && b.featured) return 1;
                    return (b.rating || 0) - (a.rating || 0);
                });
                break;
            case 'newest':
                sorted = Utils.sortBy(products, 'createdAt', 'desc');
                break;
            case 'oldest':
                sorted = Utils.sortBy(products, 'createdAt', 'asc');
                break;
            case 'price-low':
                sorted = Utils.sortBy(products, 'price', 'asc');
                break;
            case 'price-high':
                sorted = Utils.sortBy(products, 'price', 'desc');
                break;
            case 'rating':
                sorted = Utils.sortBy(products, 'rating', 'desc');
                break;
            case 'popular':
                sorted = Utils.sortBy(products, 'reviews', 'desc');
                break;
            default:
                sorted = products;
        }
        return sorted;
    }

    // Handle search functionality
    handleSearch(query) {
        this.searchQuery = query.trim();
        this.currentPage = 1;
        
        if (this.searchQuery.length >= CONFIG.UI.SEARCH_MIN_LENGTH) {
            this.renderProducts();
            this.showSearchSuggestions(query);
        } else if (this.searchQuery.length === 0) {
            this.renderProducts();
            this.hideSearchSuggestions();
        }
    }

    // Show search suggestions
    showSearchSuggestions(query) {
        const searchContainer = document.querySelector('.search-input').parentElement;
        const existingSuggestions = searchContainer.querySelector('.search-suggestions');
        
        if (existingSuggestions) {
            existingSuggestions.remove();
        }

        if (query.length < CONFIG.UI.SEARCH_MIN_LENGTH) return;

        const suggestions = storage.searchProducts(query).slice(0, 5);
        
        if (suggestions.length === 0) return;

        const suggestionsHTML = `
            <div class="search-suggestions">
                ${suggestions.map(product => `
                    <div class="search-suggestion" onclick="ProductManager.selectProduct(${product.id})">
                        <div class="flex items-center">
                            <img src="${product.image || Utils.createImagePlaceholder(32, 32)}" 
                                 alt="${product.title}" 
                                 class="w-8 h-8 rounded mr-3">
                            <div>
                                <div class="font-medium text-white">${Utils.truncateText(product.title, 30)}</div>
                                <div class="text-sm text-white/60">${Utils.formatPrice(product.price)}</div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        searchContainer.insertAdjacentHTML('beforeend', suggestionsHTML);
    }

    // Hide search suggestions
    hideSearchSuggestions() {
        const suggestions = document.querySelector('.search-suggestions');
        if (suggestions) {
            suggestions.remove();
        }
    }

    // Select product from search suggestions
    selectProduct(productId) {
        this.hideSearchSuggestions();
        const product = storage.getProductById(productId);
        if (product) {
            this.showProductModal(product);
        }
    }

    // Filter products by category
    filterByCategory(categoryName) {
        this.currentFilter = categoryName;
        this.currentPage = 1;
        this.renderProducts();
    }

    // Clear all filters
    clearFilters() {
        this.currentFilter = '';
        this.searchQuery = '';
        this.currentPage = 1;
        
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.value = '';
        }
        
        this.renderProducts();
    }

    // Pagination
    goToPage(page) {
        this.currentPage = page;
        this.renderProducts();
        
        // Scroll to top of products
        const container = document.getElementById('productsContainer');
        if (container) {
            container.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Add product to cart
    static addToCart(productId) {
        const currentUser = storage.getCurrentUser();
        if (!currentUser) {
            Components.createNotification('Please login to add items to cart', 'warning');
            AuthManager.showLoginModal();
            return;
        }

        const product = storage.getProductById(productId);
        if (!product) {
            Components.createNotification('Product not found', 'error');
            return;
        }

        if (product.stock <= 0) {
            Components.createNotification('Product is out of stock', 'error');
            return;
        }

        storage.addToCart(productId, 1);
        CartManager.updateCartUI();
        Components.createNotification('Item added to cart!', 'success');
    }

    // Show product details modal
    showProductModal(product) {
        const discount = Utils.calculateDiscount(product.originalPrice, product.price);
        const finalPrice = Utils.calculatePriceWithTax(product.price);
        const taxAmount = Utils.calculateTax(product.price);

        const modalContent = `
            <div class="product-modal">
                <div class="mb-6">
                    <img src="${product.image || Utils.createImagePlaceholder()}" 
                         alt="${product.title}" 
                         class="w-full h-64 object-cover rounded-xl mb-4">
                    
                    <h2 class="text-2xl font-bold text-white mb-2">${product.title}</h2>
                    <p class="text-white/70 mb-4">${product.description}</p>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <span class="text-2xl font-bold text-green-400">${Utils.formatPrice(product.price)}</span>
                            ${product.originalPrice && product.originalPrice > product.price ? 
                                `<span class="text-lg text-white/50 line-through ml-2">${Utils.formatPrice(product.originalPrice)}</span>` : ''}
                            ${discount > 0 ? `<span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs ml-2">-${discount}%</span>` : ''}
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-white/60">Stock: ${product.stock}</div>
                            <div class="text-sm text-white/60">Category: ${product.category}</div>
                        </div>
                    </div>
                    
                    <div class="tax-info mb-6">
                        <div class="text-sm text-white/80">
                            Final Price: <span class="font-semibold text-cyan-400">${Utils.formatPrice(finalPrice)}</span>
                            <span class="text-xs text-white/60">(includes ${Utils.formatPrice(taxAmount)} tax)</span>
                        </div>
                    </div>
                    
                    ${product.rating > 0 ? `
                        <div class="flex items-center mb-4">
                            <div class="rating text-yellow-400 mr-2">
                                ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                            </div>
                            <span class="text-white/60">${product.rating}/5 (${product.reviews} reviews)</span>
                        </div>
                    ` : ''}
                    
                    <div class="seller-badge mb-6">${product.sellerName}</div>
                </div>
                
                <div class="flex space-x-4">
                    <button onclick="ProductManager.addToCart(${product.id}); document.getElementById('productModal').classList.add('hidden')" 
                            class="btn-primary flex-1 py-3 rounded-xl font-semibold ${product.stock === 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                            ${product.stock === 0 ? 'disabled' : ''}>
                        <i class="fas fa-shopping-cart mr-2"></i>
                        ${product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                    </button>
                    <button onclick="document.getElementById('productModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Close
                    </button>
                </div>
            </div>
        `;

        // Create or update modal
        let modal = document.getElementById('productModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'productModal';
            modal.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm hidden z-50';
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="modal-card bg-dark-secondary/95 backdrop-blur-xl border border-white/10 rounded-2xl max-w-2xl w-full p-8">
                        <div id="productModalContent"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        document.getElementById('productModalContent').innerHTML = modalContent;
        modal.classList.remove('hidden');
    }

    // Show all products page
    showAllProducts() {
        // This would typically navigate to a products page
        // For now, we'll just render products in the current container
        this.clearFilters();
        this.renderProducts();
    }

    // Render main products section on homepage
    renderMainProducts() {
        const container = document.getElementById('mainProductsGrid');
        if (!container) return;

        let products = storage.getProducts();

        // Apply filters
        if (this.currentFilter) {
            products = products.filter(product => product.category === this.currentFilter);
        }

        // Apply search
        if (this.searchQuery) {
            products = storage.searchProducts(this.searchQuery);
        }

        // Apply sorting - prioritize featured products
        products = this.sortProducts(products);

        // Show initial products
        const productsToShow = products.slice(0, this.itemsPerPage);
        this.displayedProducts = productsToShow.length;

        if (productsToShow.length === 0) {
            container.innerHTML = Components.createEmptyState(
                'No Products Found',
                this.searchQuery ? `No products match "${this.searchQuery}"` : 'No products available in this category',
                'Clear Filters',
                'productManager.clearFilters()'
            );
            this.hideLoadMoreButton();
            return;
        }

        container.innerHTML = productsToShow
            .map(product => Components.createProductCard(product))
            .join('');

        // Show/hide load more button
        if (this.displayedProducts < products.length) {
            this.showLoadMoreButton();
        } else {
            this.hideLoadMoreButton();
        }

        // Add staggered animation
        const cards = container.querySelectorAll('.product-card');
        Utils.staggerAnimation(cards, Utils.slideIn);
    }

    // Render category filter
    renderCategoryFilter() {
        const container = document.getElementById('categoryFilter');
        if (!container) return;

        const categories = storage.getCategories();

        const filterHTML = `
            <button onclick="productManager.filterByCategory('')"
                    class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${this.currentFilter === '' ? 'bg-cyan-400 text-dark-primary' : 'bg-white/10 text-white hover:bg-white/20'}">
                <i class="fas fa-th-large mr-2"></i>All Products
            </button>
            ${categories.map(category => `
                <button onclick="productManager.filterByCategory('${category.name}')"
                        class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${this.currentFilter === category.name ? 'bg-cyan-400 text-dark-primary' : 'bg-white/10 text-white hover:bg-white/20'}">
                    <i class="${category.icon} mr-2"></i>${category.name}
                </button>
            `).join('')}
        `;

        container.innerHTML = filterHTML;
    }

    // Load more products
    loadMoreProducts() {
        let products = storage.getProducts();

        // Apply filters
        if (this.currentFilter) {
            products = products.filter(product => product.category === this.currentFilter);
        }

        // Apply search
        if (this.searchQuery) {
            products = storage.searchProducts(this.searchQuery);
        }

        // Apply sorting
        products = this.sortProducts(products);

        // Get next batch of products
        const nextBatch = products.slice(this.displayedProducts, this.displayedProducts + this.itemsPerPage);

        if (nextBatch.length === 0) {
            this.hideLoadMoreButton();
            return;
        }

        // Append new products
        const container = document.getElementById('mainProductsGrid');
        const newProductsHTML = nextBatch
            .map(product => Components.createProductCard(product))
            .join('');

        container.insertAdjacentHTML('beforeend', newProductsHTML);

        // Update displayed count
        this.displayedProducts += nextBatch.length;

        // Hide load more button if all products are shown
        if (this.displayedProducts >= products.length) {
            this.hideLoadMoreButton();
        }

        // Animate new cards
        const newCards = Array.from(container.children).slice(-nextBatch.length);
        Utils.staggerAnimation(newCards, Utils.slideIn);
    }

    // Show load more button
    showLoadMoreButton() {
        const btn = document.getElementById('loadMoreBtn');
        if (btn) {
            btn.style.display = 'inline-flex';
        }
    }

    // Hide load more button
    hideLoadMoreButton() {
        const btn = document.getElementById('loadMoreBtn');
        if (btn) {
            btn.style.display = 'none';
        }
    }

    // Override filter by category for main products
    filterByCategory(categoryName) {
        this.currentFilter = categoryName;
        this.displayedProducts = 0;
        this.renderMainProducts();
        this.renderCategoryFilter();
    }

    // Override clear filters for main products
    clearFilters() {
        this.currentFilter = '';
        this.searchQuery = '';
        this.displayedProducts = 0;

        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        this.renderMainProducts();
        this.renderCategoryFilter();
    }
}

// Create global instance
const productManager = new ProductManager();
