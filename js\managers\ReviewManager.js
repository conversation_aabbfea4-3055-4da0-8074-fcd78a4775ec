// Review Management System
class ReviewManager {
    constructor() {
        this.currentSort = 'newest';
    }

    // Initialize review system
    init() {
        this.setupEventListeners();
    }

    // Setup event listeners
    setupEventListeners() {
        // Event delegation for dynamic content
        document.addEventListener('click', (e) => {
            if (e.target.matches('.write-review-btn')) {
                const purchaseId = e.target.dataset.purchaseId;
                this.showReviewModal(purchaseId);
            }
            
            if (e.target.matches('.view-reviews-btn')) {
                const productId = parseInt(e.target.dataset.productId);
                this.showProductReviews(productId);
            }
            
            if (e.target.matches('.helpful-btn')) {
                const reviewId = e.target.dataset.reviewId;
                this.voteOnReview(reviewId, true);
            }
            
            if (e.target.matches('.unhelpful-btn')) {
                const reviewId = e.target.dataset.reviewId;
                this.voteOnReview(reviewId, false);
            }
        });
    }

    // Show review modal for a purchase
    showReviewModal(purchaseId) {
        const purchases = storage.getPurchases();
        const purchase = purchases.find(p => p.id === purchaseId);
        
        if (!purchase) {
            Components.createNotification('Purchase not found', 'error');
            return;
        }

        const product = storage.getProductById(purchase.productId);
        if (!product) {
            Components.createNotification('Product not found', 'error');
            return;
        }

        // Check if user already reviewed this product
        if (storage.hasUserReviewedProduct(purchase.buyerId, purchase.productId)) {
            Components.createNotification('You have already reviewed this product', 'warning');
            return;
        }

        const modalContent = `
            <form id="reviewForm" class="space-y-6">
                <input type="hidden" name="purchaseId" value="${purchaseId}">
                <input type="hidden" name="productId" value="${purchase.productId}">
                
                <div class="text-center mb-6">
                    <img src="${product.image || Utils.createImagePlaceholder(80, 80)}" 
                         alt="${product.title}" 
                         class="w-20 h-20 rounded-xl mx-auto mb-4 object-cover">
                    <h3 class="text-xl font-bold text-white">${product.title}</h3>
                    <p class="text-white/60 text-sm">Share your experience with this product</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Your Rating</label>
                    <div class="rating-input flex justify-center space-x-2 mb-2">
                        ${[1, 2, 3, 4, 5].map(star => `
                            <button type="button" 
                                    class="star-btn text-3xl text-white/30 hover:text-yellow-400 transition-colors duration-200" 
                                    data-rating="${star}">
                                ★
                            </button>
                        `).join('')}
                    </div>
                    <input type="hidden" name="rating" id="ratingValue" required>
                    <p class="text-center text-sm text-white/60">Click to rate</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-white/80 mb-3">Your Review</label>
                    <textarea name="reviewText" 
                              rows="4" 
                              required 
                              minlength="${CONFIG.REVIEWS.MIN_REVIEW_LENGTH}"
                              maxlength="${CONFIG.REVIEWS.MAX_REVIEW_LENGTH}"
                              class="w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 resize-none"
                              placeholder="Share your thoughts about this product..."></textarea>
                    <div class="flex justify-between text-xs text-white/50 mt-1">
                        <span>Minimum ${CONFIG.REVIEWS.MIN_REVIEW_LENGTH} characters</span>
                        <span id="charCount">0/${CONFIG.REVIEWS.MAX_REVIEW_LENGTH}</span>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-3">Pros (Optional)</label>
                        <textarea name="pros" 
                                  rows="2" 
                                  maxlength="200"
                                  class="w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 resize-none"
                                  placeholder="What did you like?"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white/80 mb-3">Cons (Optional)</label>
                        <textarea name="cons" 
                                  rows="2" 
                                  maxlength="200"
                                  class="w-full px-4 py-3 bg-dark-primary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 resize-none"
                                  placeholder="What could be improved?"></textarea>
                    </div>
                </div>
                
                <div class="flex space-x-4 pt-4">
                    <button type="submit" class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        <i class="fas fa-star mr-2"></i>
                        Submit Review
                    </button>
                    <button type="button" onclick="document.getElementById('reviewModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Cancel
                    </button>
                </div>
            </form>
        `;

        // Create modal
        let modal = document.getElementById('reviewModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'reviewModal';
            modal.innerHTML = Components.createModal('reviewModal', 'Write a Review', modalContent);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('.modal-content').innerHTML = modalContent;
        }

        // Setup form handlers
        this.setupReviewForm();

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
    }

    // Setup review form interactions
    setupReviewForm() {
        const form = document.getElementById('reviewForm');
        const ratingInput = document.getElementById('ratingValue');
        const starButtons = document.querySelectorAll('.star-btn');
        const reviewTextarea = form.querySelector('textarea[name="reviewText"]');
        const charCount = document.getElementById('charCount');

        // Star rating interaction
        starButtons.forEach((star, index) => {
            star.addEventListener('click', () => {
                const rating = index + 1;
                ratingInput.value = rating;
                
                // Update star display
                starButtons.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.remove('text-white/30');
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                        s.classList.add('text-white/30');
                    }
                });
            });

            star.addEventListener('mouseenter', () => {
                const rating = index + 1;
                starButtons.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                    }
                });
            });
        });

        // Reset stars on mouse leave
        document.querySelector('.rating-input').addEventListener('mouseleave', () => {
            const currentRating = parseInt(ratingInput.value) || 0;
            starButtons.forEach((s, i) => {
                if (i < currentRating) {
                    s.classList.add('text-yellow-400');
                    s.classList.remove('text-white/30');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-white/30');
                }
            });
        });

        // Character count
        reviewTextarea.addEventListener('input', () => {
            const count = reviewTextarea.value.length;
            charCount.textContent = `${count}/${CONFIG.REVIEWS.MAX_REVIEW_LENGTH}`;
            
            if (count > CONFIG.REVIEWS.MAX_REVIEW_LENGTH * 0.9) {
                charCount.classList.add('text-yellow-400');
            } else {
                charCount.classList.remove('text-yellow-400');
            }
        });

        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleReviewSubmission(e);
        });
    }

    // Handle review submission
    handleReviewSubmission(event) {
        const formData = new FormData(event.target);
        const rating = parseInt(formData.get('rating'));
        
        if (!rating || rating < 1 || rating > 5) {
            Components.createNotification('Please select a rating', 'error');
            return;
        }

        const reviewText = formData.get('reviewText').trim();
        if (reviewText.length < CONFIG.REVIEWS.MIN_REVIEW_LENGTH) {
            Components.createNotification(`Review must be at least ${CONFIG.REVIEWS.MIN_REVIEW_LENGTH} characters`, 'error');
            return;
        }

        const purchaseId = formData.get('purchaseId');
        const productId = parseInt(formData.get('productId'));
        const purchase = storage.getPurchases().find(p => p.id === purchaseId);
        
        if (!purchase) {
            Components.createNotification('Purchase not found', 'error');
            return;
        }

        const reviewData = {
            userId: purchase.buyerId,
            userName: purchase.buyerName,
            productId: productId,
            purchaseId: purchaseId,
            rating: rating,
            reviewText: reviewText,
            pros: formData.get('pros').trim() || null,
            cons: formData.get('cons').trim() || null,
            purchaseDate: purchase.purchaseDate
        };

        // Submit review
        const review = storage.addReview(reviewData);
        
        // Close modal
        document.getElementById('reviewModal').classList.add('hidden');
        
        // Show success message
        Components.createNotification('Review submitted successfully!', 'success');
        
        // Refresh purchase history if open
        const purchaseModal = document.getElementById('purchaseHistoryModal');
        if (purchaseModal && !purchaseModal.classList.contains('hidden')) {
            setTimeout(() => authManager.showPurchaseHistory(), 500);
        }
    }

    // Show product reviews modal
    showProductReviews(productId) {
        const product = storage.getProductById(productId);
        if (!product) {
            Components.createNotification('Product not found', 'error');
            return;
        }

        const reviews = storage.getReviewsByProduct(productId);
        const sortedReviews = this.sortReviews(reviews);

        const modalContent = `
            <div class="product-reviews">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-xl font-bold text-white">${product.title}</h3>
                        <div class="flex items-center mt-2">
                            <div class="rating text-yellow-400 mr-2">
                                ${this.renderStars(product.rating || 0)}
                            </div>
                            <span class="text-white/80">${product.rating || 0}/5</span>
                            <span class="text-white/60 ml-2">(${reviews.length} review${reviews.length !== 1 ? 's' : ''})</span>
                        </div>
                    </div>

                    <select id="reviewSort" class="bg-dark-primary border border-white/10 rounded-lg px-4 py-2 text-white text-sm">
                        <option value="newest">Newest First</option>
                        <option value="oldest">Oldest First</option>
                        <option value="highest_rating">Highest Rating</option>
                        <option value="lowest_rating">Lowest Rating</option>
                        <option value="most_helpful">Most Helpful</option>
                    </select>
                </div>

                <div class="reviews-list max-h-96 overflow-y-auto">
                    ${reviews.length === 0 ? this.renderEmptyReviews() : sortedReviews.map(review => this.renderReviewCard(review)).join('')}
                </div>
            </div>
        `;

        // Create modal
        let modal = document.getElementById('productReviewsModal');
        if (!modal) {
            modal = Utils.createElement('div');
            modal.id = 'productReviewsModal';
            modal.innerHTML = Components.createModal('productReviewsModal', 'Customer Reviews', modalContent);
            document.body.appendChild(modal);
        } else {
            modal.querySelector('.modal-content').innerHTML = modalContent;
        }

        // Setup sort handler
        const sortSelect = document.getElementById('reviewSort');
        if (sortSelect) {
            sortSelect.value = this.currentSort;
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.showProductReviews(productId);
            });
        }

        // Setup close handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => modal.classList.add('hidden'));
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        modal.classList.remove('hidden');
    }

    // Render review card
    renderReviewCard(review) {
        const timeAgo = Utils.timeAgo(review.createdAt);
        const purchaseTimeAgo = Utils.timeAgo(review.purchaseDate);

        return `
            <div class="review-card bg-dark-primary/30 rounded-xl p-6 mb-4 border border-white/10">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">${review.userName.charAt(0).toUpperCase()}</span>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <span class="font-semibold text-white mr-2">${review.userName}</span>
                                ${review.verified ? '<span class="px-2 py-1 bg-green-400/20 text-green-400 rounded-full text-xs font-semibold">Verified Purchase</span>' : ''}
                            </div>
                            <div class="text-xs text-white/60">
                                Purchased ${purchaseTimeAgo} • Reviewed ${timeAgo}
                            </div>
                        </div>
                    </div>

                    <div class="rating text-yellow-400">
                        ${this.renderStars(review.rating)}
                    </div>
                </div>

                <div class="review-content mb-4">
                    <p class="text-white/90 leading-relaxed">${review.reviewText}</p>

                    ${review.pros || review.cons ? `
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            ${review.pros ? `
                                <div class="pros">
                                    <h5 class="text-green-400 font-semibold text-sm mb-2">
                                        <i class="fas fa-thumbs-up mr-1"></i>Pros
                                    </h5>
                                    <p class="text-white/80 text-sm">${review.pros}</p>
                                </div>
                            ` : ''}

                            ${review.cons ? `
                                <div class="cons">
                                    <h5 class="text-red-400 font-semibold text-sm mb-2">
                                        <i class="fas fa-thumbs-down mr-1"></i>Cons
                                    </h5>
                                    <p class="text-white/80 text-sm">${review.cons}</p>
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>

                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center space-x-4">
                        <button class="helpful-btn flex items-center text-white/60 hover:text-green-400 transition-colors"
                                data-review-id="${review.id}">
                            <i class="fas fa-thumbs-up mr-1"></i>
                            Helpful (${review.helpfulVotes || 0})
                        </button>
                        <button class="unhelpful-btn flex items-center text-white/60 hover:text-red-400 transition-colors"
                                data-review-id="${review.id}">
                            <i class="fas fa-thumbs-down mr-1"></i>
                            Not Helpful (${review.unhelpfulVotes || 0})
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Render empty reviews state
    renderEmptyReviews() {
        return `
            <div class="text-center py-12">
                <i class="fas fa-star text-6xl text-white/20 mb-6"></i>
                <h4 class="text-xl font-semibold text-white mb-2">No Reviews Yet</h4>
                <p class="text-white/60">Be the first to review this product!</p>
            </div>
        `;
    }

    // Render star rating
    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        return '★'.repeat(fullStars) +
               (hasHalfStar ? '☆' : '') +
               '☆'.repeat(emptyStars);
    }

    // Sort reviews
    sortReviews(reviews) {
        const sorted = [...reviews];

        switch (this.currentSort) {
            case 'newest':
                return sorted.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            case 'oldest':
                return sorted.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
            case 'highest_rating':
                return sorted.sort((a, b) => b.rating - a.rating);
            case 'lowest_rating':
                return sorted.sort((a, b) => a.rating - b.rating);
            case 'most_helpful':
                return sorted.sort((a, b) => (b.helpfulVotes || 0) - (a.helpfulVotes || 0));
            default:
                return sorted;
        }
    }

    // Vote on review helpfulness
    voteOnReview(reviewId, isHelpful) {
        const updatedReview = storage.voteOnReview(reviewId, isHelpful);
        if (updatedReview) {
            Components.createNotification(
                isHelpful ? 'Marked as helpful!' : 'Marked as not helpful!',
                'success'
            );

            // Refresh the reviews modal if it's open
            const modal = document.getElementById('productReviewsModal');
            if (modal && !modal.classList.contains('hidden')) {
                // Find the product ID from the current modal content
                const productTitle = modal.querySelector('h3').textContent;
                const products = storage.getProducts();
                const product = products.find(p => p.title === productTitle);
                if (product) {
                    setTimeout(() => this.showProductReviews(product.id), 300);
                }
            }
        }
    }

    // Check if user can review product
    canUserReviewProduct(userId, productId) {
        // Must have purchased the product
        const hasPurchased = storage.isVerifiedPurchase(userId, productId);
        if (!hasPurchased) return false;

        // Must not have already reviewed
        const hasReviewed = storage.hasUserReviewedProduct(userId, productId);
        if (hasReviewed) return false;

        return true;
    }

    // Get review summary for product
    getReviewSummary(productId) {
        const reviews = storage.getReviewsByProduct(productId);

        if (reviews.length === 0) {
            return {
                averageRating: 0,
                totalReviews: 0,
                ratingDistribution: [0, 0, 0, 0, 0]
            };
        }

        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalRating / reviews.length;

        const ratingDistribution = [0, 0, 0, 0, 0];
        reviews.forEach(review => {
            ratingDistribution[review.rating - 1]++;
        });

        return {
            averageRating: Math.round(averageRating * 10) / 10,
            totalReviews: reviews.length,
            ratingDistribution
        };
    }
}

// Create global instance
const reviewManager = new ReviewManager();
