// Mobile Menu Manager for Obu Marketplace
class MobileMenu {
    constructor() {
        this.overlay = null;
        this.menu = null;
        this.isOpen = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    setupEventListeners() {
        // Get elements
        this.overlay = document.getElementById('mobileMenuOverlay');
        this.menu = this.overlay?.querySelector('.mobile-menu');
        
        if (!this.overlay || !this.menu) {
            console.warn('Mobile menu elements not found');
            return;
        }

        // Mobile menu button
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => this.open());
        }

        // Close button
        const closeBtn = document.getElementById('closeMobileMenu');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // Close on overlay click
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // Handle auth state changes
        document.addEventListener('authStateChanged', () => {
            this.updateAuthSection();
        });

        // Update auth section on load
        setTimeout(() => this.updateAuthSection(), 1000);

        console.log('✅ Mobile menu initialized');
    }

    open() {
        if (!this.overlay || !this.menu) return;

        console.log('📱 Opening mobile menu');
        
        this.isOpen = true;
        this.overlay.classList.remove('hidden');
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Trigger animation
        setTimeout(() => {
            this.menu.classList.add('open');
        }, 10);

        // Update currency selector
        this.syncCurrencySelector();
    }

    close() {
        if (!this.overlay || !this.menu) return;

        console.log('📱 Closing mobile menu');
        
        this.isOpen = false;
        this.menu.classList.remove('open');
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Hide overlay after animation
        setTimeout(() => {
            this.overlay.classList.add('hidden');
        }, 300);
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    updateAuthSection() {
        const authSection = document.getElementById('mobileAuthSection');
        const mobileLoginBtn = document.getElementById('mobileLoginBtn');
        const mobileRegisterBtn = document.getElementById('mobileRegisterBtn');
        
        if (!authSection || !mobileLoginBtn || !mobileRegisterBtn) return;

        // Check if user is logged in
        const isLoggedIn = typeof authManager !== 'undefined' && authManager.isLoggedIn();
        
        if (isLoggedIn) {
            const user = authManager.currentUser;
            authSection.innerHTML = `
                <div class="bg-dark-primary/30 rounded-xl p-4 mb-4">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div>
                            <div class="text-white font-semibold text-sm">${user.name}</div>
                            <div class="text-white/60 text-xs">${user.email}</div>
                        </div>
                    </div>
                    <div class="text-xs text-cyan-400 mb-3">Buyer Account</div>
                </div>
                
                <div class="space-y-2">
                    <button onclick="mobileMenu.close(); authManager.showProfile()" 
                            class="mobile-menu-link w-full text-left">
                        <i class="fas fa-user mr-3"></i>
                        Profile Settings
                    </button>
                    <button onclick="mobileMenu.close(); authManager.showPurchaseHistory()" 
                            class="mobile-menu-link w-full text-left">
                        <i class="fas fa-receipt mr-3"></i>
                        My Purchases
                    </button>
                    <button onclick="mobileMenu.close(); authManager.logout()" 
                            class="mobile-menu-link w-full text-left text-red-400 hover:text-red-300">
                        <i class="fas fa-sign-out-alt mr-3"></i>
                        Logout
                    </button>
                </div>
            `;
        } else {
            authSection.innerHTML = `
                <button id="mobileLoginBtn" onclick="mobileMenu.close(); authManager.showLoginModal()" 
                        class="mobile-menu-btn btn-primary w-full py-3 rounded-xl font-semibold mb-3">
                    Login
                </button>
                <button id="mobileRegisterBtn" onclick="mobileMenu.close(); authManager.showRegisterModal()" 
                        class="mobile-menu-btn btn-secondary w-full py-3 rounded-xl font-semibold">
                    Register
                </button>
            `;
        }
    }

    syncCurrencySelector() {
        const navSelector = document.getElementById('navCurrencySelector');
        const mobileSelector = document.getElementById('mobileCurrencySelector');
        
        if (navSelector && mobileSelector) {
            mobileSelector.value = navSelector.value;
        }
    }

    // Handle window resize
    handleResize() {
        // Close mobile menu on desktop
        if (window.innerWidth >= 768 && this.isOpen) {
            this.close();
        }
    }
}

// Create global instance
const mobileMenu = new MobileMenu();

// Handle window resize
window.addEventListener('resize', () => mobileMenu.handleResize());

// Export for global access
window.mobileMenu = mobileMenu;

// Add floating action button for better mobile UX
class FloatingActionButton {
    constructor() {
        this.fab = null;
        this.isVisible = false;
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.create());
        } else {
            this.create();
        }
    }

    create() {
        // Only show on mobile
        if (window.innerWidth >= 768) return;

        this.fab = document.createElement('div');
        this.fab.className = 'floating-action-btn';
        this.fab.innerHTML = `
            <button class="fab-btn" onclick="mobileMenu.open()">
                <i class="fas fa-bars"></i>
            </button>
        `;

        document.body.appendChild(this.fab);

        // Show/hide based on scroll
        this.setupScrollListener();

        // Add CSS if not already added
        this.addCSS();
    }

    addCSS() {
        if (document.getElementById('fab-styles')) return;

        const style = document.createElement('style');
        style.id = 'fab-styles';
        style.textContent = `
            .floating-action-btn {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 55;
                opacity: 0;
                transform: scale(0.8) translateY(20px);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                pointer-events: none;
            }

            .floating-action-btn.visible {
                opacity: 1;
                transform: scale(1) translateY(0);
                pointer-events: auto;
            }

            .fab-btn {
                width: 56px;
                height: 56px;
                background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
                border: none;
                border-radius: 50%;
                color: white;
                font-size: 1.25rem;
                cursor: pointer;
                box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .fab-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
            }

            .fab-btn:active {
                transform: scale(0.95);
            }

            @media (min-width: 768px) {
                .floating-action-btn {
                    display: none;
                }
            }
        `;

        document.head.appendChild(style);
    }

    setupScrollListener() {
        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateFAB = () => {
            const currentScrollY = window.scrollY;
            const isScrollingDown = currentScrollY > lastScrollY;
            const shouldShow = currentScrollY > 200 && !isScrollingDown;

            if (shouldShow && !this.isVisible) {
                this.show();
            } else if (!shouldShow && this.isVisible) {
                this.hide();
            }

            lastScrollY = currentScrollY;
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateFAB);
                ticking = true;
            }
        });
    }

    show() {
        if (this.fab && !this.isVisible) {
            this.isVisible = true;
            this.fab.classList.add('visible');
        }
    }

    hide() {
        if (this.fab && this.isVisible) {
            this.isVisible = false;
            this.fab.classList.remove('visible');
        }
    }
}

// Create floating action button for mobile
const floatingActionButton = new FloatingActionButton();
