// Currency Management System for Obu Marketplace
class CurrencyManager {
    constructor() {
        this.currentCurrency = 'USD'; // Default currency
        this.exchangeRates = {
            'USD': 1.0, // Base currency
            'PHP': 56.50 // Default rate, should be updated from API
        };
        
        this.currencyConfig = {
            'USD': {
                name: 'US Dollar',
                symbol: '$',
                code: 'USD',
                decimals: 2,
                position: 'before' // symbol position
            },
            'PHP': {
                name: 'Philippine Peso',
                symbol: '₱',
                code: 'PHP',
                decimals: 2,
                position: 'before'
            }
        };
        
        this.exchangeRateAPI = 'https://api.exchangerate-api.com/v4/latest/USD';
        this.lastRateUpdate = null;
        this.rateUpdateInterval = 3600000; // 1 hour in milliseconds
    }

    // Initialize currency manager
    async init() {
        try {
            // Load saved currency preference
            this.loadCurrencyPreference();
            
            // Update exchange rates
            await this.updateExchangeRates();
            
            // Set up periodic rate updates
            this.setupRateUpdates();
            
            // Update UI
            this.updateCurrencyDisplay();
            
            return true;
        } catch (error) {
            console.error('Currency manager initialization failed:', error);
            return false;
        }
    }

    // Load currency preference from storage
    loadCurrencyPreference() {
        const savedCurrency = localStorage.getItem('obu_preferred_currency');
        if (savedCurrency && this.currencyConfig[savedCurrency]) {
            this.currentCurrency = savedCurrency;
        }
    }

    // Save currency preference
    saveCurrencyPreference() {
        localStorage.setItem('obu_preferred_currency', this.currentCurrency);
    }

    // Update exchange rates from API
    async updateExchangeRates() {
        try {
            const response = await fetch(this.exchangeRateAPI);
            
            if (!response.ok) {
                throw new Error('Failed to fetch exchange rates');
            }
            
            const data = await response.json();
            
            // Update rates
            if (data.rates) {
                this.exchangeRates = {
                    'USD': 1.0,
                    'PHP': data.rates.PHP || this.exchangeRates.PHP
                };
                
                this.lastRateUpdate = new Date();
                
                // Save rates to localStorage for offline use
                localStorage.setItem('obu_exchange_rates', JSON.stringify({
                    rates: this.exchangeRates,
                    timestamp: this.lastRateUpdate.toISOString()
                }));
                
                console.log('Exchange rates updated:', this.exchangeRates);
            }
            
        } catch (error) {
            console.error('Failed to update exchange rates:', error);
            
            // Try to load cached rates
            this.loadCachedRates();
        }
    }

    // Load cached exchange rates
    loadCachedRates() {
        try {
            const cached = localStorage.getItem('obu_exchange_rates');
            if (cached) {
                const data = JSON.parse(cached);
                const cacheAge = Date.now() - new Date(data.timestamp).getTime();
                
                // Use cached rates if less than 24 hours old
                if (cacheAge < 86400000) {
                    this.exchangeRates = data.rates;
                    this.lastRateUpdate = new Date(data.timestamp);
                    console.log('Using cached exchange rates');
                }
            }
        } catch (error) {
            console.error('Failed to load cached rates:', error);
        }
    }

    // Set up periodic rate updates
    setupRateUpdates() {
        setInterval(() => {
            this.updateExchangeRates();
        }, this.rateUpdateInterval);
    }

    // Get current currency
    getCurrentCurrency() {
        return this.currentCurrency;
    }

    // Set current currency
    setCurrency(currency) {
        if (!this.currencyConfig[currency]) {
            throw new Error(`Unsupported currency: ${currency}`);
        }
        
        this.currentCurrency = currency;
        this.saveCurrencyPreference();
        this.updateCurrencyDisplay();
        
        // Trigger currency change event
        this.triggerCurrencyChangeEvent();
    }

    // Get available currencies
    getAvailableCurrencies() {
        return Object.keys(this.currencyConfig);
    }

    // Get currency configuration
    getCurrencyConfig(currency = null) {
        if (currency) {
            return this.currencyConfig[currency];
        }
        return this.currencyConfig[this.currentCurrency];
    }

    // Convert amount between currencies
    convertAmount(amount, fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) {
            return amount;
        }
        
        const fromRate = this.exchangeRates[fromCurrency];
        const toRate = this.exchangeRates[toCurrency];
        
        if (!fromRate || !toRate) {
            throw new Error(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`);
        }
        
        // Convert to USD first, then to target currency
        const usdAmount = amount / fromRate;
        const convertedAmount = usdAmount * toRate;
        
        return Math.round(convertedAmount * 100) / 100; // Round to 2 decimals
    }

    // Convert price to current currency
    convertToCurrentCurrency(amount, fromCurrency = 'USD') {
        return this.convertAmount(amount, fromCurrency, this.currentCurrency);
    }

    // Format currency for display
    formatCurrency(amount, currency = null) {
        const currencyCode = currency || this.currentCurrency;
        const config = this.currencyConfig[currencyCode];
        
        if (!config) {
            return `${amount} ${currencyCode}`;
        }
        
        const formattedAmount = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: config.decimals,
            maximumFractionDigits: config.decimals
        }).format(amount);
        
        if (config.position === 'before') {
            return `${config.symbol}${formattedAmount}`;
        } else {
            return `${formattedAmount} ${config.symbol}`;
        }
    }

    // Get exchange rate between currencies
    getExchangeRate(fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) {
            return 1;
        }
        
        const fromRate = this.exchangeRates[fromCurrency];
        const toRate = this.exchangeRates[toCurrency];
        
        if (!fromRate || !toRate) {
            return null;
        }
        
        return toRate / fromRate;
    }

    // Update currency display throughout the UI
    updateCurrencyDisplay() {
        // Update currency selector
        const currencySelector = document.getElementById('currencySelector');
        if (currencySelector) {
            currencySelector.value = this.currentCurrency;
        }
        
        // Update currency symbol in navigation
        const currencySymbol = document.getElementById('currentCurrencySymbol');
        if (currencySymbol) {
            currencySymbol.textContent = this.getCurrencyConfig().symbol;
        }
        
        // Update all price displays
        this.updateAllPrices();
    }

    // Update all price displays on the page
    updateAllPrices() {
        // Update product prices
        const priceElements = document.querySelectorAll('[data-price]');
        priceElements.forEach(element => {
            const originalPrice = parseFloat(element.dataset.price);
            const originalCurrency = element.dataset.currency || 'USD';
            
            if (!isNaN(originalPrice)) {
                const convertedPrice = this.convertAmount(originalPrice, originalCurrency, this.currentCurrency);
                element.textContent = this.formatCurrency(convertedPrice);
            }
        });
        
        // Update cart totals
        if (typeof cartManager !== 'undefined') {
            cartManager.updateCartUI();
        }
        
        // Update product cards
        if (typeof productManager !== 'undefined') {
            productManager.updateProductPrices();
        }
    }

    // Trigger currency change event
    triggerCurrencyChangeEvent() {
        const event = new CustomEvent('currencyChanged', {
            detail: {
                currency: this.currentCurrency,
                config: this.getCurrencyConfig(),
                rates: this.exchangeRates
            }
        });
        
        document.dispatchEvent(event);
    }

    // Get currency conversion info for display
    getConversionInfo(amount, fromCurrency = 'USD') {
        if (fromCurrency === this.currentCurrency) {
            return null;
        }
        
        const rate = this.getExchangeRate(fromCurrency, this.currentCurrency);
        const convertedAmount = this.convertAmount(amount, fromCurrency, this.currentCurrency);
        
        return {
            originalAmount: amount,
            originalCurrency: fromCurrency,
            convertedAmount: convertedAmount,
            targetCurrency: this.currentCurrency,
            exchangeRate: rate,
            lastUpdated: this.lastRateUpdate
        };
    }

    // Create currency selector HTML
    createCurrencySelector() {
        const currencies = this.getAvailableCurrencies();
        
        let options = '';
        currencies.forEach(currency => {
            const config = this.currencyConfig[currency];
            const selected = currency === this.currentCurrency ? 'selected' : '';
            options += `<option value="${currency}" ${selected}>${config.symbol} ${config.name}</option>`;
        });
        
        return `
            <select id="currencySelector" class="currency-selector bg-dark-secondary/50 border border-white/10 rounded-lg px-3 py-2 text-white text-sm">
                ${options}
            </select>
        `;
    }

    // Get status information
    getStatus() {
        return {
            currentCurrency: this.currentCurrency,
            availableCurrencies: this.getAvailableCurrencies(),
            exchangeRates: this.exchangeRates,
            lastRateUpdate: this.lastRateUpdate,
            ratesAge: this.lastRateUpdate ? Date.now() - this.lastRateUpdate.getTime() : null
        };
    }
}

// Create global instance
const currencyManager = new CurrencyManager();

// Export for global access
window.currencyManager = currencyManager;
