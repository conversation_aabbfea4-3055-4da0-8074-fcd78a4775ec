// PayMongo Checkout Component for Obu Marketplace
class PayMongoCheckout {
    constructor() {
        this.isProcessing = false;
        this.currentPaymentIntent = null;
        this.selectedPaymentMethod = null;
    }

    // Show PayMongo checkout modal
    showCheckoutModal(cartSummary) {
        const currentCurrency = typeof currencyManager !== 'undefined' ? currencyManager.getCurrentCurrency() : 'USD';
        const availablePaymentMethods = payMongoService.getPaymentMethodsForCurrency(currentCurrency);
        
        const modalContent = `
            <div class="paymongo-checkout">
                <!-- Currency Selector -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-white/80 mb-3">Currency</label>
                    <div class="flex items-center space-x-4">
                        ${this.createCurrencySelector()}
                        <div class="text-xs text-white/60">
                            <i class="fas fa-info-circle mr-1"></i>
                            Real-time exchange rates
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-white mb-4">Order Summary</h4>
                    <div class="bg-dark-primary/30 rounded-xl p-4 mb-4 max-h-48 overflow-y-auto">
                        ${cartSummary.items.map(({ item, product }) => `
                            <div class="flex justify-between items-center py-2 border-b border-white/10 last:border-b-0">
                                <div>
                                    <div class="text-white font-medium">${product.title}</div>
                                    <div class="text-sm text-white/60">Qty: ${item.quantity} × ${this.formatProductPrice(product)}</div>
                                </div>
                                <div class="text-white font-semibold">
                                    ${this.formatProductPrice(product, item.quantity)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-white/80">
                            <span>Subtotal:</span>
                            <span data-price="${cartSummary.subtotal}" data-currency="${currentCurrency}">${cartManager.formatPrice(cartSummary.subtotal)}</span>
                        </div>
                        <div class="flex justify-between text-white/80">
                            <span>Tax (10%):</span>
                            <span data-price="${cartSummary.tax}" data-currency="${currentCurrency}">${cartManager.formatPrice(cartSummary.tax)}</span>
                        </div>
                        <div class="flex justify-between text-xl font-bold text-white border-t border-white/10 pt-2">
                            <span>Total:</span>
                            <span class="text-cyan-400" data-price="${cartSummary.total}" data-currency="${currentCurrency}">${cartManager.formatPrice(cartSummary.total)}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Method Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-white/80 mb-3">Payment Method</label>
                    <div class="grid grid-cols-1 gap-3">
                        ${Object.entries(availablePaymentMethods).map(([key, method]) => `
                            <label class="payment-method-option cursor-pointer">
                                <input type="radio" name="paymentMethod" value="${key}" class="sr-only">
                                <div class="payment-method-card p-4 border border-white/10 rounded-xl hover:border-cyan-400/50 transition-all duration-300">
                                    <div class="flex items-center space-x-3">
                                        <i class="${method.icon} text-xl text-cyan-400"></i>
                                        <span class="text-white font-medium">${method.name}</span>
                                        <div class="ml-auto">
                                            <div class="w-4 h-4 border-2 border-white/30 rounded-full payment-radio"></div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        `).join('')}
                    </div>
                </div>

                <!-- Card Payment Form (shown when card is selected) -->
                <div id="cardPaymentForm" class="hidden mb-6">
                    <div class="bg-dark-primary/30 rounded-xl p-4">
                        <h5 class="text-white font-medium mb-4">Card Information</h5>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm text-white/70 mb-2">Card Number</label>
                                <input type="text" 
                                       id="cardNumber" 
                                       placeholder="1234 5678 9012 3456"
                                       maxlength="19"
                                       class="modal-input w-full px-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                                <div class="text-xs text-white/50 mt-1">
                                    <span id="cardType"></span>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm text-white/70 mb-2">Expiry Date</label>
                                    <input type="text" 
                                           id="cardExpiry" 
                                           placeholder="MM/YY"
                                           maxlength="5"
                                           class="modal-input w-full px-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                                </div>
                                <div>
                                    <label class="block text-sm text-white/70 mb-2">CVC</label>
                                    <input type="text" 
                                           id="cardCvc" 
                                           placeholder="123"
                                           maxlength="4"
                                           class="modal-input w-full px-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-white/70 mb-2">Cardholder Name</label>
                                <input type="text" 
                                       id="cardholderName" 
                                       placeholder="John Doe"
                                       class="modal-input w-full px-4 py-3 bg-dark-secondary/50 border border-white/10 rounded-xl text-white placeholder-white/50 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-6">
                    <label class="flex items-start">
                        <input type="checkbox" 
                               id="agreeTerms" 
                               required 
                               class="mt-1 mr-3 accent-cyan-400">
                        <span class="text-sm text-white/70 leading-relaxed">
                            I agree to the terms of service and understand that payment processing fees may apply. 
                            All transactions are processed securely through PayMongo.
                        </span>
                    </label>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-4">
                    <button id="payNowBtn" 
                            onclick="payMongoCheckout.processPayment()" 
                            class="btn-primary flex-1 py-3 rounded-xl font-semibold">
                        <i class="fas fa-lock mr-2"></i>
                        <span id="payBtnText">Pay ${cartManager.formatPrice(cartSummary.total)}</span>
                    </button>
                    <button type="button" 
                            onclick="document.getElementById('payMongoCheckoutModal').classList.add('hidden')" 
                            class="btn-secondary px-6 py-3 rounded-xl font-semibold">
                        Cancel
                    </button>
                </div>

                <!-- PayMongo Branding -->
                <div class="text-center mt-4">
                    <div class="text-xs text-white/50">
                        Secured by <span class="text-cyan-400 font-medium">PayMongo</span>
                    </div>
                </div>
            </div>
        `;

        // Show modal
        cartManager.showModal('payMongoCheckoutModal', 'Secure Checkout', modalContent);

        // Setup event listeners
        this.setupEventListeners();
    }

    // Create currency selector
    createCurrencySelector() {
        if (typeof currencyManager === 'undefined') {
            return '<span class="text-white">USD</span>';
        }

        const currencies = currencyManager.getAvailableCurrencies();
        const currentCurrency = currencyManager.getCurrentCurrency();

        return `
            <select id="checkoutCurrencySelector" 
                    onchange="payMongoCheckout.handleCurrencyChange(this.value)"
                    class="bg-dark-secondary/50 border border-white/10 rounded-lg px-3 py-2 text-white text-sm">
                ${currencies.map(currency => {
                    const config = currencyManager.getCurrencyConfig(currency);
                    const selected = currency === currentCurrency ? 'selected' : '';
                    return `<option value="${currency}" ${selected}>${config.symbol} ${config.name}</option>`;
                }).join('')}
            </select>
        `;
    }

    // Format product price with currency conversion
    formatProductPrice(product, quantity = 1) {
        let price = product.price * quantity;
        if (typeof currencyManager !== 'undefined') {
            price = currencyManager.convertToCurrentCurrency(price, 'USD');
        }
        return cartManager.formatPrice(price);
    }

    // Setup event listeners
    setupEventListeners() {
        // Payment method selection
        document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handlePaymentMethodChange(e.target.value);
            });
        });

        // Card input formatting
        this.setupCardInputFormatting();
    }

    // Handle payment method change
    handlePaymentMethodChange(method) {
        this.selectedPaymentMethod = method;

        // Update UI
        document.querySelectorAll('.payment-method-card').forEach(card => {
            card.classList.remove('border-cyan-400', 'bg-cyan-400/10');
            card.classList.add('border-white/10');
        });

        const selectedCard = document.querySelector(`input[value="${method}"]`).closest('.payment-method-card');
        selectedCard.classList.remove('border-white/10');
        selectedCard.classList.add('border-cyan-400', 'bg-cyan-400/10');

        // Show/hide card form
        const cardForm = document.getElementById('cardPaymentForm');
        if (method === 'card') {
            cardForm.classList.remove('hidden');
        } else {
            cardForm.classList.add('hidden');
        }

        // Update radio button appearance
        document.querySelectorAll('.payment-radio').forEach(radio => {
            radio.classList.remove('bg-cyan-400', 'border-cyan-400');
            radio.classList.add('border-white/30');
        });

        const selectedRadio = selectedCard.querySelector('.payment-radio');
        selectedRadio.classList.remove('border-white/30');
        selectedRadio.classList.add('bg-cyan-400', 'border-cyan-400');
    }

    // Handle currency change
    handleCurrencyChange(newCurrency) {
        if (typeof currencyManager !== 'undefined') {
            currencyManager.setCurrency(newCurrency);
            
            // Update all price displays
            currencyManager.updateAllPrices();
            
            // Update pay button text
            const cartSummary = cartManager.getCartSummary();
            document.getElementById('payBtnText').textContent = `Pay ${cartManager.formatPrice(cartSummary.total)}`;
        }
    }

    // Setup card input formatting
    setupCardInputFormatting() {
        const cardNumberInput = document.getElementById('cardNumber');
        const cardExpiryInput = document.getElementById('cardExpiry');
        const cardCvcInput = document.getElementById('cardCvc');

        if (cardNumberInput) {
            cardNumberInput.addEventListener('input', (e) => {
                let value = e.target.value.replace(/\s/g, '');
                let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
                e.target.value = formattedValue;

                // Update card type
                const cardType = payMongoService.getCardType(value);
                document.getElementById('cardType').textContent = cardType !== 'unknown' ? cardType.toUpperCase() : '';
            });
        }

        if (cardExpiryInput) {
            cardExpiryInput.addEventListener('input', (e) => {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                e.target.value = value;
            });
        }

        if (cardCvcInput) {
            cardCvcInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/\D/g, '');
            });
        }
    }

    // Process payment
    async processPayment() {
        if (this.isProcessing) return;

        try {
            this.isProcessing = true;
            this.updatePayButton(true);

            // Validate form
            if (!this.validatePaymentForm()) {
                return;
            }

            const cartSummary = cartManager.getCartSummary();
            const currentCurrency = typeof currencyManager !== 'undefined' ? currencyManager.getCurrentCurrency() : 'USD';

            if (this.selectedPaymentMethod === 'card') {
                await this.processCardPayment(cartSummary, currentCurrency);
            } else {
                await this.processEWalletPayment(cartSummary, currentCurrency);
            }

        } catch (error) {
            console.error('Payment processing error:', error);
            Components.createNotification('Payment failed: ' + error.message, 'error');
        } finally {
            this.isProcessing = false;
            this.updatePayButton(false);
        }
    }

    // Process card payment
    async processCardPayment(cartSummary, currency) {
        // Create payment intent
        const paymentIntentResult = await payMongoService.createPaymentIntent({
            amount: cartSummary.total,
            currency: currency,
            description: `Obu Marketplace Purchase - ${cartSummary.itemCount} items`,
            metadata: {
                userId: authManager.currentUser.id,
                userEmail: authManager.currentUser.email,
                itemCount: cartSummary.itemCount
            }
        });

        if (!paymentIntentResult.success) {
            throw new Error(paymentIntentResult.error);
        }

        this.currentPaymentIntent = paymentIntentResult.paymentIntent;

        // Create payment method
        const cardData = this.getCardData();
        const paymentMethodResult = await payMongoService.createPaymentMethod(cardData);

        if (!paymentMethodResult.success) {
            throw new Error(paymentMethodResult.error);
        }

        // Attach payment method to intent
        const attachResult = await payMongoService.attachPaymentMethod(
            this.currentPaymentIntent.id,
            paymentMethodResult.paymentMethod.id
        );

        if (!attachResult.success) {
            throw new Error(attachResult.error);
        }

        // Complete the purchase
        await this.completePurchase(cartSummary, currency, 'card');
    }

    // Process e-wallet payment
    async processEWalletPayment(cartSummary, currency) {
        const sourceResult = await payMongoService.createSource({
            type: this.selectedPaymentMethod,
            amount: cartSummary.total,
            currency: currency,
            redirect: {
                success: `${window.location.origin}/payment-success`,
                failed: `${window.location.origin}/payment-failed`
            }
        });

        if (!sourceResult.success) {
            throw new Error(sourceResult.error);
        }

        // Redirect to payment page
        if (sourceResult.checkoutUrl) {
            window.open(sourceResult.checkoutUrl, '_blank');
            Components.createNotification('Redirecting to payment page...', 'info');
        }
    }

    // Get card data from form
    getCardData() {
        const cardNumber = document.getElementById('cardNumber').value;
        const cardExpiry = document.getElementById('cardExpiry').value;
        const cardCvc = document.getElementById('cardCvc').value;
        const cardholderName = document.getElementById('cardholderName').value;

        const [expiryMonth, expiryYear] = cardExpiry.split('/');

        return {
            cardNumber,
            expiryMonth,
            expiryYear: '20' + expiryYear,
            cvc: cardCvc,
            cardholderName
        };
    }

    // Validate payment form
    validatePaymentForm() {
        if (!this.selectedPaymentMethod) {
            Components.createNotification('Please select a payment method', 'error');
            return false;
        }

        if (!document.getElementById('agreeTerms').checked) {
            Components.createNotification('Please agree to the terms and conditions', 'error');
            return false;
        }

        if (this.selectedPaymentMethod === 'card') {
            return this.validateCardForm();
        }

        return true;
    }

    // Validate card form
    validateCardForm() {
        const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
        const cardExpiry = document.getElementById('cardExpiry').value;
        const cardCvc = document.getElementById('cardCvc').value;
        const cardholderName = document.getElementById('cardholderName').value;

        if (!cardNumber || !payMongoService.validateCardNumber(cardNumber)) {
            Components.createNotification('Please enter a valid card number', 'error');
            return false;
        }

        if (!cardExpiry || !/^\d{2}\/\d{2}$/.test(cardExpiry)) {
            Components.createNotification('Please enter a valid expiry date (MM/YY)', 'error');
            return false;
        }

        if (!cardCvc || cardCvc.length < 3) {
            Components.createNotification('Please enter a valid CVC', 'error');
            return false;
        }

        if (!cardholderName.trim()) {
            Components.createNotification('Please enter the cardholder name', 'error');
            return false;
        }

        return true;
    }

    // Update pay button state
    updatePayButton(isProcessing) {
        const payBtn = document.getElementById('payNowBtn');
        const payBtnText = document.getElementById('payBtnText');

        if (isProcessing) {
            payBtn.disabled = true;
            payBtnText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        } else {
            payBtn.disabled = false;
            const cartSummary = cartManager.getCartSummary();
            payBtnText.textContent = `Pay ${cartManager.formatPrice(cartSummary.total)}`;
        }
    }

    // Complete purchase after successful payment
    async completePurchase(cartSummary, currency, paymentMethod) {
        try {
            // Create purchases in the system
            const purchases = [];

            cartManager.cart.forEach(cartItem => {
                const product = storage.getProductById(cartItem.productId);
                if (!product) return;

                for (let i = 0; i < cartItem.quantity; i++) {
                    let productPrice = product.price;
                    if (typeof currencyManager !== 'undefined') {
                        productPrice = currencyManager.convertToCurrentCurrency(product.price, 'USD');
                    }

                    const subtotal = productPrice;
                    const tax = Utils.calculateTax(subtotal);
                    const totalAmount = subtotal + tax;

                    const purchase = storage.addPurchase({
                        buyerId: authManager.currentUser.id,
                        buyerName: authManager.currentUser.name,
                        buyerEmail: authManager.currentUser.email,
                        productId: product.id,
                        productTitle: product.title,
                        productImage: product.image,
                        subtotal,
                        tax,
                        totalAmount,
                        currency,
                        paymentMethod: `paymongo_${paymentMethod}`,
                        paymentIntentId: this.currentPaymentIntent?.id,
                        status: 'completed',
                        downloadUrl: product.downloadUrl || null,
                        instant: product.instant || false
                    });

                    purchases.push(purchase);
                }

                // Update product stock
                storage.updateProduct(cartItem.productId, {
                    stock: product.stock - cartItem.quantity
                });
            });

            // Clear cart
            cartManager.clearCart();

            // Close modal
            document.getElementById('payMongoCheckoutModal').classList.add('hidden');

            // Show success
            Components.createNotification(
                `Payment successful! ${purchases.length} item(s) purchased`,
                'success'
            );

            // Show purchase confirmation
            setTimeout(() => {
                cartManager.showPurchaseConfirmation(purchases);
            }, 1000);

        } catch (error) {
            console.error('Purchase completion error:', error);
            Components.createNotification('Payment processed but order completion failed', 'error');
        }
    }
}

// Create global instance
const payMongoCheckout = new PayMongoCheckout();

// Export for global access
window.payMongoCheckout = payMongoCheckout;
