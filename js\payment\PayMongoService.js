// PayMongo Payment Service for Obu Marketplace
class PayMongoService {
    constructor() {
        // PayMongo API Configuration
        this.apiBaseUrl = 'https://api.paymongo.com/v1';
        this.publicKey = 'pk_test_qSD8DZ9emKDb5M9Q9WbV64C7_KEY'; // Replace with actual key
        this.secretKey = 'sk_test_7yBym3vNpY1fEvszHBighiCT'; // Replace with actual key
        
        // Supported currencies and payment methods
        this.supportedCurrencies = {
            'PHP': {
                name: 'Philippine Peso',
                symbol: '₱',
                code: 'PHP',
                minAmount: 100, // 1 PHP in centavos
                maxAmount: 10000000 // 100,000 PHP in centavos
            },
            'USD': {
                name: 'US Dollar',
                symbol: '$',
                code: 'USD',
                minAmount: 100, // $1 in cents
                maxAmount: 1000000 // $10,000 in cents
            }
        };
        
        this.paymentMethods = {
            'card': {
                name: 'Credit/Debit Card',
                icon: 'fas fa-credit-card',
                currencies: ['PHP', 'USD']
            },
            'gcash': {
                name: 'GCash',
                icon: 'fab fa-google-pay',
                currencies: ['PHP']
            },
            'paymaya': {
                name: 'PayMaya',
                icon: 'fas fa-mobile-alt',
                currencies: ['PHP']
            },
            'grabpay': {
                name: 'GrabPay',
                icon: 'fas fa-car',
                currencies: ['PHP']
            },
            'billease': {
                name: 'Billease',
                icon: 'fas fa-receipt',
                currencies: ['PHP']
            }
        };
        
        this.isInitialized = false;
    }

    // Initialize PayMongo service
    async init() {
        try {
            // Validate API keys
            if (this.publicKey.includes('YOUR_') || this.secretKey.includes('YOUR_')) {
                console.warn('PayMongo API keys not configured. Using test mode.');
                return false;
            }
            
            // Test API connection
            const isConnected = await this.testConnection();
            this.isInitialized = isConnected;
            
            return isConnected;
        } catch (error) {
            console.error('PayMongo initialization failed:', error);
            return false;
        }
    }

    // Test PayMongo API connection
    async testConnection() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/payment_methods`, {
                method: 'GET',
                headers: {
                    'Authorization': `Basic ${btoa(this.publicKey + ':')}`
                }
            });
            
            return response.ok;
        } catch (error) {
            console.error('PayMongo connection test failed:', error);
            return false;
        }
    }

    // Get supported currencies
    getSupportedCurrencies() {
        return this.supportedCurrencies;
    }

    // Get payment methods for currency
    getPaymentMethodsForCurrency(currency) {
        return Object.entries(this.paymentMethods)
            .filter(([key, method]) => method.currencies.includes(currency))
            .reduce((acc, [key, method]) => {
                acc[key] = method;
                return acc;
            }, {});
    }

    // Convert amount to PayMongo format (smallest currency unit)
    convertToPayMongoAmount(amount, currency) {
        const currencyInfo = this.supportedCurrencies[currency];
        if (!currencyInfo) {
            throw new Error(`Unsupported currency: ${currency}`);
        }
        
        // Convert to smallest unit (centavos for PHP, cents for USD)
        const paymongoAmount = Math.round(amount * 100);
        
        // Validate amount limits
        if (paymongoAmount < currencyInfo.minAmount) {
            throw new Error(`Amount too small. Minimum: ${currencyInfo.minAmount / 100} ${currency}`);
        }
        
        if (paymongoAmount > currencyInfo.maxAmount) {
            throw new Error(`Amount too large. Maximum: ${currencyInfo.maxAmount / 100} ${currency}`);
        }
        
        return paymongoAmount;
    }

    // Create payment intent
    async createPaymentIntent(paymentData) {
        try {
            const { amount, currency, description, metadata = {} } = paymentData;
            
            const paymongoAmount = this.convertToPayMongoAmount(amount, currency);
            
            const payload = {
                data: {
                    attributes: {
                        amount: paymongoAmount,
                        payment_method_allowed: this.getPaymentMethodsForCurrency(currency),
                        payment_method_options: {
                            card: {
                                request_three_d_secure: 'automatic'
                            }
                        },
                        currency: currency.toLowerCase(),
                        description: description,
                        statement_descriptor: 'Obu Marketplace',
                        metadata: {
                            ...metadata,
                            marketplace: 'obu',
                            timestamp: new Date().toISOString()
                        }
                    }
                }
            };

            const response = await fetch(`${this.apiBaseUrl}/payment_intents`, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${btoa(this.secretKey + ':')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.errors?.[0]?.detail || 'Payment intent creation failed');
            }

            const result = await response.json();
            return {
                success: true,
                paymentIntent: result.data,
                clientSecret: result.data.attributes.client_key
            };

        } catch (error) {
            console.error('PayMongo payment intent creation failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Create payment method (for card payments)
    async createPaymentMethod(cardData) {
        try {
            const { cardNumber, expiryMonth, expiryYear, cvc, cardholderName } = cardData;
            
            const payload = {
                data: {
                    attributes: {
                        type: 'card',
                        details: {
                            card_number: cardNumber.replace(/\s/g, ''),
                            exp_month: parseInt(expiryMonth),
                            exp_year: parseInt(expiryYear),
                            cvc: cvc
                        },
                        billing: {
                            name: cardholderName
                        }
                    }
                }
            };

            const response = await fetch(`${this.apiBaseUrl}/payment_methods`, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${btoa(this.publicKey + ':')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.errors?.[0]?.detail || 'Payment method creation failed');
            }

            const result = await response.json();
            return {
                success: true,
                paymentMethod: result.data
            };

        } catch (error) {
            console.error('PayMongo payment method creation failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Attach payment method to payment intent
    async attachPaymentMethod(paymentIntentId, paymentMethodId) {
        try {
            const payload = {
                data: {
                    attributes: {
                        payment_method: paymentMethodId
                    }
                }
            };

            const response = await fetch(`${this.apiBaseUrl}/payment_intents/${paymentIntentId}/attach`, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${btoa(this.secretKey + ':')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.errors?.[0]?.detail || 'Payment method attachment failed');
            }

            const result = await response.json();
            return {
                success: true,
                paymentIntent: result.data
            };

        } catch (error) {
            console.error('PayMongo payment method attachment failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Create source for e-wallet payments
    async createSource(sourceData) {
        try {
            const { type, amount, currency, redirect } = sourceData;
            
            const paymongoAmount = this.convertToPayMongoAmount(amount, currency);
            
            const payload = {
                data: {
                    attributes: {
                        type: type,
                        amount: paymongoAmount,
                        currency: currency.toLowerCase(),
                        redirect: {
                            success: redirect.success,
                            failed: redirect.failed
                        }
                    }
                }
            };

            const response = await fetch(`${this.apiBaseUrl}/sources`, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${btoa(this.secretKey + ':')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.errors?.[0]?.detail || 'Source creation failed');
            }

            const result = await response.json();
            return {
                success: true,
                source: result.data,
                checkoutUrl: result.data.attributes.redirect?.checkout_url
            };

        } catch (error) {
            console.error('PayMongo source creation failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get payment intent status
    async getPaymentIntent(paymentIntentId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/payment_intents/${paymentIntentId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Basic ${btoa(this.secretKey + ':')}`
                }
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.errors?.[0]?.detail || 'Failed to retrieve payment intent');
            }

            const result = await response.json();
            return {
                success: true,
                paymentIntent: result.data
            };

        } catch (error) {
            console.error('PayMongo payment intent retrieval failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Format currency amount for display
    formatCurrency(amount, currency) {
        const currencyInfo = this.supportedCurrencies[currency];
        if (!currencyInfo) {
            return `${amount} ${currency}`;
        }
        
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            currencyDisplay: 'symbol'
        }).format(amount);
    }

    // Validate card number using Luhn algorithm
    validateCardNumber(cardNumber) {
        const number = cardNumber.replace(/\s/g, '');
        
        if (!/^\d+$/.test(number)) {
            return false;
        }
        
        let sum = 0;
        let isEven = false;
        
        for (let i = number.length - 1; i >= 0; i--) {
            let digit = parseInt(number[i]);
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0;
    }

    // Get card type from number
    getCardType(cardNumber) {
        const number = cardNumber.replace(/\s/g, '');
        
        if (/^4/.test(number)) return 'visa';
        if (/^5[1-5]/.test(number)) return 'mastercard';
        if (/^3[47]/.test(number)) return 'amex';
        if (/^6(?:011|5)/.test(number)) return 'discover';
        
        return 'unknown';
    }

    // Check if service is available
    isAvailable() {
        return this.isInitialized;
    }

    // Get service status
    getStatus() {
        return {
            initialized: this.isInitialized,
            supportedCurrencies: Object.keys(this.supportedCurrencies),
            paymentMethods: Object.keys(this.paymentMethods)
        };
    }
}

// Create global instance
const payMongoService = new PayMongoService();

// Export for global access
window.payMongoService = payMongoService;
