// Enhanced Social Authentication Error Handler for Obu Marketplace
class SocialAuthHandler {
    constructor() {
        this.providerStatus = {
            google: { enabled: false, tested: false, error: null },
            apple: { enabled: false, tested: false, error: null }
        };
        this.fallbackMode = false;
        this.init();
    }

    init() {
        console.log('🔧 Initializing Social Auth Handler...');
        
        // Test provider availability on load
        setTimeout(() => {
            this.testAllProviders();
        }, 1000);

        // Listen for auth errors
        this.setupErrorHandling();
    }

    // Test all social auth providers
    async testAllProviders() {
        console.log('🧪 Testing social authentication providers...');
        
        if (!this.isSupabaseAvailable()) {
            this.enableFallbackMode('Supabase not available');
            return;
        }

        // Test Google provider
        await this.testProvider('google');
        
        // Test Apple provider  
        await this.testProvider('apple');

        // Update UI based on results
        this.updateSocialButtonsUI();
        
        console.log('📊 Provider test results:', this.providerStatus);
    }

    // Test individual provider
    async testProvider(provider) {
        try {
            console.log(`🔍 Testing ${provider} provider...`);
            
            if (!supabaseIntegration.isConnected()) {
                throw new Error('Supabase not connected');
            }

            // Attempt to initiate OAuth flow with skipBrowserRedirect
            const { data, error } = await supabaseIntegration.supabase.auth.signInWithOAuth({
                provider: provider,
                options: {
                    redirectTo: window.location.origin,
                    skipBrowserRedirect: true // Don't actually redirect
                }
            });

            if (error) {
                this.handleProviderError(provider, error);
                return false;
            }

            this.providerStatus[provider] = {
                enabled: true,
                tested: true,
                error: null
            };

            console.log(`✅ ${provider} provider is configured`);
            return true;

        } catch (error) {
            this.handleProviderError(provider, error);
            return false;
        }
    }

    // Handle provider-specific errors
    handleProviderError(provider, error) {
        console.warn(`⚠️ ${provider} provider error:`, error);
        
        let errorMessage = 'Unknown error';
        let userMessage = '';

        if (error.message) {
            if (error.message.includes('provider is not enabled')) {
                errorMessage = 'Provider not enabled in Supabase dashboard';
                userMessage = `${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In is not configured. Please use email/password login.`;
            } else if (error.message.includes('Invalid login credentials')) {
                errorMessage = 'Invalid OAuth configuration';
                userMessage = `${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In configuration is invalid.`;
            } else if (error.message.includes('redirect_uri_mismatch')) {
                errorMessage = 'Redirect URI mismatch';
                userMessage = `${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In redirect URL needs to be configured.`;
            } else {
                errorMessage = error.message;
                userMessage = `${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In is temporarily unavailable.`;
            }
        }

        this.providerStatus[provider] = {
            enabled: false,
            tested: true,
            error: errorMessage,
            userMessage: userMessage
        };
    }

    // Check if Supabase is available
    isSupabaseAvailable() {
        return typeof supabaseIntegration !== 'undefined' && 
               supabaseIntegration.isConnected();
    }

    // Enable fallback mode
    enableFallbackMode(reason) {
        console.log(`🔄 Enabling fallback mode: ${reason}`);
        this.fallbackMode = true;
        
        // Disable all social providers
        Object.keys(this.providerStatus).forEach(provider => {
            this.providerStatus[provider] = {
                enabled: false,
                tested: true,
                error: reason,
                userMessage: 'Social login requires Supabase configuration. Please use email/password login.'
            };
        });

        this.updateSocialButtonsUI();
    }

    // Update social buttons UI based on provider status
    updateSocialButtonsUI() {
        console.log('🎨 Updating social buttons UI...');
        
        // Update Google buttons
        this.updateProviderButton('google');
        
        // Update Apple buttons
        this.updateProviderButton('apple');
    }

    // Update individual provider button
    updateProviderButton(provider) {
        const buttons = document.querySelectorAll(`[onclick*="signInWith${provider.charAt(0).toUpperCase() + provider.slice(1)}"]`);
        const status = this.providerStatus[provider];

        buttons.forEach(button => {
            if (!status.enabled) {
                // Disable button and show why
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.title = status.userMessage || `${provider} Sign-In not available`;
                
                // Add visual indicator
                const icon = button.querySelector('svg');
                if (icon) {
                    icon.classList.add('opacity-50');
                }

                // Update click handler to show error message
                button.onclick = (e) => {
                    e.preventDefault();
                    this.showProviderError(provider);
                };
            } else {
                // Enable button
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                button.title = `Sign in with ${provider.charAt(0).toUpperCase() + provider.slice(1)}`;
                
                const icon = button.querySelector('svg');
                if (icon) {
                    icon.classList.remove('opacity-50');
                }
            }
        });
    }

    // Show provider error message
    showProviderError(provider) {
        const status = this.providerStatus[provider];
        const message = status.userMessage || `${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In is not available`;
        
        Components.createNotification(message, 'warning');
        
        // Show configuration help if needed
        if (status.error && status.error.includes('not enabled')) {
            setTimeout(() => {
                this.showConfigurationHelp(provider);
            }, 2000);
        }
    }

    // Show configuration help
    showConfigurationHelp(provider) {
        const helpModal = document.createElement('div');
        helpModal.className = 'modal-overlay';
        helpModal.innerHTML = `
            <div class="modal-card max-w-2xl">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-white">
                            ${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In Configuration
                        </h3>
                        <button onclick="this.closest('.modal-overlay').remove()" 
                                class="text-white/60 hover:text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="text-white/80 space-y-4">
                        <p>To enable ${provider.charAt(0).toUpperCase() + provider.slice(1)} Sign-In, please configure it in your Supabase dashboard:</p>
                        
                        <div class="bg-dark-primary/50 rounded-lg p-4">
                            <h4 class="font-semibold text-cyan-400 mb-2">Configuration Steps:</h4>
                            <ol class="list-decimal list-inside space-y-2 text-sm">
                                <li>Go to <a href="https://supabase.com/dashboard" target="_blank" class="text-cyan-400 hover:underline">Supabase Dashboard</a></li>
                                <li>Navigate to Authentication → Providers</li>
                                <li>Enable "${provider.charAt(0).toUpperCase() + provider.slice(1)}" provider</li>
                                ${provider === 'google' ? `
                                <li>Add your Google OAuth Client ID and Secret</li>
                                <li>Set redirect URL: <code class="bg-black/30 px-1 rounded">https://rvdjfqlydmnlblpbjjlc.supabase.co/auth/v1/callback</code></li>
                                ` : `
                                <li>Add your Apple Service ID, Team ID, Key ID, and Private Key</li>
                                <li>Set redirect URL: <code class="bg-black/30 px-1 rounded">https://rvdjfqlydmnlblpbjjlc.supabase.co/auth/v1/callback</code></li>
                                `}
                                <li>Update Site URL to include your domain</li>
                            </ol>
                        </div>
                        
                        <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-400 mb-2">Alternative Options:</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm">
                                <li>Use email/password registration and login</li>
                                <li>Create an account with your email address</li>
                                <li>All marketplace features work with email authentication</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button onclick="this.closest('.modal-overlay').remove()" 
                                class="btn-secondary px-6 py-2 rounded-lg">
                            Close
                        </button>
                        <button onclick="authManager.showRegisterModal(); this.closest('.modal-overlay').remove();" 
                                class="btn-primary px-6 py-2 rounded-lg">
                            Use Email Registration
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);
    }

    // Enhanced social auth methods with error handling
    async handleGoogleSignIn() {
        console.log('🔍 Handling Google Sign-In...');
        
        if (!this.providerStatus.google.enabled) {
            this.showProviderError('google');
            return { success: false, error: 'Provider not enabled' };
        }

        try {
            if (this.isSupabaseAvailable()) {
                const result = await supabaseIntegration.signInWithGoogle();
                
                if (result.success) {
                    Components.createNotification('Redirecting to Google...', 'info');
                    return result;
                } else {
                    throw new Error(result.error);
                }
            } else {
                throw new Error('Supabase not available');
            }
        } catch (error) {
            console.error('Google sign-in error:', error);
            
            // Handle specific errors
            if (error.message.includes('provider is not enabled')) {
                this.handleProviderError('google', error);
                this.updateProviderButton('google');
                this.showProviderError('google');
            } else {
                Components.createNotification('Google sign-in failed. Please try email/password login.', 'error');
            }
            
            return { success: false, error: error.message };
        }
    }

    async handleAppleSignIn() {
        console.log('🔍 Handling Apple Sign-In...');
        
        if (!this.providerStatus.apple.enabled) {
            this.showProviderError('apple');
            return { success: false, error: 'Provider not enabled' };
        }

        try {
            if (this.isSupabaseAvailable()) {
                const result = await supabaseIntegration.signInWithApple();
                
                if (result.success) {
                    Components.createNotification('Redirecting to Apple...', 'info');
                    return result;
                } else {
                    throw new Error(result.error);
                }
            } else {
                throw new Error('Supabase not available');
            }
        } catch (error) {
            console.error('Apple sign-in error:', error);
            
            // Handle specific errors
            if (error.message.includes('provider is not enabled')) {
                this.handleProviderError('apple', error);
                this.updateProviderButton('apple');
                this.showProviderError('apple');
            } else {
                Components.createNotification('Apple sign-in failed. Please try email/password login.', 'error');
            }
            
            return { success: false, error: error.message };
        }
    }

    // Setup global error handling
    setupErrorHandling() {
        // Listen for unhandled auth errors
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.message) {
                if (event.reason.message.includes('provider is not enabled')) {
                    console.warn('🚨 Caught unhandled social auth error:', event.reason);
                    event.preventDefault(); // Prevent console error
                    
                    // Show user-friendly message
                    Components.createNotification(
                        'Social login is not configured. Please use email/password login.',
                        'warning'
                    );
                }
            }
        });
    }

    // Get provider status
    getProviderStatus(provider) {
        return this.providerStatus[provider] || { enabled: false, tested: false, error: 'Unknown provider' };
    }

    // Get all provider statuses
    getAllProviderStatuses() {
        return {
            ...this.providerStatus,
            fallbackMode: this.fallbackMode,
            supabaseAvailable: this.isSupabaseAvailable()
        };
    }

    // Retry provider test
    async retryProvider(provider) {
        console.log(`🔄 Retrying ${provider} provider test...`);
        await this.testProvider(provider);
        this.updateProviderButton(provider);
        return this.providerStatus[provider];
    }

    // Retry all providers
    async retryAllProviders() {
        console.log('🔄 Retrying all provider tests...');
        await this.testAllProviders();
        return this.getAllProviderStatuses();
    }
}

// Create global instance
const socialAuthHandler = new SocialAuthHandler();

// Export for global access
window.socialAuthHandler = socialAuthHandler;

// Add global helper functions
window.testSocialProviders = () => socialAuthHandler.testAllProviders();
window.getSocialAuthStatus = () => socialAuthHandler.getAllProviderStatuses();
window.retrySocialAuth = (provider) => socialAuthHandler.retryProvider(provider);
