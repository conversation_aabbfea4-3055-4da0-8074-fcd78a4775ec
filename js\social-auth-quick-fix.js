// Quick Fix for Supabase Social Authentication Provider Error
// This script provides immediate error handling and fallback for social auth issues

(function() {
    'use strict';

    console.log('🔧 Loading Social Auth Quick Fix...');

    // Configuration
    const CONFIG = {
        enableFallback: true,
        showConfigHelp: true,
        autoDetectErrors: true,
        gracefulDegradation: true
    };

    // Error patterns to detect
    const ERROR_PATTERNS = {
        providerNotEnabled: /provider is not enabled/i,
        redirectMismatch: /redirect_uri_mismatch/i,
        invalidCredentials: /invalid.*credentials/i,
        configurationError: /configuration.*error/i
    };

    // Quick fix implementation
    class SocialAuthQuickFix {
        constructor() {
            this.errorCount = 0;
            this.lastError = null;
            this.fallbackActive = false;
            this.init();
        }

        init() {
            console.log('🚀 Initializing Social Auth Quick Fix...');
            
            // Setup error interception
            this.setupErrorInterception();
            
            // Setup UI enhancements
            this.setupUIEnhancements();
            
            // Test providers after page load
            setTimeout(() => {
                this.quickProviderTest();
            }, 1500);
        }

        // Intercept and handle social auth errors
        setupErrorInterception() {
            // Intercept unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                if (this.isSocialAuthError(event.reason)) {
                    console.warn('🚨 Intercepted social auth error:', event.reason);
                    event.preventDefault(); // Prevent console error
                    this.handleSocialAuthError(event.reason);
                }
            });

            // Intercept console errors
            const originalError = console.error;
            console.error = (...args) => {
                const errorMessage = args.join(' ');
                if (this.isSocialAuthError({ message: errorMessage })) {
                    this.handleSocialAuthError({ message: errorMessage });
                }
                originalError.apply(console, args);
            };

            // Override fetch to catch API errors
            const originalFetch = window.fetch;
            window.fetch = async (...args) => {
                try {
                    const response = await originalFetch.apply(window, args);
                    
                    // Check for auth API errors
                    if (args[0] && args[0].includes('/auth/v1/') && !response.ok) {
                        const errorData = await response.clone().json().catch(() => ({}));
                        if (this.isSocialAuthError(errorData)) {
                            this.handleSocialAuthError(errorData);
                        }
                    }
                    
                    return response;
                } catch (error) {
                    if (this.isSocialAuthError(error)) {
                        this.handleSocialAuthError(error);
                    }
                    throw error;
                }
            };
        }

        // Check if error is related to social authentication
        isSocialAuthError(error) {
            if (!error) return false;
            
            const message = error.message || error.msg || JSON.stringify(error);
            
            return Object.values(ERROR_PATTERNS).some(pattern => pattern.test(message)) ||
                   message.includes('OAuth') ||
                   message.includes('provider') ||
                   message.includes('social');
        }

        // Handle social authentication errors
        handleSocialAuthError(error) {
            this.errorCount++;
            this.lastError = error;
            
            console.log('🔧 Handling social auth error:', error);
            
            const message = error.message || error.msg || 'Social authentication error';
            
            // Determine error type and response
            if (ERROR_PATTERNS.providerNotEnabled.test(message)) {
                this.handleProviderNotEnabled();
            } else if (ERROR_PATTERNS.redirectMismatch.test(message)) {
                this.handleRedirectMismatch();
            } else if (ERROR_PATTERNS.invalidCredentials.test(message)) {
                this.handleInvalidCredentials();
            } else {
                this.handleGenericError(message);
            }

            // Activate fallback mode
            if (!this.fallbackActive) {
                this.activateFallbackMode();
            }
        }

        // Handle provider not enabled error
        handleProviderNotEnabled() {
            console.log('🔧 Handling provider not enabled error...');
            
            this.showUserMessage(
                'Social login is not configured yet. Please use email/password registration.',
                'info'
            );

            if (CONFIG.showConfigHelp) {
                setTimeout(() => {
                    this.showConfigurationHelp();
                }, 3000);
            }
        }

        // Handle redirect URI mismatch
        handleRedirectMismatch() {
            console.log('🔧 Handling redirect URI mismatch...');
            
            this.showUserMessage(
                'Social login configuration needs to be updated. Please use email/password login.',
                'warning'
            );
        }

        // Handle invalid credentials
        handleInvalidCredentials() {
            console.log('🔧 Handling invalid credentials...');
            
            this.showUserMessage(
                'Social login credentials are invalid. Please use email/password login.',
                'error'
            );
        }

        // Handle generic error
        handleGenericError(message) {
            console.log('🔧 Handling generic social auth error...');
            
            this.showUserMessage(
                'Social login is temporarily unavailable. Please use email/password login.',
                'warning'
            );
        }

        // Activate fallback mode
        activateFallbackMode() {
            if (this.fallbackActive) return;
            
            console.log('🔄 Activating social auth fallback mode...');
            this.fallbackActive = true;

            // Disable social auth buttons
            this.disableSocialButtons();
            
            // Show fallback options
            this.showFallbackOptions();
        }

        // Disable social authentication buttons
        disableSocialButtons() {
            const socialButtons = document.querySelectorAll('[onclick*="signInWithGoogle"], [onclick*="signInWithApple"]');
            
            socialButtons.forEach(button => {
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.title = 'Social login not available - use email/password instead';
                
                // Override click handler
                const originalOnClick = button.onclick;
                button.onclick = (e) => {
                    e.preventDefault();
                    this.showUserMessage(
                        'Social login is not configured. Please use email/password registration below.',
                        'info'
                    );
                    
                    // Show registration modal after delay
                    setTimeout(() => {
                        if (typeof authManager !== 'undefined') {
                            authManager.showRegisterModal();
                        }
                    }, 2000);
                };
            });

            console.log(`🔧 Disabled ${socialButtons.length} social auth buttons`);
        }

        // Show fallback options
        showFallbackOptions() {
            // Highlight email/password options
            const emailInputs = document.querySelectorAll('input[type="email"]');
            emailInputs.forEach(input => {
                input.style.borderColor = '#10b981';
                input.style.boxShadow = '0 0 0 1px #10b981';
            });

            // Show helpful message
            this.showUserMessage(
                'Email/password registration is available and fully functional!',
                'success'
            );
        }

        // Show configuration help
        showConfigurationHelp() {
            console.log('📖 Showing configuration help...');
            
            this.showUserMessage(
                'To enable social login, configure Google/Apple OAuth in your Supabase dashboard.',
                'info'
            );

            // Create help link
            const helpLink = document.createElement('div');
            helpLink.innerHTML = `
                <div style="position: fixed; top: 20px; right: 20px; background: #1f2937; color: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3); z-index: 10000; max-width: 300px;">
                    <h4 style="margin: 0 0 10px 0; color: #10b981;">🔧 Social Login Setup</h4>
                    <p style="margin: 0 0 10px 0; font-size: 14px;">Social login requires Supabase configuration.</p>
                    <button onclick="window.open('SUPABASE_SOCIAL_AUTH_SETUP.md', '_blank')" 
                            style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">
                        Setup Guide
                    </button>
                    <button onclick="this.parentElement.remove()" 
                            style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        Close
                    </button>
                </div>
            `;
            
            document.body.appendChild(helpLink);
            
            // Auto-remove after 30 seconds
            setTimeout(() => {
                if (helpLink.parentElement) {
                    helpLink.remove();
                }
            }, 30000);
        }

        // Quick provider test
        async quickProviderTest() {
            console.log('🧪 Running quick provider test...');
            
            if (typeof supabaseIntegration === 'undefined' || !supabaseIntegration.isConnected()) {
                console.log('⚠️ Supabase not available - activating fallback mode');
                this.activateFallbackMode();
                return;
            }

            try {
                // Test Google provider
                const googleTest = await this.testProvider('google');
                const appleTest = await this.testProvider('apple');
                
                console.log('📊 Provider test results:', { google: googleTest, apple: appleTest });
                
                if (!googleTest.available && !appleTest.available) {
                    console.log('⚠️ No social providers available - activating fallback mode');
                    this.activateFallbackMode();
                }
                
            } catch (error) {
                console.warn('⚠️ Provider test failed:', error);
                this.activateFallbackMode();
            }
        }

        // Test individual provider
        async testProvider(provider) {
            try {
                if (typeof supabaseIntegration !== 'undefined' && supabaseIntegration.testProvider) {
                    return await supabaseIntegration.testProvider(provider);
                } else {
                    // Fallback test
                    const { data, error } = await supabaseIntegration.supabase.auth.signInWithOAuth({
                        provider: provider,
                        options: {
                            redirectTo: window.location.origin,
                            skipBrowserRedirect: true
                        }
                    });

                    return { available: !error, error: error?.message };
                }
            } catch (error) {
                return { available: false, error: error.message };
            }
        }

        // Setup UI enhancements
        setupUIEnhancements() {
            // Add CSS for better error handling
            const style = document.createElement('style');
            style.textContent = `
                .social-auth-disabled {
                    opacity: 0.5 !important;
                    cursor: not-allowed !important;
                    pointer-events: none !important;
                }
                
                .social-auth-fallback-highlight {
                    border-color: #10b981 !important;
                    box-shadow: 0 0 0 1px #10b981 !important;
                }
                
                .social-auth-error-message {
                    background: #fef3c7;
                    color: #92400e;
                    padding: 12px;
                    border-radius: 8px;
                    margin: 10px 0;
                    border: 1px solid #fbbf24;
                }
            `;
            document.head.appendChild(style);
        }

        // Show user message
        showUserMessage(message, type = 'info') {
            console.log(`📢 User message (${type}): ${message}`);
            
            // Use Components.createNotification if available
            if (typeof Components !== 'undefined' && Components.createNotification) {
                Components.createNotification(message, type);
            } else {
                // Fallback notification
                const notification = document.createElement('div');
                notification.className = `social-auth-error-message`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 10000;
                    max-width: 400px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    background: ${type === 'error' ? '#fee2e2' : type === 'warning' ? '#fef3c7' : type === 'success' ? '#d1fae5' : '#dbeafe'};
                    color: ${type === 'error' ? '#991b1b' : type === 'warning' ? '#92400e' : type === 'success' ? '#065f46' : '#1e40af'};
                    border: 1px solid ${type === 'error' ? '#fca5a5' : type === 'warning' ? '#fbbf24' : type === 'success' ? '#6ee7b7' : '#93c5fd'};
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }
        }

        // Get status
        getStatus() {
            return {
                errorCount: this.errorCount,
                lastError: this.lastError,
                fallbackActive: this.fallbackActive,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Create and initialize quick fix
    const quickFix = new SocialAuthQuickFix();

    // Export for debugging
    window.socialAuthQuickFix = quickFix;
    window.getSocialAuthQuickFixStatus = () => quickFix.getStatus();

    console.log('✅ Social Auth Quick Fix loaded successfully');

})();
