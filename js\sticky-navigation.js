// Sticky Navigation Enhancement for Obu Marketplace
class StickyNavigation {
    constructor() {
        this.nav = null;
        this.isSticky = false;
        this.lastScrollY = 0;
        this.ticking = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.nav = document.querySelector('.nav-glass');
        if (!this.nav) {
            console.warn('Navigation element not found');
            return;
        }

        // Setup scroll listener
        this.setupScrollListener();
        
        // Setup intersection observer for better performance
        this.setupIntersectionObserver();

        console.log('✅ Sticky navigation initialized');
    }

    setupScrollListener() {
        const updateNavigation = () => {
            const currentScrollY = window.scrollY;
            const scrollingDown = currentScrollY > this.lastScrollY;
            const scrollThreshold = 100;

            // Add scrolled class when scrolling down past threshold
            if (currentScrollY > scrollThreshold && !this.isSticky) {
                this.makeSticky();
            } else if (currentScrollY <= scrollThreshold && this.isSticky) {
                this.removeSticky();
            }

            // Hide/show navigation based on scroll direction (optional)
            if (currentScrollY > 200) {
                if (scrollingDown && currentScrollY > this.lastScrollY + 10) {
                    this.hideNavigation();
                } else if (!scrollingDown && currentScrollY < this.lastScrollY - 10) {
                    this.showNavigation();
                }
            } else {
                this.showNavigation();
            }

            this.lastScrollY = currentScrollY;
            this.ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!this.ticking) {
                requestAnimationFrame(updateNavigation);
                this.ticking = true;
            }
        }, { passive: true });
    }

    setupIntersectionObserver() {
        // Create a sentinel element at the top of the page
        const sentinel = document.createElement('div');
        sentinel.style.height = '1px';
        sentinel.style.position = 'absolute';
        sentinel.style.top = '0';
        sentinel.style.left = '0';
        sentinel.style.width = '100%';
        sentinel.style.pointerEvents = 'none';
        document.body.insertBefore(sentinel, document.body.firstChild);

        // Observe when the sentinel goes out of view
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (!entry.isIntersecting && !this.isSticky) {
                        this.makeSticky();
                    } else if (entry.isIntersecting && this.isSticky) {
                        this.removeSticky();
                    }
                });
            },
            {
                threshold: 0,
                rootMargin: '-100px 0px 0px 0px'
            }
        );

        observer.observe(sentinel);
    }

    makeSticky() {
        if (!this.nav || this.isSticky) return;

        this.isSticky = true;
        this.nav.classList.add('scrolled');
        
        // Add smooth transition
        this.nav.style.transform = 'translateY(-100%)';
        
        // Trigger reflow and animate in
        requestAnimationFrame(() => {
            this.nav.style.transform = 'translateY(0)';
        });

        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('navigationSticky', {
            detail: { isSticky: true }
        }));

        console.log('📌 Navigation is now sticky');
    }

    removeSticky() {
        if (!this.nav || !this.isSticky) return;

        this.isSticky = false;
        this.nav.classList.remove('scrolled');
        this.nav.style.transform = '';

        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('navigationSticky', {
            detail: { isSticky: false }
        }));

        console.log('📌 Navigation sticky removed');
    }

    hideNavigation() {
        if (!this.nav) return;
        
        this.nav.style.transform = 'translateY(-100%)';
        this.nav.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    }

    showNavigation() {
        if (!this.nav) return;
        
        this.nav.style.transform = 'translateY(0)';
        this.nav.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    }

    // Force show navigation (useful for mobile menu)
    forceShow() {
        this.showNavigation();
    }

    // Get current sticky state
    getState() {
        return {
            isSticky: this.isSticky,
            scrollY: window.scrollY,
            navHeight: this.nav ? this.nav.offsetHeight : 0
        };
    }
}

// Create global instance
const stickyNavigation = new StickyNavigation();

// Export for global access
window.stickyNavigation = stickyNavigation;

// Listen for mobile menu events to ensure navigation stays visible
document.addEventListener('mobileMenuOpen', () => {
    stickyNavigation.forceShow();
});

// Handle window resize
window.addEventListener('resize', () => {
    // Reset navigation state on resize
    if (stickyNavigation.nav) {
        stickyNavigation.nav.style.transform = '';
    }
});

// Add smooth scroll behavior for anchor links
document.addEventListener('click', (e) => {
    const link = e.target.closest('a[href^="#"]');
    if (link) {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            const navHeight = stickyNavigation.nav ? stickyNavigation.nav.offsetHeight : 0;
            const targetPosition = targetElement.offsetTop - navHeight - 20;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }
});
