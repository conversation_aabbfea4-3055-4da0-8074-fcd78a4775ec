// LocalStorage Management System
class StorageManager {
    constructor() {
        this.initializeStorage();
    }

    // Initialize storage with default data if empty
    initializeStorage() {
        // Initialize categories
        if (!this.getCategories().length) {
            this.setCategories(CONFIG.DEFAULT_CATEGORIES);
        }

        // Initialize products
        if (!this.getProducts().length) {
            this.setProducts(CONFIG.SAMPLE_PRODUCTS);
            this.updateCategoryCounts();
        }

        // Initialize other storage if needed
        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.USERS)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.USERS, JSON.stringify([]));
        }

        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.ORDERS)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.ORDERS, JSON.stringify([]));
        }

        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.CART)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.CART, JSON.stringify([]));
        }
    }

    // Generic storage methods
    setItem(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }

    getItem(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    }

    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }

    // User Management
    getUsers() {
        return this.getItem(CONFIG.STORAGE_KEYS.USERS, []);
    }

    setUsers(users) {
        return this.setItem(CONFIG.STORAGE_KEYS.USERS, users);
    }

    addUser(user) {
        const users = this.getUsers();
        user.id = this.generateId();
        user.createdAt = new Date().toISOString();
        users.push(user);
        this.setUsers(users);
        return user;
    }

    getUserById(id) {
        const users = this.getUsers();
        return users.find(user => user.id === id);
    }

    getUserByEmail(email) {
        const users = this.getUsers();
        return users.find(user => user.email === email);
    }

    updateUser(id, updates) {
        const users = this.getUsers();
        const index = users.findIndex(user => user.id === id);
        if (index !== -1) {
            users[index] = { ...users[index], ...updates, updatedAt: new Date().toISOString() };
            this.setUsers(users);
            return users[index];
        }
        return null;
    }

    // Current User Management
    getCurrentUser() {
        return this.getItem(CONFIG.STORAGE_KEYS.CURRENT_USER);
    }

    setCurrentUser(user) {
        return this.setItem(CONFIG.STORAGE_KEYS.CURRENT_USER, user);
    }

    clearCurrentUser() {
        return this.removeItem(CONFIG.STORAGE_KEYS.CURRENT_USER);
    }

    // Product Management
    getProducts() {
        return this.getItem(CONFIG.STORAGE_KEYS.PRODUCTS, []);
    }

    setProducts(products) {
        return this.setItem(CONFIG.STORAGE_KEYS.PRODUCTS, products);
    }

    addProduct(product) {
        const products = this.getProducts();
        product.id = this.generateId();
        product.createdAt = new Date().toISOString();
        product.rating = product.rating || 0;
        product.reviews = product.reviews || 0;
        product.stock = product.stock || 1;
        product.featured = product.featured || false;
        products.push(product);
        this.setProducts(products);
        this.updateCategoryCounts();
        return product;
    }

    getProductById(id) {
        const products = this.getProducts();
        return products.find(product => product.id === id);
    }

    getProductsByCategory(categoryName) {
        const products = this.getProducts();
        return products.filter(product => product.category === categoryName);
    }

    getFeaturedProducts() {
        const products = this.getProducts();
        return products.filter(product => product.featured);
    }

    searchProducts(query) {
        const products = this.getProducts();
        const searchTerm = query.toLowerCase();
        return products.filter(product => 
            product.title.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
        );
    }

    updateProduct(id, updates) {
        const products = this.getProducts();
        const index = products.findIndex(product => product.id === id);
        if (index !== -1) {
            products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };
            this.setProducts(products);
            this.updateCategoryCounts();
            return products[index];
        }
        return null;
    }

    deleteProduct(id) {
        const products = this.getProducts();
        const filteredProducts = products.filter(product => product.id !== id);
        this.setProducts(filteredProducts);
        this.updateCategoryCounts();
        return true;
    }

    // Category Management
    getCategories() {
        return this.getItem(CONFIG.STORAGE_KEYS.CATEGORIES, []);
    }

    setCategories(categories) {
        return this.setItem(CONFIG.STORAGE_KEYS.CATEGORIES, categories);
    }

    updateCategoryCounts() {
        const categories = this.getCategories();
        const products = this.getProducts();
        
        categories.forEach(category => {
            category.count = products.filter(product => product.category === category.name).length;
        });
        
        this.setCategories(categories);
    }

    // Cart Management
    getCart() {
        return this.getItem(CONFIG.STORAGE_KEYS.CART, []);
    }

    setCart(cart) {
        return this.setItem(CONFIG.STORAGE_KEYS.CART, cart);
    }

    addToCart(productId, quantity = 1) {
        const cart = this.getCart();
        const existingItem = cart.find(item => item.productId === productId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            cart.push({
                id: this.generateId(),
                productId,
                quantity,
                addedAt: new Date().toISOString()
            });
        }
        
        this.setCart(cart);
        return cart;
    }

    removeFromCart(productId) {
        const cart = this.getCart();
        const filteredCart = cart.filter(item => item.productId !== productId);
        this.setCart(filteredCart);
        return filteredCart;
    }

    clearCart() {
        this.setCart([]);
        return [];
    }

    // Order Management
    getOrders() {
        return this.getItem(CONFIG.STORAGE_KEYS.ORDERS, []);
    }

    setOrders(orders) {
        return this.setItem(CONFIG.STORAGE_KEYS.ORDERS, orders);
    }

    addOrder(order) {
        const orders = this.getOrders();
        order.id = this.generateId();
        order.createdAt = new Date().toISOString();
        order.status = order.status || 'pending';
        orders.push(order);
        this.setOrders(orders);
        return order;
    }

    getOrdersByUser(userId) {
        const orders = this.getOrders();
        return orders.filter(order => order.buyerId === userId || order.sellerId === userId);
    }

    updateOrder(id, updates) {
        const orders = this.getOrders();
        const index = orders.findIndex(order => order.id === id);
        if (index !== -1) {
            orders[index] = { ...orders[index], ...updates, updatedAt: new Date().toISOString() };
            this.setOrders(orders);
            return orders[index];
        }
        return null;
    }

    // Utility Methods
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    exportData() {
        return {
            users: this.getUsers(),
            products: this.getProducts(),
            categories: this.getCategories(),
            orders: this.getOrders(),
            exportedAt: new Date().toISOString()
        };
    }

    importData(data) {
        try {
            if (data.users) this.setUsers(data.users);
            if (data.products) this.setProducts(data.products);
            if (data.categories) this.setCategories(data.categories);
            if (data.orders) this.setOrders(data.orders);
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    clearAllData() {
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeStorage();
    }
}

// Create global instance
const storage = new StorageManager();
