// LocalStorage Management System
class StorageManager {
    constructor() {
        this.initializeStorage();
    }

    // Initialize storage with default data if empty
    initializeStorage() {
        // Initialize categories
        if (!this.getCategories().length) {
            this.setCategories(CONFIG.DEFAULT_CATEGORIES);
        }

        // Initialize products
        if (!this.getProducts().length) {
            this.setProducts(CONFIG.SAMPLE_PRODUCTS);
            this.updateCategoryCounts();
        }

        // Initialize other storage if needed
        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.USERS)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.USERS, JSON.stringify([]));
        }

        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.PURCHASES)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.PURCHASES, JSON.stringify([]));
        }

        if (!localStorage.getItem(CONFIG.STORAGE_KEYS.CART)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.CART, JSON.stringify([]));
        }
    }

    // Generic storage methods
    setItem(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }

    getItem(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    }

    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }

    // User Management
    getUsers() {
        return this.getItem(CONFIG.STORAGE_KEYS.USERS, []);
    }

    setUsers(users) {
        return this.setItem(CONFIG.STORAGE_KEYS.USERS, users);
    }

    addUser(user) {
        const users = this.getUsers();
        user.id = this.generateId();
        user.accountType = 'buyer'; // Force buyer-only system
        user.createdAt = new Date().toISOString();
        user.emailVerified = false;
        user.lastLogin = null;
        // Hash password (simple demo - in production use proper hashing)
        user.passwordHash = this.simpleHash(user.password);
        delete user.password; // Remove plain text password
        users.push(user);
        this.setUsers(users);
        return user;
    }

    // Simple password hashing for demo (use bcrypt in production)
    simpleHash(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Verify password
    verifyPassword(password, hash) {
        return this.simpleHash(password) === hash;
    }

    getUserById(id) {
        const users = this.getUsers();
        return users.find(user => user.id === id);
    }

    getUserByEmail(email) {
        const users = this.getUsers();
        return users.find(user => user.email === email);
    }

    updateUser(id, updates) {
        const users = this.getUsers();
        const index = users.findIndex(user => user.id === id);
        if (index !== -1) {
            users[index] = { ...users[index], ...updates, updatedAt: new Date().toISOString() };
            this.setUsers(users);
            return users[index];
        }
        return null;
    }

    // Current User Management with Session
    getCurrentUser() {
        const session = this.getUserSession();
        if (session && session.expiresAt > Date.now()) {
            return this.getItem(CONFIG.STORAGE_KEYS.CURRENT_USER);
        } else {
            this.clearCurrentUser();
            return null;
        }
    }

    setCurrentUser(user) {
        // Update last login
        this.updateUser(user.id, { lastLogin: new Date().toISOString() });

        // Set session
        this.setUserSession({
            userId: user.id,
            createdAt: Date.now(),
            expiresAt: Date.now() + CONFIG.AUTH.SESSION_DURATION
        });

        return this.setItem(CONFIG.STORAGE_KEYS.CURRENT_USER, user);
    }

    clearCurrentUser() {
        this.removeItem(CONFIG.STORAGE_KEYS.USER_SESSION);
        return this.removeItem(CONFIG.STORAGE_KEYS.CURRENT_USER);
    }

    // Session Management
    getUserSession() {
        return this.getItem(CONFIG.STORAGE_KEYS.USER_SESSION);
    }

    setUserSession(session) {
        return this.setItem(CONFIG.STORAGE_KEYS.USER_SESSION, session);
    }

    isSessionValid() {
        const session = this.getUserSession();
        return session && session.expiresAt > Date.now();
    }

    // Product Management
    getProducts() {
        return this.getItem(CONFIG.STORAGE_KEYS.PRODUCTS, []);
    }

    setProducts(products) {
        return this.setItem(CONFIG.STORAGE_KEYS.PRODUCTS, products);
    }

    addProduct(product) {
        const products = this.getProducts();
        product.id = this.generateId();
        product.createdAt = new Date().toISOString();
        product.rating = product.rating || 0;
        product.reviews = product.reviews || 0;
        product.stock = product.stock || 1;
        product.featured = product.featured || false;
        products.push(product);
        this.setProducts(products);
        this.updateCategoryCounts();
        return product;
    }

    getProductById(id) {
        const products = this.getProducts();
        return products.find(product => product.id === id);
    }

    getProductsByCategory(categoryName) {
        const products = this.getProducts();
        return products.filter(product => product.category === categoryName);
    }

    getFeaturedProducts() {
        const products = this.getProducts();
        return products.filter(product => product.featured);
    }

    searchProducts(query) {
        const products = this.getProducts();
        const searchTerm = query.toLowerCase();
        return products.filter(product => 
            product.title.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
        );
    }

    updateProduct(id, updates) {
        const products = this.getProducts();
        const index = products.findIndex(product => product.id === id);
        if (index !== -1) {
            products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };
            this.setProducts(products);
            this.updateCategoryCounts();
            return products[index];
        }
        return null;
    }

    deleteProduct(id) {
        const products = this.getProducts();
        const filteredProducts = products.filter(product => product.id !== id);
        this.setProducts(filteredProducts);
        this.updateCategoryCounts();
        return true;
    }

    // Category Management
    getCategories() {
        return this.getItem(CONFIG.STORAGE_KEYS.CATEGORIES, []);
    }

    setCategories(categories) {
        return this.setItem(CONFIG.STORAGE_KEYS.CATEGORIES, categories);
    }

    updateCategoryCounts() {
        const categories = this.getCategories();
        const products = this.getProducts();
        
        categories.forEach(category => {
            category.count = products.filter(product => product.category === category.name).length;
        });
        
        this.setCategories(categories);
    }

    // Cart Management
    getCart() {
        return this.getItem(CONFIG.STORAGE_KEYS.CART, []);
    }

    setCart(cart) {
        return this.setItem(CONFIG.STORAGE_KEYS.CART, cart);
    }

    addToCart(productId, quantity = 1) {
        const cart = this.getCart();
        const existingItem = cart.find(item => item.productId === productId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            cart.push({
                id: this.generateId(),
                productId,
                quantity,
                addedAt: new Date().toISOString()
            });
        }
        
        this.setCart(cart);
        return cart;
    }

    removeFromCart(productId) {
        const cart = this.getCart();
        const filteredCart = cart.filter(item => item.productId !== productId);
        this.setCart(filteredCart);
        return filteredCart;
    }

    clearCart() {
        this.setCart([]);
        return [];
    }

    // Purchase Management (Buyer-only system)
    getPurchases() {
        return this.getItem(CONFIG.STORAGE_KEYS.PURCHASES, []);
    }

    setPurchases(purchases) {
        return this.setItem(CONFIG.STORAGE_KEYS.PURCHASES, purchases);
    }

    addPurchase(purchase) {
        const purchases = this.getPurchases();
        purchase.id = this.generateId();
        purchase.purchaseDate = new Date().toISOString();
        purchase.status = purchase.status || 'completed';
        purchase.downloadAttempts = 0;
        purchase.maxDownloads = 3; // Allow 3 downloads per purchase
        purchases.push(purchase);
        this.setPurchases(purchases);
        return purchase;
    }

    getPurchasesByUser(userId) {
        const purchases = this.getPurchases();
        return purchases.filter(purchase => purchase.buyerId === userId);
    }

    updatePurchase(id, updates) {
        const purchases = this.getPurchases();
        const index = purchases.findIndex(purchase => purchase.id === id);
        if (index !== -1) {
            purchases[index] = { ...purchases[index], ...updates, updatedAt: new Date().toISOString() };
            this.setPurchases(purchases);
            return purchases[index];
        }
        return null;
    }

    // Record download attempt
    recordDownload(purchaseId) {
        const purchase = this.getPurchases().find(p => p.id === purchaseId);
        if (purchase && purchase.downloadAttempts < purchase.maxDownloads) {
            this.updatePurchase(purchaseId, {
                downloadAttempts: purchase.downloadAttempts + 1,
                lastDownload: new Date().toISOString()
            });
            return true;
        }
        return false;
    }

    // Utility Methods
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    exportData() {
        return {
            users: this.getUsers(),
            products: this.getProducts(),
            categories: this.getCategories(),
            purchases: this.getPurchases(),
            exportedAt: new Date().toISOString()
        };
    }

    importData(data) {
        try {
            if (data.users) this.setUsers(data.users);
            if (data.products) this.setProducts(data.products);
            if (data.categories) this.setCategories(data.categories);
            if (data.purchases) this.setPurchases(data.purchases);
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    clearAllData() {
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeStorage();
    }
}

// Create global instance
const storage = new StorageManager();
