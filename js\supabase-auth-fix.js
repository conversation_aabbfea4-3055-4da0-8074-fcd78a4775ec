// Supabase Authentication Provider Fix
class SupabaseAuthFix {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔧 Supabase Auth Fix initializing...');
        
        // Check if Supabase is available
        if (typeof supabaseIntegration === 'undefined') {
            console.warn('⚠️ Supabase integration not available');
            return;
        }

        // Test Supabase connection
        this.testSupabaseConnection();
        
        // Setup fallback authentication
        this.setupFallbackAuth();
    }

    async testSupabaseConnection() {
        try {
            if (!supabaseIntegration.isConnected()) {
                console.warn('⚠️ Supabase not connected, using localStorage fallback');
                return;
            }

            // Test basic connection
            const { data, error } = await supabaseIntegration.supabase.auth.getSession();
            
            if (error) {
                console.error('❌ Supabase auth error:', error);
                this.handleAuthError(error);
            } else {
                console.log('✅ Supabase auth connection successful');
            }

        } catch (error) {
            console.error('❌ Supabase connection test failed:', error);
            this.handleAuthError(error);
        }
    }

    handleAuthError(error) {
        // Handle specific authentication errors
        if (error.message && error.message.includes('provider is not enabled')) {
            console.warn('⚠️ Social auth providers not configured in Supabase');
            this.showProviderConfigurationHelp();
        }
        
        // Always fall back to localStorage authentication
        this.enableLocalStorageFallback();
    }

    showProviderConfigurationHelp() {
        console.log(`
🔧 SUPABASE SOCIAL AUTH CONFIGURATION HELP:

To enable Google and Apple Sign-In:

1. Go to your Supabase Dashboard: https://supabase.com/dashboard
2. Navigate to Authentication > Providers
3. Enable Google OAuth:
   - Toggle "Enable sign in with Google"
   - Add your Google OAuth Client ID and Secret
   - Set redirect URL: https://your-project-id.supabase.co/auth/v1/callback

4. Enable Apple Sign-In:
   - Toggle "Enable sign in with Apple"
   - Add your Apple Service ID, Team ID, Key ID, and Private Key
   - Set redirect URL: https://your-project-id.supabase.co/auth/v1/callback

5. Update your site URL in Authentication > Settings:
   - Site URL: http://localhost:8000 (for development)
   - Additional Redirect URLs: Add your production domain

For now, the system will use localStorage authentication as fallback.
        `);
    }

    enableLocalStorageFallback() {
        console.log('🔄 Enabling localStorage authentication fallback');
        
        // Ensure AuthManager uses localStorage methods
        if (typeof authManager !== 'undefined') {
            // Override social auth methods to show helpful messages
            authManager.signInWithGoogle = () => {
                Components.createNotification(
                    'Google Sign-In requires Supabase configuration. Please use email/password login.',
                    'info'
                );
                console.log('💡 To enable Google Sign-In, configure OAuth in Supabase Dashboard');
            };

            authManager.signInWithApple = () => {
                Components.createNotification(
                    'Apple Sign-In requires Supabase configuration. Please use email/password login.',
                    'info'
                );
                console.log('💡 To enable Apple Sign-In, configure OAuth in Supabase Dashboard');
            };
        }
    }

    // Test social authentication availability
    async testSocialAuth() {
        console.log('🧪 Testing social authentication providers...');
        
        if (!supabaseIntegration.isConnected()) {
            console.log('ℹ️ Supabase not connected - social auth not available');
            return {
                google: false,
                apple: false,
                reason: 'Supabase not connected'
            };
        }

        try {
            // Test Google provider
            const googleTest = await this.testProvider('google');
            const appleTest = await this.testProvider('apple');

            return {
                google: googleTest,
                apple: appleTest,
                reason: 'Provider configuration'
            };

        } catch (error) {
            console.error('❌ Social auth test failed:', error);
            return {
                google: false,
                apple: false,
                reason: error.message
            };
        }
    }

    async testProvider(provider) {
        try {
            // This will fail if provider is not configured, but won't actually sign in
            const { data, error } = await supabaseIntegration.supabase.auth.signInWithOAuth({
                provider: provider,
                options: {
                    redirectTo: window.location.origin,
                    skipBrowserRedirect: true // Don't actually redirect
                }
            });

            return !error;
        } catch (error) {
            console.warn(`⚠️ ${provider} provider not configured:`, error.message);
            return false;
        }
    }

    // Show authentication status
    showAuthStatus() {
        console.log('📊 Authentication System Status:');
        console.log('='.repeat(40));
        
        const status = {
            supabaseConnected: supabaseIntegration?.isConnected() || false,
            localStorageAvailable: typeof Storage !== 'undefined',
            authManagerLoaded: typeof authManager !== 'undefined',
            currentUser: authManager?.currentUser || null
        };

        Object.entries(status).forEach(([key, value]) => {
            const icon = value ? '✅' : '❌';
            console.log(`${icon} ${key}: ${value}`);
        });

        if (status.currentUser) {
            console.log(`👤 Current User: ${status.currentUser.name} (${status.currentUser.email})`);
        }

        return status;
    }

    // Fix authentication modal display
    fixModalDisplay() {
        console.log('🔧 Fixing modal display issues...');
        
        // Ensure modal CSS is loaded
        if (!document.getElementById('modal-fix-styles')) {
            const style = document.createElement('style');
            style.id = 'modal-fix-styles';
            style.textContent = `
                .modal-overlay {
                    position: fixed !important;
                    inset: 0 !important;
                    background: rgba(0, 0, 0, 0.8) !important;
                    backdrop-filter: blur(4px) !important;
                    z-index: 9999 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    padding: 1rem !important;
                }

                .modal-card {
                    background: rgba(26, 26, 46, 0.95) !important;
                    backdrop-filter: blur(25px) !important;
                    border-radius: 1rem !important;
                    width: 100% !important;
                    max-width: 28rem !important;
                    max-height: 90vh !important;
                    overflow-y: auto !important;
                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6) !important;
                }

                .hidden {
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        }

        // Test modal creation
        this.testModalCreation();
    }

    testModalCreation() {
        console.log('🧪 Testing modal creation...');
        
        try {
            if (typeof authManager === 'undefined') {
                console.error('❌ AuthManager not available');
                return false;
            }

            // Test login modal
            authManager.showLoginModal();
            
            setTimeout(() => {
                const modal = document.getElementById('loginModal');
                if (modal) {
                    console.log('✅ Login modal created successfully');
                    authManager.hideModal('loginModal');
                } else {
                    console.error('❌ Login modal not created');
                }
            }, 500);

            return true;

        } catch (error) {
            console.error('❌ Modal creation test failed:', error);
            return false;
        }
    }

    // Complete system fix
    async runCompleteFix() {
        console.log('🔧 Running complete authentication system fix...');
        console.log('='.repeat(50));

        // 1. Test Supabase connection
        await this.testSupabaseConnection();

        // 2. Fix modal display
        this.fixModalDisplay();

        // 3. Test social auth
        const socialAuthStatus = await this.testSocialAuth();
        console.log('📱 Social Auth Status:', socialAuthStatus);

        // 4. Show system status
        const systemStatus = this.showAuthStatus();

        // 5. Create test accounts if needed
        if (typeof window.createTestAccounts === 'function') {
            await window.createTestAccounts();
        }

        console.log('='.repeat(50));
        console.log('✅ Authentication system fix complete');

        return {
            supabase: supabaseIntegration?.isConnected() || false,
            socialAuth: socialAuthStatus,
            system: systemStatus,
            modals: true
        };
    }
}

// Create global instance
const supabaseAuthFix = new SupabaseAuthFix();

// Add global functions
window.supabaseAuthFix = supabaseAuthFix;
window.testSocialAuth = () => supabaseAuthFix.testSocialAuth();
window.showAuthStatus = () => supabaseAuthFix.showAuthStatus();
window.fixAuthSystem = () => supabaseAuthFix.runCompleteFix();

// Auto-run fix on load
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('🔧 Auto-running authentication system fix...');
        supabaseAuthFix.runCompleteFix();
    }, 2000);
});
