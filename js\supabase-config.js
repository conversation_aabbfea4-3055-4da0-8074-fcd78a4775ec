// Supabase Configuration and Database Manager
class SupabaseManager {
    constructor() {
        // Replace these with your actual Supabase project credentials
        this.supabaseUrl = 'YOUR_SUPABASE_URL'; // e.g., 'https://your-project.supabase.co'
        this.supabaseKey = 'YOUR_SUPABASE_ANON_KEY'; // Your anon/public key
        this.supabase = null;
        this.currentUser = null;
    }

    // Initialize Supabase client
    async init() {
        try {
            // Import Supabase client (you'll need to include the Supabase JS library)
            if (typeof supabase === 'undefined') {
                console.warn('Supabase client not loaded. Using localStorage fallback.');
                return false;
            }

            this.supabase = supabase.createClient(this.supabaseUrl, this.supabaseKey);
            
            // Check for existing session
            const { data: { session } } = await this.supabase.auth.getSession();
            if (session) {
                this.currentUser = session.user;
                await this.syncUserProfile();
            }

            // Listen for auth changes
            this.supabase.auth.onAuthStateChange(async (event, session) => {
                if (event === 'SIGNED_IN' && session) {
                    this.currentUser = session.user;
                    await this.syncUserProfile();
                } else if (event === 'SIGNED_OUT') {
                    this.currentUser = null;
                }
            });

            return true;
        } catch (error) {
            console.error('Failed to initialize Supabase:', error);
            return false;
        }
    }

    // User Authentication Methods
    async signUp(email, password, userData) {
        try {
            const { data, error } = await this.supabase.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        name: userData.name,
                        account_type: 'buyer'
                    }
                }
            });

            if (error) throw error;

            // Create user profile
            if (data.user) {
                await this.createUserProfile(data.user, userData);
            }

            return { success: true, user: data.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async signIn(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;

            this.currentUser = data.user;
            await this.syncUserProfile();

            return { success: true, user: data.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;

            this.currentUser = null;
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // User Profile Methods
    async createUserProfile(user, userData) {
        try {
            const { error } = await this.supabase
                .from('users')
                .insert({
                    id: user.id,
                    name: userData.name,
                    email: user.email,
                    account_type: 'buyer',
                    email_verified: user.email_confirmed_at ? true : false
                });

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error creating user profile:', error);
            return false;
        }
    }

    async syncUserProfile() {
        if (!this.currentUser) return null;

        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('id', this.currentUser.id)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error syncing user profile:', error);
            return null;
        }
    }

    // Product Methods
    async getProducts() {
        try {
            const { data, error } = await this.supabase
                .from('products')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching products:', error);
            return [];
        }
    }

    async getProductById(id) {
        try {
            const { data, error } = await this.supabase
                .from('products')
                .select('*')
                .eq('id', id)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error fetching product:', error);
            return null;
        }
    }

    async getFeaturedProducts() {
        try {
            const { data, error } = await this.supabase
                .from('products')
                .select('*')
                .eq('featured', true)
                .order('rating', { ascending: false });

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching featured products:', error);
            return [];
        }
    }

    // Category Methods
    async getCategories() {
        try {
            const { data, error } = await this.supabase
                .from('categories')
                .select('*')
                .order('name');

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching categories:', error);
            return [];
        }
    }

    // Purchase Methods
    async createPurchase(purchaseData) {
        try {
            const { data, error } = await this.supabase
                .from('purchases')
                .insert(purchaseData)
                .select()
                .single();

            if (error) throw error;

            // Update product stock
            await this.updateProductStock(purchaseData.product_id, -1);

            return data;
        } catch (error) {
            console.error('Error creating purchase:', error);
            return null;
        }
    }

    async getUserPurchases(userId) {
        try {
            const { data, error } = await this.supabase
                .from('purchases')
                .select('*')
                .eq('buyer_id', userId)
                .order('purchase_date', { ascending: false });

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching user purchases:', error);
            return [];
        }
    }

    async updateProductStock(productId, change) {
        try {
            // Get current stock
            const { data: product } = await this.supabase
                .from('products')
                .select('stock')
                .eq('id', productId)
                .single();

            if (product) {
                const newStock = Math.max(0, product.stock + change);
                const { error } = await this.supabase
                    .from('products')
                    .update({ stock: newStock })
                    .eq('id', productId);

                if (error) throw error;
            }
        } catch (error) {
            console.error('Error updating product stock:', error);
        }
    }

    // Review Methods
    async createReview(reviewData) {
        try {
            const { data, error } = await this.supabase
                .from('reviews')
                .insert(reviewData)
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error creating review:', error);
            return null;
        }
    }

    async getProductReviews(productId) {
        try {
            const { data, error } = await this.supabase
                .from('reviews')
                .select('*')
                .eq('product_id', productId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching reviews:', error);
            return [];
        }
    }

    async hasUserReviewedProduct(userId, productId) {
        try {
            const { data, error } = await this.supabase
                .from('reviews')
                .select('id')
                .eq('user_id', userId)
                .eq('product_id', productId)
                .single();

            return !error && data;
        } catch (error) {
            return false;
        }
    }

    async voteOnReview(reviewId, isHelpful) {
        try {
            const field = isHelpful ? 'helpful_votes' : 'unhelpful_votes';
            
            // Get current vote count
            const { data: review } = await this.supabase
                .from('reviews')
                .select(field)
                .eq('id', reviewId)
                .single();

            if (review) {
                const newCount = (review[field] || 0) + 1;
                const { data, error } = await this.supabase
                    .from('reviews')
                    .update({ [field]: newCount })
                    .eq('id', reviewId)
                    .select()
                    .single();

                if (error) throw error;
                return data;
            }
        } catch (error) {
            console.error('Error voting on review:', error);
            return null;
        }
    }

    // Cart Methods
    async getCartItems(userId) {
        try {
            const { data, error } = await this.supabase
                .from('cart_items')
                .select(`
                    *,
                    products (*)
                `)
                .eq('user_id', userId);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching cart items:', error);
            return [];
        }
    }

    async addToCart(userId, productId, quantity = 1) {
        try {
            const { data, error } = await this.supabase
                .from('cart_items')
                .upsert({
                    user_id: userId,
                    product_id: productId,
                    quantity: quantity
                })
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error adding to cart:', error);
            return null;
        }
    }

    async updateCartQuantity(userId, productId, quantity) {
        try {
            if (quantity <= 0) {
                return await this.removeFromCart(userId, productId);
            }

            const { data, error } = await this.supabase
                .from('cart_items')
                .update({ quantity })
                .eq('user_id', userId)
                .eq('product_id', productId)
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error updating cart quantity:', error);
            return null;
        }
    }

    async removeFromCart(userId, productId) {
        try {
            const { error } = await this.supabase
                .from('cart_items')
                .delete()
                .eq('user_id', userId)
                .eq('product_id', productId);

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error removing from cart:', error);
            return false;
        }
    }

    async clearCart(userId) {
        try {
            const { error } = await this.supabase
                .from('cart_items')
                .delete()
                .eq('user_id', userId);

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error clearing cart:', error);
            return false;
        }
    }

    // Utility Methods
    isConnected() {
        return this.supabase !== null;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Create global instance
const supabaseManager = new SupabaseManager();
