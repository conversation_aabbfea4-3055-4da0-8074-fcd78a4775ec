// Supabase Connection Test for Obu Marketplace
class SupabaseConnectionTest {
    constructor() {
        this.testResults = [];
    }

    // Run comprehensive connection tests
    async runConnectionTests() {
        console.log('🔗 TESTING SUPABASE CONNECTION');
        console.log('='.repeat(50));
        
        this.testResults = [];
        
        try {
            await this.testBasicConnection();
            await this.testDatabaseSchema();
            await this.testAuthentication();
            await this.testDataOperations();
            
            this.displayResults();
            return this.getConnectionStatus();
        } catch (error) {
            console.error('Connection test failed:', error);
            return { connected: false, error: error.message };
        }
    }

    // Test basic Supabase connection
    async testBasicConnection() {
        console.log('\n🔌 Testing Basic Connection...');
        
        try {
            // Check if Supabase client is loaded
            this.addResult('Supabase client loaded', typeof supabase !== 'undefined');
            
            // Check if integration is initialized
            this.addResult('Integration class exists', typeof supabaseIntegration !== 'undefined');
            
            // Initialize connection
            const initialized = await supabaseIntegration.init();
            this.addResult('Connection initialized', initialized);
            
            // Test connection
            const connected = supabaseIntegration.isConnected();
            this.addResult('Connection established', connected);
            
            if (connected) {
                // Test basic query
                const connectionTest = await supabaseIntegration.testConnection();
                this.addResult('Database query test', connectionTest);
            }
            
        } catch (error) {
            this.addResult('Basic connection error', false, error.message);
        }
    }

    // Test database schema
    async testDatabaseSchema() {
        console.log('\n🗄️ Testing Database Schema...');
        
        if (!supabaseIntegration.isConnected()) {
            this.addResult('Schema test skipped', false, 'No connection');
            return;
        }

        try {
            // Test categories table
            const { data: categories, error: catError } = await supabaseIntegration.supabase
                .from('categories')
                .select('count')
                .limit(1);
            
            this.addResult('Categories table accessible', !catError);
            
            // Test products table
            const { data: products, error: prodError } = await supabaseIntegration.supabase
                .from('products')
                .select('count')
                .limit(1);
            
            this.addResult('Products table accessible', !prodError);
            
            // Test users table
            const { data: users, error: userError } = await supabaseIntegration.supabase
                .from('users')
                .select('count')
                .limit(1);
            
            this.addResult('Users table accessible', !userError);
            
            // Test purchases table
            const { data: purchases, error: purchError } = await supabaseIntegration.supabase
                .from('purchases')
                .select('count')
                .limit(1);
            
            this.addResult('Purchases table accessible', !purchError);
            
            // Test reviews table
            const { data: reviews, error: reviewError } = await supabaseIntegration.supabase
                .from('reviews')
                .select('count')
                .limit(1);
            
            this.addResult('Reviews table accessible', !reviewError);
            
        } catch (error) {
            this.addResult('Schema test error', false, error.message);
        }
    }

    // Test authentication
    async testAuthentication() {
        console.log('\n🔐 Testing Authentication...');
        
        if (!supabaseIntegration.isConnected()) {
            this.addResult('Auth test skipped', false, 'No connection');
            return;
        }

        try {
            // Test getting current session
            const { data: session, error: sessionError } = await supabaseIntegration.supabase.auth.getSession();
            this.addResult('Session check works', !sessionError);
            
            // Test auth state
            const { data: { user }, error: userError } = await supabaseIntegration.supabase.auth.getUser();
            this.addResult('User check works', !userError);
            
            // Check if user is logged in
            const isLoggedIn = user !== null;
            this.addResult('Current auth state', true, isLoggedIn ? 'User logged in' : 'No user logged in');
            
        } catch (error) {
            this.addResult('Auth test error', false, error.message);
        }
    }

    // Test data operations
    async testDataOperations() {
        console.log('\n📊 Testing Data Operations...');
        
        if (!supabaseIntegration.isConnected()) {
            this.addResult('Data test skipped', false, 'No connection');
            return;
        }

        try {
            // Test reading categories
            const { data: categories, error: catError } = await supabaseIntegration.supabase
                .from('categories')
                .select('*')
                .limit(5);
            
            this.addResult('Read categories', !catError && categories && categories.length > 0);
            
            // Test reading products
            const { data: products, error: prodError } = await supabaseIntegration.supabase
                .from('products')
                .select('*')
                .limit(5);
            
            this.addResult('Read products', !prodError && products && products.length > 0);
            
            // Test data sync functions
            if (typeof supabaseIntegration.syncProducts === 'function') {
                const syncedProducts = await supabaseIntegration.syncProducts();
                this.addResult('Product sync function', Array.isArray(syncedProducts));
            }
            
        } catch (error) {
            this.addResult('Data operations error', false, error.message);
        }
    }

    // Helper method to add test results
    addResult(testName, passed, details = '') {
        const result = {
            name: testName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const icon = passed ? '✅' : '❌';
        const detailsText = details ? ` (${details})` : '';
        console.log(`${icon} ${testName}${detailsText}`);
    }

    // Display test results
    displayResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n' + '='.repeat(50));
        console.log('🎯 SUPABASE CONNECTION TEST RESULTS');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Success Rate: ${percentage}%`);
        console.log('='.repeat(50));
        
        if (percentage === 100) {
            console.log('🎉 PERFECT! Supabase is fully connected and working.');
        } else if (percentage >= 80) {
            console.log('✅ GOOD! Supabase connection is mostly working.');
        } else if (percentage >= 60) {
            console.log('⚠️ PARTIAL! Some Supabase features may not work.');
        } else {
            console.log('🚨 ISSUES! Supabase connection has problems.');
        }
        
        // Show failed tests
        const failed = this.testResults.filter(r => !r.passed);
        if (failed.length > 0) {
            console.log('\n❌ Failed Tests:');
            failed.forEach(test => {
                const details = test.details ? ` - ${test.details}` : '';
                console.log(`  • ${test.name}${details}`);
            });
        }
        
        console.log('\n' + '='.repeat(50));
    }

    // Get connection status
    getConnectionStatus() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const percentage = Math.round((passed / total) * 100);
        
        return {
            connected: percentage >= 80,
            percentage: percentage,
            totalTests: total,
            passedTests: passed,
            failedTests: total - passed,
            status: percentage >= 90 ? 'EXCELLENT' : 
                   percentage >= 80 ? 'GOOD' : 
                   percentage >= 60 ? 'PARTIAL' : 'POOR'
        };
    }

    // Quick connection check
    async quickCheck() {
        console.log('⚡ Quick Supabase Connection Check...');
        
        try {
            if (typeof supabaseIntegration === 'undefined') {
                console.log('❌ Supabase integration not loaded');
                return false;
            }
            
            await supabaseIntegration.init();
            
            if (!supabaseIntegration.isConnected()) {
                console.log('❌ Supabase connection failed');
                return false;
            }
            
            const testResult = await supabaseIntegration.testConnection();
            
            if (testResult) {
                console.log('✅ Supabase connection working!');
                return true;
            } else {
                console.log('❌ Supabase database query failed');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Connection error:', error.message);
            return false;
        }
    }
}

// Create global instance
const supabaseConnectionTest = new SupabaseConnectionTest();

// Add to window for console access
window.testSupabaseConnection = () => supabaseConnectionTest.runConnectionTests();
window.quickSupabaseCheck = () => supabaseConnectionTest.quickCheck();

// Auto-run quick check when script loads
document.addEventListener('DOMContentLoaded', async () => {
    // Wait a moment for other scripts to load
    setTimeout(async () => {
        console.log('🔍 Supabase Connection Test Script Loaded');
        console.log('Run window.testSupabaseConnection() for full test');
        console.log('Run window.quickSupabaseCheck() for quick check');
        
        // Auto quick check
        await supabaseConnectionTest.quickCheck();
    }, 1000);
});
