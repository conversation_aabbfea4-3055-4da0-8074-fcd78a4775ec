// Complete Supabase Integration with Social Authentication
class SupabaseIntegration {
    constructor() {
        this.supabaseUrl = 'https://rvdjfqlydmnlblpbjjlc.supabase.co';
        this.supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ2ZGpmcWx5ZG1ubGJscGJqamxjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3OTUwODQsImV4cCI6MjA2NTM3MTA4NH0.qc7ZMEhW1sNSgkuAKZSYoWJArEz4Z-P-STv-08awrgs';
        this.supabase = null;
        this.isInitialized = false;
    }

    // Initialize Supabase with fallback to localStorage
    async init() {
        try {
            if (typeof supabase === 'undefined') {
                console.warn('Supabase client not loaded. Using localStorage fallback.');
                return false;
            }

            this.supabase = supabase.createClient(this.supabaseUrl, this.supabaseKey);
            
            // Set up auth state listener
            this.supabase.auth.onAuthStateChange(async (event, session) => {
                await this.handleAuthStateChange(event, session);
            });

            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('Supabase initialization failed:', error);
            return false;
        }
    }

    // Handle authentication state changes
    async handleAuthStateChange(event, session) {
        console.log('Auth state changed:', event, session?.user?.email);
        
        if (event === 'SIGNED_IN' && session) {
            await this.handleUserSignIn(session.user);
        } else if (event === 'SIGNED_OUT') {
            this.handleUserSignOut();
        }
    }

    // Handle user sign in
    async handleUserSignIn(user) {
        try {
            // Check if user profile exists
            let userProfile = await this.getUserProfile(user.id);
            
            if (!userProfile) {
                // Create user profile for new users
                userProfile = await this.createUserProfile(user);
            }

            // Update AuthManager
            if (typeof authManager !== 'undefined') {
                authManager.currentUser = {
                    id: user.id,
                    name: userProfile.name,
                    email: userProfile.email,
                    accountType: 'buyer',
                    authProvider: userProfile.auth_provider,
                    createdAt: userProfile.created_at,
                    lastLogin: new Date().toISOString()
                };

                // Update localStorage for compatibility
                storage.setCurrentUser(authManager.currentUser);
                authManager.updateUI();
                cartManager.updateCartUI();
                
                Components.createNotification(`Welcome, ${userProfile.name}!`, 'success');
            }
        } catch (error) {
            console.error('Error handling user sign in:', error);
            Components.createNotification('Login successful but profile setup failed', 'error');
        }
    }

    // Handle user sign out
    handleUserSignOut() {
        if (typeof authManager !== 'undefined') {
            authManager.currentUser = null;
            storage.clearCurrentUser();
            authManager.updateUI();
            cartManager.updateCartUI();
        }
    }

    // Get user profile
    async getUserProfile(userId) {
        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Error fetching user profile:', error);
            return null;
        }
    }

    // Create user profile
    async createUserProfile(user) {
        try {
            const provider = user.app_metadata?.provider || 'email';
            const name = user.user_metadata?.full_name || 
                        user.user_metadata?.name || 
                        user.email.split('@')[0];

            const profileData = {
                id: user.id,
                name: name,
                email: user.email,
                account_type: 'buyer',
                auth_provider: provider,
                avatar_url: user.user_metadata?.avatar_url || null,
                email_verified: user.email_confirmed_at ? true : false
            };

            const { data, error } = await this.supabase
                .from('users')
                .insert(profileData)
                .select()
                .single();

            if (error) throw error;

            return data;
        } catch (error) {
            console.error('Error creating user profile:', error);
            throw error;
        }
    }

    // Enhanced social authentication methods with better error handling
    async signInWithGoogle() {
        try {
            console.log('🔍 Attempting Google sign-in...');

            if (!this.isInitialized) {
                throw new Error('Supabase not initialized');
            }

            const { data, error } = await this.supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: `${window.location.origin}/`,
                    queryParams: {
                        access_type: 'offline',
                        prompt: 'consent',
                    }
                }
            });

            if (error) {
                console.error('Google OAuth error:', error);

                // Handle specific error types
                if (error.message && error.message.includes('provider is not enabled')) {
                    throw new Error('Google Sign-In is not configured in Supabase. Please contact support or use email/password login.');
                } else if (error.message && error.message.includes('redirect_uri_mismatch')) {
                    throw new Error('Google Sign-In redirect URL configuration error. Please contact support.');
                } else {
                    throw new Error(`Google Sign-In error: ${error.message}`);
                }
            }

            console.log('✅ Google sign-in initiated successfully');
            return { success: true, data };
        } catch (error) {
            console.error('Google sign-in error:', error);
            return { success: false, error: error.message };
        }
    }

    async signInWithApple() {
        try {
            console.log('🔍 Attempting Apple sign-in...');

            if (!this.isInitialized) {
                throw new Error('Supabase not initialized');
            }

            const { data, error } = await this.supabase.auth.signInWithOAuth({
                provider: 'apple',
                options: {
                    redirectTo: `${window.location.origin}/`
                }
            });

            if (error) {
                console.error('Apple OAuth error:', error);

                // Handle specific error types
                if (error.message && error.message.includes('provider is not enabled')) {
                    throw new Error('Apple Sign-In is not configured in Supabase. Please contact support or use email/password login.');
                } else if (error.message && error.message.includes('redirect_uri_mismatch')) {
                    throw new Error('Apple Sign-In redirect URL configuration error. Please contact support.');
                } else {
                    throw new Error(`Apple Sign-In error: ${error.message}`);
                }
            }

            console.log('✅ Apple sign-in initiated successfully');
            return { success: true, data };
        } catch (error) {
            console.error('Apple sign-in error:', error);
            return { success: false, error: error.message };
        }
    }

    // Test provider availability
    async testProvider(provider) {
        try {
            if (!this.isInitialized) {
                return { available: false, error: 'Supabase not initialized' };
            }

            // Attempt OAuth with skipBrowserRedirect to test configuration
            const { data, error } = await this.supabase.auth.signInWithOAuth({
                provider: provider,
                options: {
                    redirectTo: window.location.origin,
                    skipBrowserRedirect: true
                }
            });

            if (error) {
                if (error.message && error.message.includes('provider is not enabled')) {
                    return { available: false, error: 'Provider not enabled in Supabase dashboard' };
                } else {
                    return { available: false, error: error.message };
                }
            }

            return { available: true, error: null };
        } catch (error) {
            return { available: false, error: error.message };
        }
    }

    // Email authentication
    async signUpWithEmail(email, password, userData) {
        try {
            const { data, error } = await this.supabase.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        name: userData.name,
                        account_type: 'buyer'
                    }
                }
            });

            if (error) throw error;
            return { success: true, user: data.user };
        } catch (error) {
            console.error('Email sign-up error:', error);
            return { success: false, error: error.message };
        }
    }

    async signInWithEmail(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;
            return { success: true, user: data.user };
        } catch (error) {
            console.error('Email sign-in error:', error);
            return { success: false, error: error.message };
        }
    }

    // Sign out
    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            return { success: true };
        } catch (error) {
            console.error('Sign-out error:', error);
            return { success: false, error: error.message };
        }
    }

    // Data operations with localStorage fallback
    async syncProducts() {
        if (!this.isInitialized) return storage.getProducts();

        try {
            const { data, error } = await this.supabase
                .from('products')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;

            // Update localStorage cache
            storage.setProducts(data || []);
            return data || [];
        } catch (error) {
            console.error('Error syncing products:', error);
            return storage.getProducts(); // Fallback to localStorage
        }
    }

    async syncUserPurchases(userId) {
        if (!this.isInitialized) return storage.getPurchasesByUser(userId);

        try {
            const { data, error } = await this.supabase
                .from('purchases')
                .select('*')
                .eq('buyer_id', userId)
                .order('purchase_date', { ascending: false });

            if (error) throw error;

            // Update localStorage cache
            const allPurchases = storage.getPurchases();
            const otherPurchases = allPurchases.filter(p => p.buyerId !== userId);
            storage.setPurchases([...otherPurchases, ...(data || [])]);
            
            return data || [];
        } catch (error) {
            console.error('Error syncing purchases:', error);
            return storage.getPurchasesByUser(userId); // Fallback
        }
    }

    async syncProductReviews(productId) {
        if (!this.isInitialized) return storage.getReviewsByProduct(productId);

        try {
            const { data, error } = await this.supabase
                .from('reviews')
                .select('*')
                .eq('product_id', productId)
                .order('created_at', { ascending: false });

            if (error) throw error;

            // Update localStorage cache
            const allReviews = storage.getReviews();
            const otherReviews = allReviews.filter(r => r.productId !== productId);
            storage.setReviews([...otherReviews, ...(data || [])]);
            
            return data || [];
        } catch (error) {
            console.error('Error syncing reviews:', error);
            return storage.getReviewsByProduct(productId); // Fallback
        }
    }

    // Utility methods
    isConnected() {
        return this.isInitialized && this.supabase !== null;
    }

    getCurrentUser() {
        return this.supabase?.auth?.getUser() || null;
    }

    // Test connection
    async testConnection() {
        if (!this.isInitialized) return false;

        try {
            const { data, error } = await this.supabase
                .from('categories')
                .select('count')
                .limit(1);

            return !error;
        } catch (error) {
            console.error('Connection test failed:', error);
            return false;
        }
    }
}

// Create global instance
const supabaseIntegration = new SupabaseIntegration();

// Initialize on page load
document.addEventListener('DOMContentLoaded', async () => {
    await supabaseIntegration.init();
});

// Export for global access
window.supabaseIntegration = supabaseIntegration;
