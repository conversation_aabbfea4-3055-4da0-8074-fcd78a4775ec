// Comprehensive Functionality Testing for Obu Marketplace
class FunctionalityTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    // Run all tests
    async runAllTests() {
        if (this.isRunning) {
            console.log('Tests are already running...');
            return;
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 Starting Obu Marketplace Functionality Tests...');
        
        try {
            // Core System Tests
            await this.testStorageSystem();
            await this.testAuthenticationSystem();
            await this.testProductSystem();
            await this.testCartSystem();
            await this.testReviewSystem();
            await this.testUIResponsiveness();
            
            // Integration Tests
            await this.testPurchaseFlow();
            await this.testReviewFlow();
            
            this.displayResults();
        } catch (error) {
            console.error('Test suite failed:', error);
        } finally {
            this.isRunning = false;
        }
    }

    // Test storage system
    async testStorageSystem() {
        console.log('📦 Testing Storage System...');
        
        try {
            // Test localStorage operations
            const testData = { test: 'data', timestamp: Date.now() };
            storage.setItem('test_key', testData);
            const retrieved = storage.getItem('test_key');
            
            this.assert(
                JSON.stringify(retrieved) === JSON.stringify(testData),
                'Storage: Basic read/write operations'
            );
            
            // Test user operations
            const testUser = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'testpass123'
            };
            
            const createdUser = storage.addUser(testUser);
            this.assert(createdUser && createdUser.id, 'Storage: User creation');
            
            const foundUser = storage.getUserByEmail('<EMAIL>');
            this.assert(foundUser && foundUser.email === '<EMAIL>', 'Storage: User retrieval');
            
            // Cleanup
            storage.removeItem('test_key');
            
        } catch (error) {
            this.assert(false, `Storage: System error - ${error.message}`);
        }
    }

    // Test authentication system
    async testAuthenticationSystem() {
        console.log('🔐 Testing Authentication System...');
        
        try {
            // Test login modal creation
            authManager.showLoginModal();
            const loginModal = document.getElementById('loginModal');
            this.assert(loginModal && !loginModal.classList.contains('hidden'), 'Auth: Login modal display');
            
            // Test register modal creation
            authManager.showRegisterModal();
            const registerModal = document.getElementById('registerModal');
            this.assert(registerModal && !registerModal.classList.contains('hidden'), 'Auth: Register modal display');
            
            // Test password validation
            const weakPassword = Utils.validatePassword('123');
            const strongPassword = Utils.validatePassword('strongpass123');
            this.assert(!weakPassword.isValid, 'Auth: Weak password rejection');
            this.assert(strongPassword.isValid, 'Auth: Strong password acceptance');
            
            // Test email validation
            const invalidEmail = Utils.validateEmail('invalid-email');
            const validEmail = Utils.validateEmail('<EMAIL>');
            this.assert(!invalidEmail, 'Auth: Invalid email rejection');
            this.assert(validEmail, 'Auth: Valid email acceptance');
            
            // Hide modals
            if (loginModal) loginModal.classList.add('hidden');
            if (registerModal) registerModal.classList.add('hidden');
            
        } catch (error) {
            this.assert(false, `Auth: System error - ${error.message}`);
        }
    }

    // Test product system
    async testProductSystem() {
        console.log('🛍️ Testing Product System...');
        
        try {
            // Test product loading
            const products = storage.getProducts();
            this.assert(products && products.length > 0, 'Products: Data loading');
            
            // Test product filtering
            productManager.filterByCategory('Gaming');
            this.assert(productManager.currentFilter === 'Gaming', 'Products: Category filtering');
            
            // Test product search
            const searchResults = storage.searchProducts('Roblox');
            this.assert(searchResults && searchResults.length > 0, 'Products: Search functionality');
            
            // Test product card creation
            const firstProduct = products[0];
            const productCard = Components.createProductCard(firstProduct);
            this.assert(productCard && productCard.includes(firstProduct.title), 'Products: Card generation');
            
            // Test product modal
            productManager.showProductModal(firstProduct.id);
            const productModal = document.getElementById('productModal');
            this.assert(productModal && !productModal.classList.contains('hidden'), 'Products: Modal display');
            
            // Hide modal
            if (productModal) productModal.classList.add('hidden');
            
        } catch (error) {
            this.assert(false, `Products: System error - ${error.message}`);
        }
    }

    // Test cart system
    async testCartSystem() {
        console.log('🛒 Testing Cart System...');
        
        try {
            // Clear cart first
            storage.clearCart();
            
            // Test adding to cart
            const products = storage.getProducts();
            const testProduct = products[0];
            
            cartManager.addToCart(testProduct.id);
            const cartItems = storage.getCartItems();
            this.assert(cartItems && cartItems.length > 0, 'Cart: Add item');
            
            // Test cart display
            cartManager.showCart();
            const cartModal = document.getElementById('cartModal');
            this.assert(cartModal && !cartModal.classList.contains('hidden'), 'Cart: Modal display');
            
            // Test quantity update
            cartManager.updateQuantity(testProduct.id, 2);
            const updatedCart = storage.getCartItems();
            const updatedItem = updatedCart.find(item => item.productId === testProduct.id);
            this.assert(updatedItem && updatedItem.quantity === 2, 'Cart: Quantity update');
            
            // Test cart calculations
            const total = cartManager.calculateTotal();
            this.assert(total && total.subtotal > 0, 'Cart: Price calculations');
            
            // Hide modal
            if (cartModal) cartModal.classList.add('hidden');
            
        } catch (error) {
            this.assert(false, `Cart: System error - ${error.message}`);
        }
    }

    // Test review system
    async testReviewSystem() {
        console.log('⭐ Testing Review System...');
        
        try {
            // Test review eligibility
            const products = storage.getProducts();
            const testProduct = products[0];
            
            // Create a test user and purchase
            const testUser = {
                name: 'Review Tester',
                email: '<EMAIL>',
                password: 'testpass123'
            };
            const user = storage.addUser(testUser);
            
            const testPurchase = {
                buyerId: user.id,
                buyerName: user.name,
                buyerEmail: user.email,
                productId: testProduct.id,
                productTitle: testProduct.title,
                subtotal: testProduct.price,
                tax: Utils.calculateTax(testProduct.price),
                totalAmount: Utils.calculatePriceWithTax(testProduct.price),
                status: 'completed'
            };
            const purchase = storage.addPurchase(testPurchase);
            
            // Test review creation
            const testReview = {
                userId: user.id,
                userName: user.name,
                productId: testProduct.id,
                purchaseId: purchase.id,
                rating: 5,
                reviewText: 'This is a test review with sufficient length to pass validation.',
                pros: 'Great product',
                cons: null,
                purchaseDate: purchase.purchaseDate
            };
            
            const review = storage.addReview(testReview);
            this.assert(review && review.id, 'Reviews: Creation');
            
            // Test review retrieval
            const productReviews = storage.getReviewsByProduct(testProduct.id);
            this.assert(productReviews && productReviews.length > 0, 'Reviews: Retrieval');
            
            // Test duplicate prevention
            const hasReviewed = storage.hasUserReviewedProduct(user.id, testProduct.id);
            this.assert(hasReviewed, 'Reviews: Duplicate prevention');
            
            // Test product rating update
            const updatedProduct = storage.getProductById(testProduct.id);
            this.assert(updatedProduct.rating > 0, 'Reviews: Product rating update');
            
        } catch (error) {
            this.assert(false, `Reviews: System error - ${error.message}`);
        }
    }

    // Test UI responsiveness
    async testUIResponsiveness() {
        console.log('📱 Testing UI Responsiveness...');
        
        try {
            // Test modal responsiveness
            const modal = Components.createModal('testModal', 'Test Modal', '<p>Test content</p>');
            this.assert(modal.includes('max-w-md'), 'UI: Modal responsive classes');
            this.assert(modal.includes('p-2 md:p-4'), 'UI: Mobile padding');
            
            // Test button touch targets
            const products = storage.getProducts();
            const productCard = Components.createProductCard(products[0]);
            this.assert(productCard.includes('touch-manipulation'), 'UI: Touch-friendly buttons');
            
            // Test viewport meta tag
            const viewportMeta = document.querySelector('meta[name="viewport"]');
            this.assert(viewportMeta && viewportMeta.content.includes('user-scalable=no'), 'UI: Mobile viewport');
            
        } catch (error) {
            this.assert(false, `UI: System error - ${error.message}`);
        }
    }

    // Test complete purchase flow
    async testPurchaseFlow() {
        console.log('💳 Testing Purchase Flow...');
        
        try {
            // Create test user
            const testUser = {
                name: 'Purchase Tester',
                email: '<EMAIL>',
                password: 'testpass123'
            };
            const user = storage.addUser(testUser);
            authManager.currentUser = user;
            
            // Add product to cart
            const products = storage.getProducts();
            const testProduct = products[0];
            cartManager.addToCart(testProduct.id);
            
            // Test checkout process
            const cartItems = storage.getCartItems();
            this.assert(cartItems.length > 0, 'Purchase Flow: Cart has items');
            
            // Simulate purchase
            const purchaseData = {
                buyerId: user.id,
                buyerName: user.name,
                buyerEmail: user.email,
                productId: testProduct.id,
                productTitle: testProduct.title,
                subtotal: testProduct.price,
                tax: Utils.calculateTax(testProduct.price),
                totalAmount: Utils.calculatePriceWithTax(testProduct.price),
                paymentMethod: 'credit_card',
                status: 'completed'
            };
            
            const purchase = storage.addPurchase(purchaseData);
            this.assert(purchase && purchase.id, 'Purchase Flow: Purchase creation');
            
            // Test purchase history
            const userPurchases = storage.getPurchasesByUser(user.id);
            this.assert(userPurchases.length > 0, 'Purchase Flow: Purchase history');
            
        } catch (error) {
            this.assert(false, `Purchase Flow: System error - ${error.message}`);
        }
    }

    // Test complete review flow
    async testReviewFlow() {
        console.log('📝 Testing Review Flow...');
        
        try {
            // Get existing purchase from previous test
            const users = storage.getUsers();
            const testUser = users.find(u => u.email === '<EMAIL>');
            const purchases = storage.getPurchasesByUser(testUser.id);
            
            if (purchases.length > 0) {
                const purchase = purchases[0];
                
                // Test review modal creation
                reviewManager.showReviewModal(purchase.id);
                const reviewModal = document.getElementById('reviewModal');
                this.assert(reviewModal && !reviewModal.classList.contains('hidden'), 'Review Flow: Modal display');
                
                // Hide modal
                if (reviewModal) reviewModal.classList.add('hidden');
                
                this.assert(true, 'Review Flow: Complete flow tested');
            } else {
                this.assert(false, 'Review Flow: No purchases found for testing');
            }
            
        } catch (error) {
            this.assert(false, `Review Flow: System error - ${error.message}`);
        }
    }

    // Helper method to assert test results
    assert(condition, testName) {
        const result = {
            name: testName,
            passed: condition,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        if (condition) {
            console.log(`✅ ${testName}`);
        } else {
            console.log(`❌ ${testName}`);
        }
    }

    // Display test results
    displayResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Success Rate: ${percentage}%`);
        
        if (percentage >= 90) {
            console.log('🎉 Excellent! System is working well.');
        } else if (percentage >= 75) {
            console.log('⚠️ Good, but some issues need attention.');
        } else {
            console.log('🚨 Critical issues detected. Review failed tests.');
        }
        
        // Show failed tests
        const failed = this.testResults.filter(r => !r.passed);
        if (failed.length > 0) {
            console.log('\n❌ Failed Tests:');
            failed.forEach(test => console.log(`  - ${test.name}`));
        }
    }

    // Quick mobile test
    testMobileFeatures() {
        console.log('📱 Quick Mobile Features Test...');
        
        // Test touch events
        const hasTouch = 'ontouchstart' in window;
        console.log(`Touch Support: ${hasTouch ? '✅' : '❌'}`);
        
        // Test viewport
        const viewport = document.querySelector('meta[name="viewport"]');
        console.log(`Mobile Viewport: ${viewport ? '✅' : '❌'}`);
        
        // Test responsive classes
        const responsiveElements = document.querySelectorAll('[class*="md:"], [class*="lg:"], [class*="sm:"]');
        console.log(`Responsive Elements: ${responsiveElements.length > 0 ? '✅' : '❌'} (${responsiveElements.length} found)`);
        
        // Test touch-friendly buttons
        const touchButtons = document.querySelectorAll('.touch-manipulation, [class*="min-h-"]');
        console.log(`Touch-Friendly Buttons: ${touchButtons.length > 0 ? '✅' : '❌'} (${touchButtons.length} found)`);
    }
}

// Create global tester instance
const functionalityTester = new FunctionalityTester();

// Add to window for console access
window.testObuMarketplace = () => functionalityTester.runAllTests();
window.testMobileFeatures = () => functionalityTester.testMobileFeatures();
