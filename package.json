{"name": "obu-marketplace", "version": "1.0.0", "description": "A modern digital marketplace for gaming items and digital products", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build"}, "keywords": ["marketplace", "gaming", "digital", "ecommerce"], "author": "Obu Marketplace", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}