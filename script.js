// Obu Marketplace JavaScript

// Global state
let currentUser = null;
let cart = [];
let products = [];

// Categories data
const categories = [
    { name: 'Game Top Up', icon: 'fas fa-gamepad', gradient: 'from-cyan-400 to-blue-500', count: '500+' },
    { name: 'Gaming Accounts', icon: 'fas fa-user-circle', gradient: 'from-purple-400 to-pink-500', count: '1200+' },
    { name: 'Gift Cards', icon: 'fas fa-gift', gradient: 'from-green-400 to-emerald-500', count: '800+' },
    { name: 'Game Items', icon: 'fas fa-sword', gradient: 'from-red-400 to-pink-500', count: '2000+' },
    { name: 'Streaming', icon: 'fas fa-video', gradient: 'from-pink-400 to-purple-500', count: '300+' },
    { name: 'Software', icon: 'fas fa-desktop', gradient: 'from-indigo-400 to-blue-500', count: '150+' }
];

// Sample products data
const sampleProducts = [
    {
        id: 1,
        title: 'Roblox 1000 Robux',
        category: 'Game Top Up',
        price: 12.99,
        originalPrice: 15.99,
        image: 'https://via.placeholder.com/300x200?text=Roblox+Robux',
        seller: 'GameStore Pro',
        rating: 4.8,
        reviews: 245,
        instant: true
    },
    {
        id: 2,
        title: 'Steam Wallet $50',
        category: 'Gift Cards',
        price: 45.00,
        originalPrice: 50.00,
        image: 'https://via.placeholder.com/300x200?text=Steam+Wallet',
        seller: 'Digital Hub',
        rating: 4.9,
        reviews: 189,
        instant: true
    },
    {
        id: 3,
        title: 'Mobile Legends 2000 Diamonds',
        category: 'Game Top Up',
        price: 28.50,
        originalPrice: 32.00,
        image: 'https://via.placeholder.com/300x200?text=ML+Diamonds',
        seller: 'Gaming Central',
        rating: 4.7,
        reviews: 156,
        instant: true
    }
];

// DOM Elements
const loginBtn = document.getElementById('loginBtn');
const registerBtn = document.getElementById('registerBtn');
const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');
const closeLoginModal = document.getElementById('closeLoginModal');
const closeRegisterModal = document.getElementById('closeRegisterModal');
const switchToRegister = document.getElementById('switchToRegister');
const switchToLogin = document.getElementById('switchToLogin');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const categoriesGrid = document.getElementById('categoriesGrid');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    setupAnimations();
    renderCategories();
    renderFeaturedProducts();
    setupScrollEffects();
});

function setupAnimations() {
    // Add loading animation to page
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease-in-out';
        document.body.style.opacity = '1';
    }, 100);

    // Add hover effects to navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add glow effect to buttons
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.5)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
}

function setupScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.hero-section');
        const heroContent = document.querySelector('.hero-content');

        // Only apply parallax if hero section is visible
        if (heroSection && heroContent && scrolled < window.innerHeight) {
            requestAnimationFrame(() => {
                heroContent.style.transform = `translateY(${scrolled * 0.3}px)`;
            });
        }

        ticking = false;
    }

    // Throttled scroll listener
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });

    // Intersection Observer for fade-in animations (more performant)
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements that should fade in
    setTimeout(() => {
        const elementsToObserve = document.querySelectorAll('.feature-card, .product-card');
        elementsToObserve.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
            observer.observe(element);
        });
    }, 500);
}

function initializeApp() {
    // Check for saved user session
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUIForLoggedInUser();
    }
    
    // Load cart from localStorage
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
        cart = JSON.parse(savedCart);
        updateCartUI();
    }
}

function setupEventListeners() {
    // Modal controls
    loginBtn.addEventListener('click', () => showModal('login'));
    registerBtn.addEventListener('click', () => showModal('register'));
    closeLoginModal.addEventListener('click', () => hideModal('login'));
    closeRegisterModal.addEventListener('click', () => hideModal('register'));
    switchToRegister.addEventListener('click', () => switchModal('register'));
    switchToLogin.addEventListener('click', () => switchModal('login'));
    
    // Form submissions
    loginForm.addEventListener('submit', handleLogin);
    registerForm.addEventListener('submit', handleRegister);
    
    // Close modals when clicking outside
    loginModal.addEventListener('click', (e) => {
        if (e.target === loginModal) hideModal('login');
    });
    registerModal.addEventListener('click', (e) => {
        if (e.target === registerModal) hideModal('register');
    });
    
    // Search functionality
    const searchInput = document.querySelector('input[type="text"]');
    searchInput.addEventListener('input', handleSearch);
}

function showModal(type) {
    const modal = type === 'login' ? loginModal : registerModal;
    modal.classList.remove('hidden');
    modal.querySelector('.bg-white').classList.add('modal-enter');
}

function hideModal(type) {
    const modal = type === 'login' ? loginModal : registerModal;
    modal.classList.add('hidden');
}

function switchModal(type) {
    hideModal(type === 'login' ? 'register' : 'login');
    setTimeout(() => showModal(type), 100);
}

function handleLogin(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const email = formData.get('email') || e.target.querySelector('input[type="email"]').value;
    const password = formData.get('password') || e.target.querySelector('input[type="password"]').value;
    
    // Simulate login (in real app, this would be an API call)
    if (email && password) {
        currentUser = {
            id: Date.now(),
            email: email,
            name: email.split('@')[0],
            type: 'buyer',
            joinDate: new Date().toISOString()
        };
        
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        updateUIForLoggedInUser();
        hideModal('login');
        showNotification('Login successful!', 'success');
    }
}

function handleRegister(e) {
    e.preventDefault();
    const form = e.target;
    const name = form.querySelector('input[type="text"]').value;
    const email = form.querySelector('input[type="email"]').value;
    const accountType = form.querySelector('select').value;
    const password = form.querySelector('input[type="password"]').value;
    const agreeTerms = form.querySelector('input[type="checkbox"]').checked;
    
    if (!agreeTerms) {
        showNotification('Please agree to the terms and conditions', 'error');
        return;
    }
    
    // Simulate registration
    currentUser = {
        id: Date.now(),
        name: name,
        email: email,
        type: accountType,
        joinDate: new Date().toISOString()
    };
    
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    updateUIForLoggedInUser();
    hideModal('register');
    showNotification('Account created successfully!', 'success');
}

function updateUIForLoggedInUser() {
    if (currentUser) {
        loginBtn.textContent = currentUser.name;
        loginBtn.onclick = showUserMenu;
        registerBtn.style.display = 'none';
    }
}

function showUserMenu() {
    // Create user dropdown menu
    const menu = document.createElement('div');
    menu.className = 'absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50';
    menu.innerHTML = `
        <div class="py-1">
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Orders</a>
            ${currentUser.type !== 'buyer' ? '<a href="dashboard.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Seller Dashboard</a>' : ''}
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="logout()">Logout</a>
        </div>
    `;
    
    // Position and show menu
    const rect = loginBtn.getBoundingClientRect();
    menu.style.position = 'fixed';
    menu.style.top = rect.bottom + 'px';
    menu.style.right = (window.innerWidth - rect.right) + 'px';
    
    document.body.appendChild(menu);
    
    // Remove menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function removeMenu() {
            menu.remove();
            document.removeEventListener('click', removeMenu);
        });
    }, 100);
}

function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    loginBtn.textContent = 'Login';
    loginBtn.onclick = () => showModal('login');
    registerBtn.style.display = 'block';
    showNotification('Logged out successfully', 'success');
}

function renderCategories() {
    categoriesGrid.innerHTML = categories.map((category, index) => `
        <div class="category-card group" onclick="browseCategory('${category.name}')">
            <div class="feature-icon-wrapper mb-6">
                <div class="feature-icon bg-gradient-to-br ${category.gradient} w-20 h-20 rounded-2xl">
                    <i class="${category.icon} text-white text-3xl"></i>
                </div>
                <div class="feature-glow"></div>
            </div>
            <h3 class="text-lg font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                ${category.name}
            </h3>
            <p class="text-sm text-white/60">${category.count} items</p>
        </div>
    `).join('');

    // Add gentle staggered animation
    const cards = document.querySelectorAll('.category-card');
    cards.forEach((card, index) => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
        card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    });
}

function renderFeaturedProducts() {
    const productsSection = document.createElement('section');
    productsSection.className = 'featured-products-section py-24 bg-dark-secondary/20 relative';
    productsSection.innerHTML = `
        <div class="absolute inset-0 bg-gradient-to-b from-dark-primary via-dark-secondary/10 to-dark-primary"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-black text-white mb-6">
                    Featured
                    <span class="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">Products</span>
                </h2>
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                    Discover the best deals and most popular items in our premium marketplace
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="featuredProducts">
                ${sampleProducts.map(product => createProductCard(product)).join('')}
            </div>
        </div>
    `;

    // Insert after categories section
    const categoriesSection = document.querySelector('.categories-section');
    categoriesSection.parentNode.insertBefore(productsSection, categoriesSection.nextSibling);

    // Set initial state for product cards (they'll be animated by Intersection Observer)
    setTimeout(() => {
        const productCards = document.querySelectorAll('#featuredProducts .product-card');
        productCards.forEach((card) => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
    }, 100);
}

function createProductCard(product) {
    const discount = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    const finalPrice = calculatePriceWithTax(product.price);

    return `
        <div class="product-card group">
            <div class="relative overflow-hidden rounded-t-2xl">
                <img src="${product.image}" alt="${product.title}" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110">
                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                ${product.instant ? '<span class="absolute top-3 left-3 bg-gradient-to-r from-green-400 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">⚡ Instant</span>' : ''}
                ${discount > 0 ? `<span class="absolute top-3 right-3 bg-gradient-to-r from-red-400 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">-${discount}%</span>` : ''}
            </div>

            <div class="p-6">
                <h3 class="font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">${product.title}</h3>
                <p class="text-sm text-white/60 mb-3">${product.category}</p>

                <div class="flex items-center mb-3">
                    <div class="rating text-yellow-400">
                        ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                    </div>
                    <span class="text-sm text-white/60 ml-2">(${product.reviews})</span>
                </div>

                <div class="seller-badge mb-4">${product.seller}</div>

                <div class="flex items-center justify-between mb-4">
                    <div>
                        <span class="price text-xl font-bold">$${product.price}</span>
                        ${product.originalPrice > product.price ? `<span class="price-original text-sm ml-2">$${product.originalPrice}</span>` : ''}
                    </div>
                </div>

                <div class="tax-info mb-4">
                    <i class="fas fa-info-circle mr-2"></i>
                    Final price: <span class="font-semibold">$${finalPrice.toFixed(2)}</span> (includes 10% tax)
                </div>

                <button onclick="addToCart(${product.id})" class="btn-primary w-full py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Add to Cart
                </button>
            </div>
        </div>
    `;
}

function calculatePriceWithTax(price) {
    return price * 1.10; // 10% tax
}

function addToCart(productId) {
    if (!currentUser) {
        showNotification('Please login to add items to cart', 'warning');
        showModal('login');
        return;
    }
    
    const product = sampleProducts.find(p => p.id === productId);
    if (product) {
        cart.push({...product, addedAt: new Date().toISOString()});
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartUI();
        showNotification('Item added to cart!', 'success');
    }
}

function updateCartUI() {
    // Update cart icon with count
    const cartIcon = document.querySelector('.fa-shopping-cart').parentElement;
    const existingBadge = cartIcon.querySelector('.notification-badge');
    
    if (cart.length > 0) {
        if (!existingBadge) {
            const badge = document.createElement('span');
            badge.className = 'notification-badge';
            badge.textContent = cart.length;
            cartIcon.style.position = 'relative';
            cartIcon.appendChild(badge);
        } else {
            existingBadge.textContent = cart.length;
        }
    } else if (existingBadge) {
        existingBadge.remove();
    }
}

function browseCategory(categoryName) {
    showNotification(`Browsing ${categoryName} category`, 'success');
    // In a real app, this would navigate to a category page
}

function handleSearch(e) {
    const query = e.target.value.toLowerCase();
    if (query.length > 2) {
        // Simulate search suggestions
        const suggestions = sampleProducts
            .filter(p => p.title.toLowerCase().includes(query))
            .slice(0, 5);
        
        showSearchSuggestions(suggestions, e.target);
    } else {
        hideSearchSuggestions();
    }
}

function showSearchSuggestions(suggestions, inputElement) {
    hideSearchSuggestions();
    
    if (suggestions.length === 0) return;
    
    const suggestionsDiv = document.createElement('div');
    suggestionsDiv.className = 'search-suggestions';
    suggestionsDiv.innerHTML = suggestions.map(product => `
        <div class="search-suggestion" onclick="selectProduct(${product.id})">
            <div class="flex items-center">
                <img src="${product.image}" alt="${product.title}" class="w-8 h-8 rounded mr-3">
                <div>
                    <div class="font-medium">${product.title}</div>
                    <div class="text-sm text-gray-600">$${product.price}</div>
                </div>
            </div>
        </div>
    `).join('');
    
    inputElement.parentElement.appendChild(suggestionsDiv);
}

function hideSearchSuggestions() {
    const existing = document.querySelector('.search-suggestions');
    if (existing) existing.remove();
}

function selectProduct(productId) {
    hideSearchSuggestions();
    showNotification('Product selected!', 'success');
    // In a real app, this would navigate to the product page
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} fixed top-6 right-6 z-50 max-w-sm transform translate-x-full`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-3 text-lg"></i>
            <span class="font-medium">${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Slide in animation
    setTimeout(() => {
        notification.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading states for better UX
function showLoading(element) {
    element.classList.add('loading');
    const spinner = document.createElement('div');
    spinner.className = 'spinner mx-auto';
    element.appendChild(spinner);
}

function hideLoading(element) {
    element.classList.remove('loading');
    const spinner = element.querySelector('.spinner');
    if (spinner) spinner.remove();
}
