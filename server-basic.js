const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;

// Sample data
const users = [
    {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        accountType: 'buyer',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        accountType: 'buyer',
        createdAt: new Date().toISOString()
    }
];

const products = [
    {
        id: 1,
        title: 'Roblox 1000 Robux',
        description: 'Get 1000 Robux instantly delivered to your account',
        category: 'Game Top Up',
        price: 12.99,
        originalPrice: 15.99,
        image: 'https://via.placeholder.com/300x200?text=Roblox+Robux',
        stock: 100,
        rating: 4.8,
        reviews: 245,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        title: 'Steam Wallet $50',
        description: 'Steam Wallet Gift Card - $50 USD',
        category: 'Gift Cards',
        price: 45.00,
        originalPrice: 50.00,
        image: 'https://via.placeholder.com/300x200?text=Steam+Wallet',
        stock: 50,
        rating: 4.9,
        reviews: 189,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 3,
        title: 'Mobile Legends 2000 Diamonds',
        description: 'Mobile Legends Bang Bang 2000 Diamonds top up',
        category: 'Game Top Up',
        price: 28.50,
        originalPrice: 32.00,
        image: 'https://via.placeholder.com/300x200?text=ML+Diamonds',
        stock: 75,
        rating: 4.7,
        reviews: 156,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    }
];

// Helper functions
function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon'
    };
    return mimeTypes[ext] || 'text/plain';
}

function sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(JSON.stringify(data));
}

function sendFile(res, filePath) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        const contentType = getContentType(filePath);
        res.writeHead(200, { 
            'Content-Type': contentType,
            'Access-Control-Allow-Origin': '*'
        });
        res.end(data);
    });
}

function parseBody(req, callback) {
    let body = '';
    req.on('data', chunk => {
        body += chunk.toString();
    });
    req.on('end', () => {
        try {
            const parsed = body ? JSON.parse(body) : {};
            callback(null, parsed);
        } catch (err) {
            callback(err, null);
        }
    });
}

// Create server
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const method = req.method;

    console.log(`${method} ${pathname}`);

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end();
        return;
    }

    // API Routes
    if (pathname.startsWith('/api/')) {
        
        // Health check
        if (pathname === '/api/health' && method === 'GET') {
            sendJSON(res, {
                status: 'OK',
                message: 'Obu Marketplace API is running',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            });
            return;
        }

        // Get products
        if (pathname === '/api/products' && method === 'GET') {
            const { category, search } = parsedUrl.query;
            let filteredProducts = products.filter(p => p.status === 'active');
            
            if (category) {
                filteredProducts = filteredProducts.filter(p => p.category === category);
            }
            
            if (search) {
                const searchLower = search.toLowerCase();
                filteredProducts = filteredProducts.filter(p => 
                    p.title.toLowerCase().includes(searchLower) || 
                    p.description.toLowerCase().includes(searchLower)
                );
            }
            
            sendJSON(res, filteredProducts);
            return;
        }

        // Get single product
        if (pathname.startsWith('/api/products/') && method === 'GET') {
            const productId = parseInt(pathname.split('/')[3]);
            const product = products.find(p => p.id === productId && p.status === 'active');
            
            if (!product) {
                sendJSON(res, { error: 'Product not found' }, 404);
                return;
            }
            
            sendJSON(res, product);
            return;
        }

        // User login
        if (pathname === '/api/login' && method === 'POST') {
            parseBody(req, (err, body) => {
                if (err) {
                    sendJSON(res, { error: 'Invalid JSON' }, 400);
                    return;
                }
                
                const { email, password } = body;
                const user = users.find(u => u.email === email && u.password === password);
                
                if (!user) {
                    sendJSON(res, { error: 'Invalid credentials' }, 401);
                    return;
                }
                
                const { password: _, ...userResponse } = user;
                sendJSON(res, {
                    message: 'Login successful',
                    user: userResponse
                });
            });
            return;
        }

        // User registration
        if (pathname === '/api/register' && method === 'POST') {
            parseBody(req, (err, body) => {
                if (err) {
                    sendJSON(res, { error: 'Invalid JSON' }, 400);
                    return;
                }
                
                const { name, email, password, accountType = 'buyer' } = body;
                
                const existingUser = users.find(u => u.email === email);
                if (existingUser) {
                    sendJSON(res, { error: 'Email already registered' }, 400);
                    return;
                }
                
                const newUser = {
                    id: users.length + 1,
                    name,
                    email,
                    password,
                    accountType,
                    createdAt: new Date().toISOString()
                };
                
                users.push(newUser);
                
                const { password: _, ...userResponse } = newUser;
                sendJSON(res, {
                    message: 'User created successfully',
                    user: userResponse
                }, 201);
            });
            return;
        }

        // Get categories
        if (pathname === '/api/categories' && method === 'GET') {
            const categories = [...new Set(products.map(p => p.category))];
            sendJSON(res, categories);
            return;
        }

        // API route not found
        sendJSON(res, { error: 'API endpoint not found' }, 404);
        return;
    }

    // Serve static files
    if (pathname === '/' || pathname === '/index.html') {
        sendFile(res, path.join(__dirname, 'index.html'));
        return;
    }

    // Serve other static files
    const filePath = path.join(__dirname, pathname);
    
    // Security check - prevent directory traversal
    if (!filePath.startsWith(__dirname)) {
        res.writeHead(403, { 'Content-Type': 'text/plain' });
        res.end('Forbidden');
        return;
    }

    fs.stat(filePath, (err, stats) => {
        if (err || !stats.isFile()) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        sendFile(res, filePath);
    });
});

// Start server
server.listen(PORT, () => {
    console.log(`🚀 Obu Marketplace server running on http://localhost:${PORT}`);
    console.log(`📊 API endpoints available at: http://localhost:${PORT}/api/`);
    console.log(`💾 Using in-memory database (${users.length} users, ${products.length} products)`);
    console.log(`🔧 Node.js version: ${process.version}`);
    console.log(`⏰ Started at: ${new Date().toLocaleString()}`);
    console.log('');
    console.log('Available API endpoints:');
    console.log('  GET  /api/health');
    console.log('  GET  /api/products');
    console.log('  GET  /api/products/:id');
    console.log('  POST /api/login');
    console.log('  POST /api/register');
    console.log('  GET  /api/categories');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Handle server errors
server.on('error', (err) => {
    console.error('Server error:', err);
});

module.exports = server;
