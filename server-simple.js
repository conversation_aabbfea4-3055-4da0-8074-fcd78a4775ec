const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// In-memory data storage (for development without SQLite)
let users = [
    {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123', // In production, this would be hashed
        accountType: 'buyer',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        accountType: 'buyer',
        createdAt: new Date().toISOString()
    }
];

let products = [
    {
        id: 1,
        title: 'Roblox 1000 Robux',
        description: 'Get 1000 Robux instantly delivered to your account',
        category: 'Game Top Up',
        price: 12.99,
        originalPrice: 15.99,
        image: 'https://via.placeholder.com/300x200?text=Roblox+Robux',
        stock: 100,
        rating: 4.8,
        reviews: 245,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        title: 'Steam Wallet $50',
        description: 'Steam Wallet Gift Card - $50 USD',
        category: 'Gift Cards',
        price: 45.00,
        originalPrice: 50.00,
        image: 'https://via.placeholder.com/300x200?text=Steam+Wallet',
        stock: 50,
        rating: 4.9,
        reviews: 189,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 3,
        title: 'Mobile Legends 2000 Diamonds',
        description: 'Mobile Legends Bang Bang 2000 Diamonds top up',
        category: 'Game Top Up',
        price: 28.50,
        originalPrice: 32.00,
        image: 'https://via.placeholder.com/300x200?text=ML+Diamonds',
        stock: 75,
        rating: 4.7,
        reviews: 156,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 4,
        title: 'Valorant 2150 VP',
        description: 'Valorant Points for purchasing skins and battle pass',
        category: 'Game Top Up',
        price: 19.99,
        originalPrice: 24.99,
        image: 'https://via.placeholder.com/300x200?text=Valorant+VP',
        stock: 200,
        rating: 4.6,
        reviews: 98,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    },
    {
        id: 5,
        title: 'Netflix Premium 1 Month',
        description: 'Netflix Premium subscription for 1 month',
        category: 'Subscriptions',
        price: 15.99,
        originalPrice: 17.99,
        image: 'https://via.placeholder.com/300x200?text=Netflix+Premium',
        stock: 30,
        rating: 4.9,
        reviews: 67,
        sellerId: 1,
        status: 'active',
        createdAt: new Date().toISOString()
    }
];

let orders = [];
let nextUserId = users.length + 1;
let nextOrderId = 1;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// Routes

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// API Routes

// Get all products
app.get('/api/products', (req, res) => {
    const { category, search, limit = 20, offset = 0 } = req.query;
    
    let filteredProducts = products.filter(p => p.status === 'active');
    
    if (category) {
        filteredProducts = filteredProducts.filter(p => p.category === category);
    }
    
    if (search) {
        const searchLower = search.toLowerCase();
        filteredProducts = filteredProducts.filter(p => 
            p.title.toLowerCase().includes(searchLower) || 
            p.description.toLowerCase().includes(searchLower)
        );
    }
    
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    res.json(paginatedProducts);
});

// Get product by ID
app.get('/api/products/:id', (req, res) => {
    const productId = parseInt(req.params.id);
    const product = products.find(p => p.id === productId && p.status === 'active');
    
    if (!product) {
        return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json(product);
});

// User registration
app.post('/api/register', (req, res) => {
    const { name, email, password, accountType = 'buyer' } = req.body;
    
    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
        return res.status(400).json({ error: 'Email already registered' });
    }
    
    // Create new user
    const newUser = {
        id: nextUserId++,
        name,
        email,
        password, // In production, this would be hashed
        accountType,
        createdAt: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // Return user without password
    const { password: _, ...userResponse } = newUser;
    
    res.status(201).json({
        message: 'User created successfully',
        user: userResponse
    });
});

// User login
app.post('/api/login', (req, res) => {
    const { email, password } = req.body;
    
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
        return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Return user without password
    const { password: _, ...userResponse } = user;
    
    res.json({
        message: 'Login successful',
        user: userResponse
    });
});

// Create order
app.post('/api/orders', (req, res) => {
    const { productId, quantity = 1, userId } = req.body;
    
    const product = products.find(p => p.id === parseInt(productId) && p.status === 'active');
    if (!product) {
        return res.status(404).json({ error: 'Product not found' });
    }
    
    const user = users.find(u => u.id === parseInt(userId));
    if (!user) {
        return res.status(404).json({ error: 'User not found' });
    }
    
    if (product.stock < quantity) {
        return res.status(400).json({ error: 'Insufficient stock' });
    }
    
    const subtotal = product.price * quantity;
    const tax = subtotal * 0.10; // 10% tax
    const total = subtotal + tax;
    
    const order = {
        id: nextOrderId++,
        userId: user.id,
        productId: product.id,
        productTitle: product.title,
        quantity,
        subtotal,
        tax,
        total,
        status: 'completed',
        createdAt: new Date().toISOString()
    };
    
    orders.push(order);
    
    // Update product stock
    product.stock -= quantity;
    
    res.status(201).json({
        message: 'Order created successfully',
        order
    });
});

// Get user orders
app.get('/api/orders/:userId', (req, res) => {
    const userId = parseInt(req.params.userId);
    const userOrders = orders.filter(o => o.userId === userId);
    
    res.json(userOrders);
});

// Get categories
app.get('/api/categories', (req, res) => {
    const categories = [...new Set(products.map(p => p.category))];
    res.json(categories);
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'Obu Marketplace API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Obu Marketplace server running on http://localhost:${PORT}`);
    console.log(`📊 API endpoints available at: http://localhost:${PORT}/api/`);
    console.log(`💾 Using in-memory database (${users.length} users, ${products.length} products)`);
    console.log(`🔧 Node.js version: ${process.version}`);
    console.log(`⏰ Started at: ${new Date().toLocaleString()}`);
});

module.exports = app;
