@echo off
echo ========================================
echo    Node.js Setup for Obu Marketplace
echo ========================================
echo.

echo Detected Node.js installation at: C:\Program Files\nodejs
echo.

echo Setting up environment variables...
set "PATH=C:\Program Files\nodejs;%PATH%"
set "NODE_PATH=C:\Program Files\nodejs"

echo Testing Node.js access...
"C:\Program Files\nodejs\node.exe" --version
if %errorlevel% neq 0 (
    echo ERROR: Cannot access Node.js!
    pause
    exit /b 1
)

echo Testing npm access...
"C:\Program Files\nodejs\npm.cmd" --version
if %errorlevel% neq 0 (
    echo ERROR: Cannot access npm!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Installing Dependencies
echo ========================================
echo.

echo Installing project dependencies...
echo This may take a few minutes...
echo.

"C:\Program Files\nodejs\npm.cmd" install --verbose

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo    Installation Failed - Trying Alternative
    echo ========================================
    echo.
    
    echo Trying to install without sqlite3 first...
    "C:\Program Files\nodejs\npm.cmd" install --ignore-scripts
    
    echo.
    echo Installing sqlite3 separately...
    "C:\Program Files\nodejs\npm.cmd" install sqlite3 --build-from-source
    
    if %errorlevel% neq 0 (
        echo.
        echo SQLite3 installation failed. Using alternative database setup...
        echo The server will use in-memory database instead.
        echo.
    )
)

echo.
echo ========================================
echo    Testing Server Startup
echo ========================================
echo.

echo Testing server startup...
timeout /t 2 /nobreak >nul

start /b "Obu Marketplace Server" "C:\Program Files\nodejs\node.exe" server.js
timeout /t 5 /nobreak >nul

echo.
echo Checking if server is running...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Server is running successfully!
    echo.
    echo 🌐 Marketplace available at: http://localhost:3000
    echo 📊 API endpoints available at: http://localhost:3000/api/
    echo.
    echo Press any key to open the marketplace in your browser...
    pause >nul
    start http://localhost:3000
) else (
    echo ⚠️ Server may not be running properly.
    echo Check the console output above for any errors.
    echo.
    echo You can manually start the server with:
    echo "C:\Program Files\nodejs\node.exe" server.js
    echo.
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo To start the server in the future, use:
echo   start-server.bat
echo.
echo To stop the server:
echo   Press Ctrl+C in the server window
echo.
pause
