@echo off
echo ========================================
echo    Starting Obu Marketplace Server
echo ========================================
echo.

echo Setting Node.js path...
set "PATH=C:\Program Files\nodejs;%PATH%"

echo Checking Node.js...
"C:\Program Files\nodejs\node.exe" --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not accessible!
    echo Please run setup-nodejs.bat first.
    pause
    exit /b 1
)

echo.
echo Starting server...
echo.
echo 🌐 Marketplace will be available at: http://localhost:3000
echo 📊 API endpoints available at: http://localhost:3000/api/
echo.
echo Press Ctrl+C to stop the server
echo.

"C:\Program Files\nodejs\node.exe" server.js

pause
