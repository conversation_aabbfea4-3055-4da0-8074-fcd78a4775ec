/* Obu Marketplace - Dark Premium Theme */

/* CSS Variables for Dark Theme */
:root {
    --dark-primary: #0a0a0f;
    --dark-secondary: #1a1a2e;
    --dark-tertiary: #16213e;
    --accent-cyan: #00d4ff;
    --accent-purple: #8b5cf6;
    --accent-pink: #ec4899;
    --accent-green: #10b981;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-muted: rgba(255, 255, 255, 0.5);
    --border-color: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --shadow-glow: 0 0 30px rgba(0, 212, 255, 0.3);
    --shadow-glow-purple: 0 0 30px rgba(139, 92, 246, 0.3);
    --shadow-glow-pink: 0 0 30px rgba(236, 72, 153, 0.3);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--dark-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.font-inter {
    font-family: 'Inter', sans-serif;
}

.bg-dark-primary {
    background: var(--dark-primary);
}

.bg-dark-secondary {
    background: var(--dark-secondary);
}

/* Utility Classes */
.glow-text {
    text-shadow: 0 0 20px currentColor;
}

.glow-text-white {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Navigation Styles */
.nav-glass {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-glass.scrolled {
    background: rgba(10, 10, 15, 0.98);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-link {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: var(--text-primary) !important;
    text-shadow: 0 0 10px currentColor;
}

/* Search Input */
.search-input {
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.search-input:focus {
    background: rgba(26, 26, 46, 0.7);
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
}

.search-glow {
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple), var(--accent-pink));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(10px);
}

.search-input:focus + .search-glow {
    opacity: 0.3;
}

/* Search Button */
.search-btn {
    position: relative;
    background: var(--accent-cyan);
    color: var(--dark-primary);
    font-weight: 600;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.search-btn:hover {
    background: var(--accent-cyan-hover, #0ea5e9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.4);
}

.search-btn:active {
    transform: translateY(0);
}

.search-btn i {
    font-size: 0.875rem;
}

/* Mobile Menu */
.mobile-menu-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 60;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 0;
}

.mobile-menu {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(25px);
    width: 320px;
    height: 100vh;
    overflow-y: auto;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.open {
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-content {
    padding: 1.5rem;
}

.mobile-menu-section {
    margin-bottom: 2rem;
}

.mobile-menu-section:last-child {
    margin-bottom: 0;
}

.mobile-menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.mobile-menu-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.mobile-menu-btn {
    touch-action: manipulation;
    min-height: 48px;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-primary:hover::before {
    opacity: 1;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-cyan);
    border: 2px solid var(--accent-cyan);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-secondary:hover::before {
    opacity: 0.1;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
    color: white;
    border-color: var(--accent-purple);
}

/* Cart Button */
.cart-btn {
    position: relative;
}

.cart-glow {
    position: absolute;
    inset: -10px;
    background: radial-gradient(circle, var(--accent-cyan) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
    filter: blur(10px);
    z-index: -1;
}

.cart-btn:hover .cart-glow {
    opacity: 0.3;
}

/* Hero Section */
.hero-section {
    position: relative;
    background: radial-gradient(ellipse at center, var(--dark-secondary) 0%, var(--dark-primary) 70%);
}

.hero-bg {
    position: absolute;
    inset: 0;
    background:
        radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

.hero-content {
    animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    }
    100% {
        text-shadow: 0 0 40px rgba(139, 92, 246, 0.8), 0 0 60px rgba(236, 72, 153, 0.4);
    }
}

/* Floating Particles */
.floating-particles {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-cyan);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 0 10px currentColor;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 8s;
    background: var(--accent-purple);
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
    background: var(--accent-pink);
}

.particle:nth-child(4) {
    left: 60%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    left: 80%;
    animation-delay: 4s;
    animation-duration: 6s;
    background: var(--accent-purple);
}

.particle:nth-child(6) {
    left: 90%;
    animation-delay: 5s;
    animation-duration: 8s;
    background: var(--accent-pink);
}

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10%, 90% {
        opacity: 1;
    }
    50% {
        transform: translateY(-10vh) rotate(180deg);
    }
}

/* Hero Buttons */
.btn-hero-primary {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-hero-primary .btn-glow {
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple), var(--accent-pink), var(--accent-cyan));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(10px);
    animation: rotateGlow 3s linear infinite;
}

.btn-hero-primary:hover .btn-glow {
    opacity: 0.7;
}

.btn-hero-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.btn-hero-secondary .btn-glow-secondary {
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, transparent, var(--accent-cyan), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(5px);
}

.btn-hero-secondary:hover .btn-glow-secondary {
    opacity: 0.5;
}

.btn-hero-secondary:hover {
    border-color: var(--accent-cyan);
    color: var(--accent-cyan);
}

@keyframes rotateGlow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Hero Stats */
.hero-stats {
    animation: statsSlideUp 1.5s ease-out 0.5s both;
}

@keyframes statsSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

/* Scroll Indicator */
.scroll-indicator {
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-cyan);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: arrowGlow 2s ease-in-out infinite alternate;
}

@keyframes arrowGlow {
    0% {
        border-color: var(--accent-cyan);
        box-shadow: 0 0 10px var(--accent-cyan);
    }
    100% {
        border-color: var(--accent-purple);
        box-shadow: 0 0 20px var(--accent-purple);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-10px) translateX(-50%);
    }
    60% {
        transform: translateY(-5px) translateX(-50%);
    }
}

/* Features Section */
.features-section {
    position: relative;
}

.section-title {
    animation: titleSlideIn 1s ease-out;
}

@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card {
    background: rgba(26, 26, 46, 0.6);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 24px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(139, 92, 246, 0.08));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: rgba(0, 212, 255, 0.35);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 30px rgba(0, 212, 255, 0.15);
}

.feature-icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.feature-glow {
    position: absolute;
    inset: -10px;
    background: inherit;
    border-radius: inherit;
    filter: blur(20px);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.feature-card:hover .feature-glow {
    opacity: 0.6;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-card {
    position: relative;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(25px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(0, 212, 255, 0.1);
    border-radius: 1rem;
    width: 100%;
    max-width: 28rem;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-card::before {
    content: '';
    position: absolute;
    inset: -1px;
    background: linear-gradient(45deg, var(--accent-cyan), var(--accent-purple), var(--accent-pink));
    border-radius: inherit;
    opacity: 0.2;
    z-index: -1;
    filter: blur(8px);
}

.modal-input {
    position: relative;
    background: rgba(10, 10, 15, 0.8) !important;
    backdrop-filter: blur(10px);
}

.modal-input:focus {
    background: rgba(10, 10, 15, 0.9) !important;
    transform: translateY(-1px);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.modal-enter {
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--accent-cyan), var(--accent-purple));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--accent-purple), var(--accent-pink));
}

/* Notifications */
.alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 16px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: notificationSlide 0.3s ease-out;
}

@keyframes notificationSlide {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-green);
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #ff6b6b;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #fbbf24;
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.alert-info {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-cyan);
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
}

/* Product Cards */
.product-card {
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(139, 92, 246, 0.08));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 212, 255, 0.4);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 25px rgba(0, 212, 255, 0.15);
}

/* Category Cards */
.category-card {
    background: rgba(26, 26, 46, 0.7);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.08), rgba(139, 92, 246, 0.08));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.category-card:hover::before {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.01);
    border-color: rgba(0, 212, 255, 0.35);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 0 30px rgba(0, 212, 255, 0.15);
}

/* Price Display */
.price {
    font-weight: 700;
    color: var(--accent-green);
    text-shadow: 0 0 10px currentColor;
}

.price-original {
    text-decoration: line-through;
    color: var(--text-muted);
    font-weight: 400;
}

/* Tax Info */
.tax-info {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #fbbf24;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Seller Badge */
.seller-badge {
    background: rgba(139, 92, 246, 0.1);
    color: var(--accent-purple);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

/* Rating Stars */
.rating {
    color: #fbbf24;
    text-shadow: 0 0 10px currentColor;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--accent-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-top: none;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.search-suggestion {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.search-suggestion:hover {
    background: rgba(0, 212, 255, 0.1);
    color: var(--text-primary);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 3rem;
        line-height: 1.1;
        padding: 0 1rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        padding: 0 1rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 2rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .nav-glass .max-w-7xl {
        padding: 0 1rem;
    }

    .search-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Modal Optimization */
    .modal-card {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    .modal-content {
        padding: 1.5rem;
    }

    /* Product Grid */
    .products-section .grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    /* Category Filter */
    #categoryFilter {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
        padding: 0 1rem;
    }

    #categoryFilter .filter-btn {
        width: 100%;
        justify-content: center;
        min-height: 48px;
    }

    /* Touch-friendly buttons */
    .btn-primary,
    .btn-secondary {
        min-height: 48px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* Form inputs */
    .modal-input {
        min-height: 48px;
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem 1rem;
    }

    /* Purchase history cards */
    .purchase-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .purchase-card .flex {
        flex-direction: column;
        gap: 1rem;
    }

    .purchase-card img {
        width: 100%;
        max-width: 120px;
        height: auto;
        align-self: center;
    }

    /* Review cards */
    .review-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .review-card .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 640px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        margin-bottom: 1rem;
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }

    .floating-particles {
        display: none; /* Hide particles on mobile for performance */
    }

    /* Single column product grid */
    .products-section .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }

    /* Larger tap targets for mobile */
    .star-btn {
        font-size: 2.5rem;
        padding: 0.5rem;
        min-width: 52px;
        min-height: 52px;
    }

    /* Mobile-optimized modals */
    .modal-card {
        margin: 0.25rem;
        max-width: calc(100vw - 0.5rem);
        border-radius: 1rem;
    }

    /* Navigation improvements */
    .nav-glass .flex {
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: space-between;
    }

    /* User menu dropdown */
    .user-menu {
        position: fixed;
        top: 70px;
        right: 0.5rem;
        left: 0.5rem;
        width: auto;
    }

    /* Cart improvements */
    .cart-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .cart-item img {
        width: 100%;
        max-width: 150px;
        align-self: center;
    }

    /* Checkout form */
    .checkout-form .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.75rem;
        line-height: 1.3;
    }

    /* Extra touch-friendly sizing */
    .btn-primary,
    .btn-secondary,
    .filter-btn {
        min-height: 52px;
        font-size: 1rem;
    }

    /* Form improvements */
    .modal-input,
    textarea {
        min-height: 52px;
        font-size: 16px;
        padding: 1rem;
    }

    /* Product card improvements */
    .product-card {
        border-radius: 1rem;
        margin-bottom: 1rem;
    }

    .product-info {
        padding: 1rem;
    }

    /* Review form improvements */
    .rating-input {
        justify-content: center;
        gap: 0.5rem;
    }

    .star-btn {
        font-size: 2rem;
        min-width: 48px;
        min-height: 48px;
    }

    /* Purchase history improvements */
    .purchase-card .flex {
        flex-direction: column;
        text-align: center;
    }

    .purchase-card .space-x-2 {
        flex-direction: column;
        gap: 0.5rem;
    }

    .purchase-card .space-x-2 > * {
        margin: 0;
    }
}

@media (max-width: 320px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .modal-content {
        padding: 1rem;
    }

    .product-info {
        padding: 0.75rem;
    }

    /* Very small screen optimizations */
    .btn-primary,
    .btn-secondary {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .modal-input {
        padding: 0.75rem;
    }

    /* Compact purchase cards */
    .purchase-card {
        padding: 0.75rem;
    }

    /* Compact review cards */
    .review-card {
        padding: 0.75rem;
    }
}

/* Mobile Touch Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Touch device optimizations */
    .product-card:hover,
    .feature-card:hover,
    .category-card:hover {
        transform: none; /* Disable hover transforms on touch devices */
    }

    .product-card:active,
    .feature-card:active,
    .category-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Touch-friendly buttons */
    .btn-primary:active,
    .btn-secondary:active,
    .filter-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Star rating touch feedback */
    .star-btn:active {
        transform: scale(1.1);
        transition: transform 0.1s ease;
    }

    /* Remove hover states that don't work on touch */
    .nav-link:hover,
    .user-menu-btn:hover {
        background-color: transparent;
    }

    /* Touch-specific focus styles */
    .modal-input:focus,
    textarea:focus {
        outline: 2px solid var(--accent-cyan);
        outline-offset: 2px;
    }
}

/* Swipe gesture support */
.swipe-container {
    touch-action: pan-x;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.swipe-container::-webkit-scrollbar {
    display: none;
}

/* Mobile Touch Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Touch device optimizations */
    .product-card:hover,
    .feature-card:hover,
    .category-card:hover {
        transform: none; /* Disable hover transforms on touch devices */
    }

    .product-card:active,
    .feature-card:active,
    .category-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* Touch-friendly buttons */
    .btn-primary:active,
    .btn-secondary:active,
    .filter-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Star rating touch feedback */
    .star-btn:active {
        transform: scale(1.1);
        transition: transform 0.1s ease;
    }
}

/* Swipe gesture support */
.swipe-container {
    touch-action: pan-x;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.swipe-container::-webkit-scrollbar {
    display: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: rgba(255, 255, 255, 0.3);
        --text-secondary: rgba(255, 255, 255, 0.9);
    }
}

/* Fix transparency and performance issues */
.category-card,
.product-card,
.feature-card {
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
}

.category-card:not(:hover),
.product-card:not(:hover),
.feature-card:not(:hover) {
    will-change: auto;
}

/* Ensure proper stacking context */
.hero-section {
    isolation: isolate;
}

.nav-glass {
    isolation: isolate;
    z-index: 1000;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-particles,
    .particle {
        display: none;
    }

    .hero-content {
        transform: none !important;
    }
}

/* Print styles */
@media print {
    .nav-glass,
    .floating-particles,
    .btn-hero-primary,
    .btn-hero-secondary {
        display: none;
    }

    body {
        background: white;
        color: black;
    }
}
