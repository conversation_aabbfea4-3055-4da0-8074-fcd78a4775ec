-- =====================================================
-- OBU MARKETPLACE - COMPLETE SAMPLE DATA
-- =====================================================
-- Run this script AFTER creating schema and RLS policies

-- =====================================================
-- 1. INSERT CATEGORIES
-- =====================================================
INSERT INTO public.categories (name, icon, gradient, description, is_active) VALUES
('Gaming', 'fas fa-gamepad', 'from-cyan-400 to-blue-500', 'Games, accounts, and in-game items', true),
('Digital Cards', 'fas fa-credit-card', 'from-green-400 to-emerald-500', 'Gift cards and digital vouchers', true),
('Streaming', 'fas fa-play-circle', 'from-purple-400 to-pink-500', 'Entertainment and streaming services', true),
('Software', 'fas fa-laptop-code', 'from-indigo-400 to-blue-500', 'Software and digital tools', true),
('Mobile Gaming', 'fas fa-mobile-alt', 'from-pink-400 to-red-500', 'Mobile games and in-app purchases', true);

-- =====================================================
-- 2. INSERT PRODUCTS
-- =====================================================
INSERT INTO public.products (
    title, description, category_id, category_name, price, original_price, 
    image_url, store_name, instant_delivery, stock, tags, featured, download_url, is_active
) VALUES 
(
    'Roblox 1000 Robux',
    'Get 1000 Robux instantly delivered to your account. Perfect for purchasing premium items, game passes, and avatar accessories. Instant delivery within 5 minutes of purchase.',
    1, 'Gaming', 12.99, 15.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Gaming Store', true, 50,
    ARRAY['roblox', 'robux', 'gaming', 'instant'],
    true,
    'https://example.com/download/roblox-code',
    true
),
(
    'Steam Wallet $50',
    'Steam Wallet Gift Card worth $50 USD. Can be used to purchase games, DLC, and in-game items on Steam. Valid worldwide and never expires.',
    2, 'Digital Cards', 45.00, 50.00,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Digital Store', true, 30,
    ARRAY['steam', 'gift card', 'gaming'], 
    true,
    'https://example.com/download/steam-wallet-code',
    true
),
(
    'Mobile Legends 2000 Diamonds',
    'Mobile Legends Bang Bang 2000 Diamonds top-up. Get premium skins, heroes, and battle passes. Fast and secure delivery to your account.',
    5, 'Mobile Gaming', 28.50, 32.00,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Mobile Gaming', true, 25,
    ARRAY['mobile legends', 'diamonds', 'mobile gaming'], 
    true,
    'https://example.com/download/ml-diamonds-code',
    true
),
(
    'Valorant VP 2000',
    'Valorant Points for purchasing weapon skins, battle passes, and premium content. Compatible with all regions and instant delivery.',
    1, 'Gaming', 19.99, 24.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Gaming Store', true, 40,
    ARRAY['valorant', 'vp', 'riot games'], 
    false,
    'https://example.com/download/valorant-vp-code',
    true
),
(
    'Netflix Premium 1 Month',
    'Netflix Premium subscription for 1 month. Enjoy 4K streaming on multiple devices with no ads. Works in all supported countries.',
    3, 'Streaming', 15.99, 17.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Digital Store', true, 100,
    ARRAY['netflix', 'streaming', 'premium'], 
    false,
    'https://example.com/download/netflix-account',
    true
),
(
    'Google Play Gift Card $25',
    'Google Play Gift Card worth $25 USD. Use for apps, games, movies, books, and more on Google Play Store.',
    2, 'Digital Cards', 22.50, 25.00,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Digital Store', true, 75,
    ARRAY['google play', 'gift card', 'android'], 
    true,
    'https://example.com/download/googleplay-code',
    true
),
(
    'Spotify Premium 3 Months',
    'Spotify Premium subscription for 3 months. Ad-free music, offline downloads, and unlimited skips.',
    3, 'Streaming', 29.99, 35.97,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Digital Store', true, 60,
    ARRAY['spotify', 'music', 'streaming', 'premium'], 
    true,
    'https://example.com/download/spotify-account',
    true
),
(
    'Free Fire 2000 Diamonds',
    'Free Fire 2000 Diamonds top-up. Get exclusive skins, characters, and weapon upgrades. Instant delivery guaranteed.',
    5, 'Mobile Gaming', 24.99, 29.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Mobile Gaming', true, 35,
    ARRAY['free fire', 'diamonds', 'mobile gaming', 'battle royale'], 
    false,
    'https://example.com/download/freefire-diamonds',
    true
),
(
    'Fortnite V-Bucks 2800',
    'Get 2800 V-Bucks for Fortnite. Purchase Battle Passes, skins, emotes, and more exclusive content.',
    1, 'Gaming', 19.99, 24.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Gaming Store', true, 75,
    ARRAY['fortnite', 'v-bucks', 'battle royale', 'epic games'], 
    true,
    'https://example.com/download/fortnite-vbucks',
    true
),
(
    'PlayStation Store $25',
    'PlayStation Store Gift Card worth $25. Perfect for games, DLC, and PlayStation Plus subscriptions.',
    2, 'Digital Cards', 22.50, 25.00,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Digital Store', true, 40,
    ARRAY['playstation', 'ps5', 'ps4', 'gift card'], 
    false,
    'https://example.com/download/ps-store-card',
    true
),
(
    'Minecraft Java Edition',
    'Minecraft Java Edition account with full access. Build, explore, and create in the ultimate sandbox game.',
    1, 'Gaming', 26.95, 29.95,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Gaming Store', true, 25,
    ARRAY['minecraft', 'java', 'sandbox', 'mojang'], 
    true,
    'https://example.com/download/minecraft-account',
    true
),
(
    'Adobe Creative Cloud 1 Month',
    'Adobe Creative Cloud subscription for 1 month. Access to Photoshop, Illustrator, Premiere Pro, and more.',
    4, 'Software', 52.99, 59.99,
    'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop',
    'Obu Software Store', true, 15,
    ARRAY['adobe', 'creative', 'photoshop', 'design'], 
    false,
    'https://example.com/download/adobe-cc',
    true
);

-- =====================================================
-- 3. UPDATE CATEGORY PRODUCT COUNTS
-- =====================================================
UPDATE public.categories SET product_count = (
    SELECT COUNT(*) FROM public.products WHERE category_id = categories.id AND is_active = true
);

-- =====================================================
-- 4. SET INITIAL PRODUCT RATINGS (Simulating Reviews)
-- =====================================================
UPDATE public.products SET rating = 4.8, review_count = 245 WHERE title = 'Roblox 1000 Robux';
UPDATE public.products SET rating = 4.6, review_count = 156 WHERE title = 'Steam Wallet $50';
UPDATE public.products SET rating = 4.7, review_count = 189 WHERE title = 'Mobile Legends 2000 Diamonds';
UPDATE public.products SET rating = 4.5, review_count = 134 WHERE title = 'Valorant VP 2000';
UPDATE public.products SET rating = 4.8, review_count = 203 WHERE title = 'Netflix Premium 1 Month';
UPDATE public.products SET rating = 4.9, review_count = 178 WHERE title = 'Google Play Gift Card $25';
UPDATE public.products SET rating = 4.7, review_count = 167 WHERE title = 'Spotify Premium 3 Months';
UPDATE public.products SET rating = 4.6, review_count = 145 WHERE title = 'Free Fire 2000 Diamonds';
UPDATE public.products SET rating = 4.8, review_count = 234 WHERE title = 'Fortnite V-Bucks 2800';
UPDATE public.products SET rating = 4.5, review_count = 123 WHERE title = 'PlayStation Store $25';
UPDATE public.products SET rating = 4.9, review_count = 289 WHERE title = 'Minecraft Java Edition';
UPDATE public.products SET rating = 4.4, review_count = 98 WHERE title = 'Adobe Creative Cloud 1 Month';

-- =====================================================
-- 5. SAMPLE ADMIN USER (Optional)
-- =====================================================
-- Note: This user will need to be created through Supabase Auth first
-- Then you can insert the profile data:

-- INSERT INTO public.users (id, name, email, account_type, auth_provider, email_verified) VALUES
-- ('YOUR_ADMIN_USER_UUID', 'Admin User', '<EMAIL>', 'buyer', 'email', true);

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================
-- Run these to verify data was inserted correctly:

-- Check categories
-- SELECT * FROM public.categories ORDER BY name;

-- Check products
-- SELECT title, category_name, price, rating, review_count, is_active FROM public.products ORDER BY category_name, title;

-- Check category counts
-- SELECT c.name, c.product_count, COUNT(p.id) as actual_count 
-- FROM public.categories c 
-- LEFT JOIN public.products p ON c.id = p.category_id AND p.is_active = true 
-- GROUP BY c.id, c.name, c.product_count 
-- ORDER BY c.name;

-- =====================================================
-- SAMPLE DATA INSERTION COMPLETE
-- =====================================================
-- Your Obu Marketplace database is now ready with sample data!
-- 
-- Next steps:
-- 1. Configure Supabase Auth providers (Google, Apple)
-- 2. Update your frontend with Supabase URL and API key
-- 3. Test the application with real user registration
-- 4. Add more products as needed
