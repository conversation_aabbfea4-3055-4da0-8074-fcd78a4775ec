-- Row Level Security (RLS) Policies for Obu Marketplace
-- Run this after creating the schema and sample data

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Users table policies
-- Users can read their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (during signup)
CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Categories table policies
-- Categories are publicly readable
CREATE POLICY "Categories are publicly readable" ON public.categories
    FOR SELECT USING (true);

-- Only authenticated users can see categories (optional, remove if you want public access)
-- CREATE POLICY "Authenticated users can view categories" ON public.categories
--     FOR SELECT USING (auth.role() = 'authenticated');

-- Products table policies
-- Products are publicly readable
CREATE POLICY "Products are publicly readable" ON public.products
    FOR SELECT USING (true);

-- Only admins can modify products (you'll need to implement admin role)
-- CREATE POLICY "Only admins can modify products" ON public.products
--     FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Purchases table policies
-- Users can only see their own purchases
CREATE POLICY "Users can view own purchases" ON public.purchases
    FOR SELECT USING (auth.uid() = buyer_id);

-- Users can create their own purchases
CREATE POLICY "Users can create own purchases" ON public.purchases
    FOR INSERT WITH CHECK (auth.uid() = buyer_id);

-- Users can update their own purchases (for download tracking)
CREATE POLICY "Users can update own purchases" ON public.purchases
    FOR UPDATE USING (auth.uid() = buyer_id);

-- Reviews table policies
-- Reviews are publicly readable (so anyone can see product reviews)
CREATE POLICY "Reviews are publicly readable" ON public.reviews
    FOR SELECT USING (true);

-- Users can only create reviews for their own purchases
CREATE POLICY "Users can create reviews for own purchases" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.purchases 
            WHERE id = purchase_id 
            AND buyer_id = auth.uid() 
            AND status = 'completed'
        )
    );

-- Users can update helpful votes on reviews (for voting system)
CREATE POLICY "Users can vote on reviews" ON public.reviews
    FOR UPDATE USING (true)
    WITH CHECK (true);

-- Cart items table policies
-- Users can only see their own cart items
CREATE POLICY "Users can view own cart" ON public.cart_items
    FOR SELECT USING (auth.uid() = user_id);

-- Users can manage their own cart items
CREATE POLICY "Users can manage own cart" ON public.cart_items
    FOR ALL USING (auth.uid() = user_id);

-- User sessions table policies
-- Users can only see their own sessions
CREATE POLICY "Users can view own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can manage their own sessions
CREATE POLICY "Users can manage own sessions" ON public.user_sessions
    FOR ALL USING (auth.uid() = user_id);

-- Additional security functions
-- Function to check if user has purchased a product
CREATE OR REPLACE FUNCTION public.user_has_purchased_product(product_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.purchases 
        WHERE buyer_id = auth.uid() 
        AND product_id = $1 
        AND status = 'completed'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can review a product
CREATE OR REPLACE FUNCTION public.can_user_review_product(product_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
    -- Must have purchased the product
    IF NOT public.user_has_purchased_product(product_id) THEN
        RETURN FALSE;
    END IF;
    
    -- Must not have already reviewed
    IF EXISTS (
        SELECT 1 FROM public.reviews 
        WHERE user_id = auth.uid() 
        AND product_id = $1
    ) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enhanced review policy using the function
DROP POLICY IF EXISTS "Users can create reviews for own purchases" ON public.reviews;
CREATE POLICY "Users can create reviews for own purchases" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        public.can_user_review_product(product_id)
    );

-- Prevent users from reviewing the same product twice
CREATE POLICY "Prevent duplicate reviews" ON public.reviews
    FOR INSERT WITH CHECK (
        NOT EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE user_id = auth.uid() 
            AND product_id = NEW.product_id
        )
    );

-- Grant necessary permissions
-- Grant usage on sequences
GRANT USAGE ON SEQUENCE public.categories_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE public.products_id_seq TO authenticated;

-- Grant permissions on tables
GRANT SELECT ON public.categories TO anon, authenticated;
GRANT SELECT ON public.products TO anon, authenticated;
GRANT ALL ON public.users TO authenticated;
GRANT ALL ON public.purchases TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.reviews TO authenticated;
GRANT ALL ON public.cart_items TO authenticated;
GRANT ALL ON public.user_sessions TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.user_has_purchased_product(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_user_review_product(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_product_rating() TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_category_count() TO authenticated;

-- Create indexes for RLS performance
CREATE INDEX IF NOT EXISTS idx_purchases_buyer_id ON public.purchases(buyer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON public.reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_user_id ON public.cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);

-- Optional: Create admin role check function
-- You can extend this to implement admin functionality
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user has admin role in their JWT claims
    -- This is just an example - implement based on your admin system
    RETURN (auth.jwt() ->> 'role' = 'admin') OR 
           (auth.jwt() ->> 'user_role' = 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Example admin policies (uncomment if you implement admin system)
-- CREATE POLICY "Admins can manage all data" ON public.products
--     FOR ALL USING (public.is_admin());

-- CREATE POLICY "Admins can view all purchases" ON public.purchases
--     FOR SELECT USING (public.is_admin());

-- CREATE POLICY "Admins can manage categories" ON public.categories
--     FOR ALL USING (public.is_admin());
