-- Sample Data for Obu Marketplace
-- Run this after the schema is created

-- Insert Categories
INSERT INTO categories (name, icon, gradient, description) VALUES
('Gaming', 'fas fa-gamepad', 'from-cyan-400 to-blue-500', 'Games, accounts, and in-game items'),
('Digital Cards', 'fas fa-credit-card', 'from-green-400 to-emerald-500', 'Gift cards and digital vouchers'),
('Streaming', 'fas fa-play-circle', 'from-purple-400 to-pink-500', 'Entertainment and streaming services'),
('Software', 'fas fa-laptop-code', 'from-indigo-400 to-blue-500', 'Software and digital tools');

-- Insert Products
INSERT INTO products (title, description, category_id, category_name, price, original_price, image_url, store_name, instant_delivery, stock, tags, featured, download_url) VALUES
('Roblox 1000 Robux', 'Get 1000 Robux instantly delivered to your account. Perfect for purchasing premium items, game passes, and avatar accessories.', 1, 'Gaming', 12.99, 15.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 50, ARRAY['roblox', 'robux', 'gaming', 'instant'], true, 'https://example.com/download/roblox-code'),

('Steam Wallet $50', 'Steam Wallet Gift Card worth $50 USD. Can be used to purchase games, DLC, and in-game items on Steam.', 2, 'Digital Cards', 45.00, 50.00, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Digital Store', true, 30, ARRAY['steam', 'gift card', 'gaming'], true, 'https://example.com/download/steam-wallet-code'),

('Mobile Legends 2000 Diamonds', 'Mobile Legends Bang Bang 2000 Diamonds top-up. Get premium skins, heroes, and battle passes.', 1, 'Gaming', 28.50, 32.00, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Mobile Gaming', true, 25, ARRAY['mobile legends', 'diamonds', 'mobile gaming'], true, 'https://example.com/download/ml-diamonds-code'),

('Valorant VP 2000', 'Valorant Points for purchasing weapon skins, battle passes, and premium content.', 1, 'Gaming', 19.99, 24.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 40, ARRAY['valorant', 'vp', 'riot games'], false, 'https://example.com/download/valorant-vp-code'),

('Netflix Premium 1 Month', 'Netflix Premium subscription for 1 month. Enjoy 4K streaming on multiple devices.', 3, 'Streaming', 15.99, 17.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Digital Store', true, 100, ARRAY['netflix', 'streaming', 'premium'], false, 'https://example.com/download/netflix-account'),

('Fortnite V-Bucks 2800', 'Get 2800 V-Bucks for Fortnite. Purchase Battle Passes, skins, emotes, and more exclusive content.', 1, 'Gaming', 19.99, 24.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 75, ARRAY['fortnite', 'v-bucks', 'battle royale', 'epic games'], true, 'https://example.com/download/fortnite-vbucks'),

('PlayStation Store $25', 'PlayStation Store Gift Card worth $25. Perfect for games, DLC, and PlayStation Plus subscriptions.', 2, 'Digital Cards', 22.50, 25.00, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Digital Store', true, 40, ARRAY['playstation', 'ps5', 'ps4', 'gift card'], false, 'https://example.com/download/ps-store-card'),

('Spotify Premium 3 Months', 'Spotify Premium subscription for 3 months. Ad-free music, offline downloads, and unlimited skips.', 3, 'Streaming', 29.99, 35.97, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Digital Store', true, 60, ARRAY['spotify', 'music', 'streaming', 'premium'], false, 'https://example.com/download/spotify-premium'),

('Minecraft Java Edition', 'Minecraft Java Edition account with full access. Build, explore, and create in the ultimate sandbox game.', 1, 'Gaming', 26.95, 29.95, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 25, ARRAY['minecraft', 'java', 'sandbox', 'mojang'], true, 'https://example.com/download/minecraft-account'),

('Adobe Creative Cloud 1 Month', 'Adobe Creative Cloud subscription for 1 month. Access to Photoshop, Illustrator, Premiere Pro, and more.', 4, 'Software', 52.99, 59.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Software Store', true, 15, ARRAY['adobe', 'creative', 'photoshop', 'design'], false, 'https://example.com/download/adobe-cc'),

('League of Legends 3500 RP', 'League of Legends Riot Points for purchasing champions, skins, and other in-game content.', 1, 'Gaming', 24.99, 27.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 55, ARRAY['league of legends', 'riot points', 'lol', 'moba'], false, 'https://example.com/download/lol-rp'),

('Xbox Game Pass Ultimate 1 Month', 'Xbox Game Pass Ultimate for 1 month. Access to hundreds of games on Xbox and PC, plus Xbox Live Gold.', 1, 'Gaming', 14.99, 16.99, 'https://images.unsplash.com/photo-*************-b2417e99c4e3?w=400&h=300&fit=crop', 'Obu Gaming Store', true, 80, ARRAY['xbox', 'game pass', 'microsoft', 'subscription'], true, 'https://example.com/download/xbox-gamepass');

-- Create demo users (these will be created through Supabase Auth, but we can prepare the profile data)
-- Note: In a real app, users would be created through Supabase Auth signup

-- Sample Reviews (will be added after users are created)
-- These are examples of what the review data would look like

-- Update category counts based on products
UPDATE categories SET product_count = (
    SELECT COUNT(*) FROM products WHERE category_id = categories.id
);

-- Set some initial ratings for products (simulating existing reviews)
UPDATE products SET rating = 4.8, review_count = 245 WHERE title = 'Roblox 1000 Robux';
UPDATE products SET rating = 4.6, review_count = 156 WHERE title = 'Steam Wallet $50';
UPDATE products SET rating = 4.7, review_count = 189 WHERE title = 'Mobile Legends 2000 Diamonds';
UPDATE products SET rating = 4.5, review_count = 134 WHERE title = 'Valorant VP 2000';
UPDATE products SET rating = 4.8, review_count = 203 WHERE title = 'Netflix Premium 1 Month';
UPDATE products SET rating = 4.7, review_count = 189 WHERE title = 'Fortnite V-Bucks 2800';
UPDATE products SET rating = 4.9, review_count = 156 WHERE title = 'PlayStation Store $25';
UPDATE products SET rating = 4.8, review_count = 203 WHERE title = 'Spotify Premium 3 Months';
UPDATE products SET rating = 4.9, review_count = 312 WHERE title = 'Minecraft Java Edition';
UPDATE products SET rating = 4.6, review_count = 89 WHERE title = 'Adobe Creative Cloud 1 Month';
UPDATE products SET rating = 4.8, review_count = 267 WHERE title = 'League of Legends 3500 RP';
UPDATE products SET rating = 4.9, review_count = 445 WHERE title = 'Xbox Game Pass Ultimate 1 Month';
