<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0284c7;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        #output {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Authentication System Test Page</h1>
    
    <div class="test-section">
        <h2>🧪 Quick Tests</h2>
        <button onclick="runQuickDiagnostics()">Run Quick Diagnostics</button>
        <button onclick="createTestAccounts()">Create Test Accounts</button>
        <button onclick="testAdminLogin()">Test Admin Login</button>
        <button onclick="testUserLogin()">Test User Login</button>
        <button onclick="testLogout()">Test Logout</button>
    </div>

    <div class="test-section">
        <h2>🔍 Manual Tests</h2>
        <div>
            <input type="email" id="testEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="testPassword" placeholder="Password" value="try465">
            <button onclick="testManualLogin()">Manual Login Test</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🛠️ System Tools</h2>
        <button onclick="clearAllData()">Clear All Data</button>
        <button onclick="resetAuthSystem()">Reset Auth System</button>
        <button onclick="showSystemStatus()">Show System Status</button>
        <button onclick="exportData()">Export Data</button>
    </div>

    <div class="test-section">
        <h2>📊 Test Output</h2>
        <div id="output"></div>
        <button onclick="clearOutput()">Clear Output</button>
    </div>

    <!-- Include all necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/auth-fix.js"></script>

    <script>
        // Test functions
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        async function runQuickDiagnostics() {
            log('🔍 Running Quick Diagnostics...', 'info');
            
            try {
                // Check if systems are loaded
                log(`Storage: ${typeof storage !== 'undefined' ? '✅' : '❌'}`);
                log(`AuthManager: ${typeof authManager !== 'undefined' ? '✅' : '❌'}`);
                log(`Utils: ${typeof Utils !== 'undefined' ? '✅' : '❌'}`);
                log(`Components: ${typeof Components !== 'undefined' ? '✅' : '❌'}`);

                if (typeof storage !== 'undefined') {
                    // Check test accounts
                    const adminUser = storage.getUserByEmail('<EMAIL>');
                    const regularUser = storage.getUserByEmail('<EMAIL>');
                    
                    log(`Admin account: ${adminUser ? '✅' : '❌'}`);
                    log(`User account: ${regularUser ? '✅' : '❌'}`);

                    if (adminUser) {
                        const passwordTest = storage.verifyPassword('try465', adminUser.passwordHash);
                        log(`Admin password verification: ${passwordTest ? '✅' : '❌'}`);
                    }

                    if (regularUser) {
                        const passwordTest = storage.verifyPassword('try465', regularUser.passwordHash);
                        log(`User password verification: ${passwordTest ? '✅' : '❌'}`);
                    }
                }

                if (typeof authManager !== 'undefined') {
                    log(`Currently logged in: ${authManager.isLoggedIn() ? '✅' : '❌'}`);
                    log(`Current user: ${authManager.currentUser?.name || 'None'}`);
                }

                log('✅ Diagnostics complete', 'success');

            } catch (error) {
                log(`❌ Diagnostics error: ${error.message}`, 'error');
            }
        }

        async function createTestAccounts() {
            log('👥 Creating test accounts...', 'info');
            
            try {
                if (typeof storage === 'undefined') {
                    log('❌ Storage not available', 'error');
                    return;
                }

                const accounts = [
                    { name: 'Obu Admin', email: '<EMAIL>', password: 'try465' },
                    { name: 'Obu User', email: '<EMAIL>', password: 'try465' }
                ];

                accounts.forEach(account => {
                    const existing = storage.getUserByEmail(account.email);
                    if (!existing) {
                        const user = storage.addUser(account);
                        log(`✅ Created: ${account.email} (ID: ${user.id})`, 'success');
                    } else {
                        log(`✅ Already exists: ${account.email}`, 'info');
                    }
                });

                log('✅ Test accounts ready', 'success');

            } catch (error) {
                log(`❌ Account creation error: ${error.message}`, 'error');
            }
        }

        async function testAdminLogin() {
            log('🔐 Testing admin login...', 'info');
            return await testLoginWithCredentials('<EMAIL>', 'try465');
        }

        async function testUserLogin() {
            log('🔐 Testing user login...', 'info');
            return await testLoginWithCredentials('<EMAIL>', 'try465');
        }

        async function testManualLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            log(`🔐 Testing manual login for: ${email}`, 'info');
            return await testLoginWithCredentials(email, password);
        }

        async function testLoginWithCredentials(email, password) {
            try {
                if (typeof authManager === 'undefined' || typeof storage === 'undefined') {
                    log('❌ Auth system not available', 'error');
                    return false;
                }

                // Logout first
                if (authManager.isLoggedIn()) {
                    authManager.logout();
                    log('🚪 Logged out previous user', 'info');
                }

                // Check if user exists
                const user = storage.getUserByEmail(email);
                if (!user) {
                    log(`❌ User not found: ${email}`, 'error');
                    return false;
                }

                // Test password verification
                const isValidPassword = storage.verifyPassword(password, user.passwordHash);
                log(`Password verification: ${isValidPassword ? '✅' : '❌'}`);

                if (!isValidPassword) {
                    log('❌ Invalid password', 'error');
                    return false;
                }

                // Perform login
                authManager.currentUser = user;
                storage.setCurrentUser(user);
                authManager.updateUI();

                log(`✅ Login successful: ${user.name}`, 'success');
                return true;

            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testLogout() {
            log('🚪 Testing logout...', 'info');
            
            try {
                if (typeof authManager === 'undefined') {
                    log('❌ AuthManager not available', 'error');
                    return;
                }

                if (!authManager.isLoggedIn()) {
                    log('ℹ️ No user currently logged in', 'warning');
                    return;
                }

                const userName = authManager.currentUser?.name;
                authManager.logout();
                
                log(`✅ Logout successful for: ${userName}`, 'success');

            } catch (error) {
                log(`❌ Logout error: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            log('🗑️ Clearing all data...', 'warning');
            
            try {
                if (typeof storage !== 'undefined') {
                    storage.clearAllData();
                    log('✅ All data cleared', 'success');
                } else {
                    log('❌ Storage not available', 'error');
                }
            } catch (error) {
                log(`❌ Clear data error: ${error.message}`, 'error');
            }
        }

        function resetAuthSystem() {
            log('🔄 Resetting auth system...', 'info');
            
            try {
                if (typeof storage !== 'undefined') {
                    storage.clearCurrentUser();
                }
                
                if (typeof authManager !== 'undefined') {
                    authManager.currentUser = null;
                    authManager.updateUI();
                }
                
                log('✅ Auth system reset', 'success');
                
            } catch (error) {
                log(`❌ Reset error: ${error.message}`, 'error');
            }
        }

        function showSystemStatus() {
            log('📊 System Status:', 'info');
            log(`  Storage: ${typeof storage !== 'undefined' ? 'Available' : 'Not Available'}`);
            log(`  AuthManager: ${typeof authManager !== 'undefined' ? 'Available' : 'Not Available'}`);
            log(`  Current User: ${authManager?.currentUser?.name || 'None'}`);
            log(`  Logged In: ${authManager?.isLoggedIn() || false}`);
            
            if (typeof storage !== 'undefined') {
                const users = storage.getUsers();
                log(`  Total Users: ${users.length}`);
                users.forEach(user => {
                    log(`    - ${user.name} (${user.email})`);
                });
            }
        }

        function exportData() {
            log('📤 Exporting data...', 'info');
            
            try {
                if (typeof storage !== 'undefined') {
                    const data = storage.exportData();
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'obu-marketplace-data.json';
                    a.click();
                    URL.revokeObjectURL(url);
                    log('✅ Data exported', 'success');
                } else {
                    log('❌ Storage not available', 'error');
                }
            } catch (error) {
                log(`❌ Export error: ${error.message}`, 'error');
            }
        }

        // Initialize auth manager
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof authManager !== 'undefined') {
                authManager.init();
            }
            log('🚀 Test page loaded', 'success');
            log('💡 Use the buttons above to test authentication', 'info');
        });
    </script>
</body>
</html>
