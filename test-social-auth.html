<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Auth Test - Obu Marketplace</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        button {
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0284c7;
            transform: translateY(-1px);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        #output {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
            font-size: 12px;
        }
        .status-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status-enabled { border-left: 4px solid #10b981; }
        .status-disabled { border-left: 4px solid #ef4444; }
        .status-testing { border-left: 4px solid #f59e0b; }
    </style>
</head>
<body>
    <h1>🔐 Social Authentication Test - Obu Marketplace</h1>
    
    <div class="test-section">
        <h2>📊 Current Status</h2>
        <div id="statusCards"></div>
        <button onclick="refreshStatus()">Refresh Status</button>
    </div>

    <div class="test-section">
        <h2>🧪 Provider Tests</h2>
        <div class="test-grid">
            <button onclick="testGoogleProvider()">Test Google Provider</button>
            <button onclick="testAppleProvider()">Test Apple Provider</button>
            <button onclick="testAllProviders()">Test All Providers</button>
            <button onclick="retryFailedProviders()">Retry Failed Providers</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication Tests</h2>
        <div class="test-grid">
            <button onclick="testGoogleLogin()">Test Google Login</button>
            <button onclick="testAppleLogin()">Test Apple Login</button>
            <button onclick="testEmailLogin()">Test Email Login</button>
            <button onclick="showLoginModal()">Show Login Modal</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Configuration Tools</h2>
        <div class="test-grid">
            <button onclick="showSupabaseConfig()">Show Supabase Config</button>
            <button onclick="showConfigHelp()">Show Setup Guide</button>
            <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
            <button onclick="checkAuthState()">Check Auth State</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🚨 Error Simulation</h2>
        <div class="test-grid">
            <button onclick="simulateProviderError()">Simulate Provider Error</button>
            <button onclick="simulateRedirectError()">Simulate Redirect Error</button>
            <button onclick="simulateConfigError()">Simulate Config Error</button>
            <button onclick="testErrorHandling()">Test Error Handling</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Test Output</h2>
        <div id="output"></div>
        <button onclick="clearOutput()">Clear Output</button>
        <button onclick="exportResults()">Export Results</button>
    </div>

    <!-- Include all necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/supabase-integration.js"></script>
    <script src="js/supabase-auth-fix.js"></script>
    <script src="js/social-auth-handler.js"></script>
    <script src="js/managers/AuthManager.js"></script>

    <script>
        let testResults = {};

        // Utility functions
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : type === 'info' ? 'info' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // Status display
        function refreshStatus() {
            log('🔄 Refreshing status...', 'info');
            
            const statusCards = document.getElementById('statusCards');
            statusCards.innerHTML = '';

            // Check if systems are available
            const systems = {
                supabaseIntegration: typeof supabaseIntegration !== 'undefined',
                socialAuthHandler: typeof socialAuthHandler !== 'undefined',
                authManager: typeof authManager !== 'undefined'
            };

            // System status cards
            Object.entries(systems).forEach(([system, available]) => {
                const card = document.createElement('div');
                card.className = `status-card ${available ? 'status-enabled' : 'status-disabled'}`;
                card.innerHTML = `
                    <h4>${system}</h4>
                    <p>Status: ${available ? '✅ Available' : '❌ Not Available'}</p>
                `;
                statusCards.appendChild(card);
            });

            // Provider status
            if (typeof socialAuthHandler !== 'undefined') {
                const providerStatus = socialAuthHandler.getAllProviderStatuses();
                
                Object.entries(providerStatus).forEach(([provider, status]) => {
                    if (provider === 'fallbackMode' || provider === 'supabaseAvailable') return;
                    
                    const card = document.createElement('div');
                    card.className = `status-card ${status.enabled ? 'status-enabled' : status.tested ? 'status-disabled' : 'status-testing'}`;
                    card.innerHTML = `
                        <h4>${provider.charAt(0).toUpperCase() + provider.slice(1)} Provider</h4>
                        <p>Status: ${status.enabled ? '✅ Enabled' : status.tested ? '❌ Disabled' : '⏳ Testing'}</p>
                        ${status.error ? `<p class="error">Error: ${status.error}</p>` : ''}
                        ${status.userMessage ? `<p class="warning">Message: ${status.userMessage}</p>` : ''}
                    `;
                    statusCards.appendChild(card);
                });

                // Overall status
                const overallCard = document.createElement('div');
                overallCard.className = `status-card ${providerStatus.fallbackMode ? 'status-disabled' : 'status-enabled'}`;
                overallCard.innerHTML = `
                    <h4>Overall Status</h4>
                    <p>Mode: ${providerStatus.fallbackMode ? '🔄 Fallback Mode' : '✅ Normal Mode'}</p>
                    <p>Supabase: ${providerStatus.supabaseAvailable ? '✅ Connected' : '❌ Not Connected'}</p>
                `;
                statusCards.appendChild(overallCard);
            }

            log('✅ Status refreshed', 'success');
        }

        // Provider tests
        async function testGoogleProvider() {
            log('🧪 Testing Google provider...', 'info');
            
            if (typeof socialAuthHandler === 'undefined') {
                log('❌ Social auth handler not available', 'error');
                return;
            }

            try {
                const result = await socialAuthHandler.retryProvider('google');
                log(`Google provider test result: ${JSON.stringify(result)}`, result.enabled ? 'success' : 'warning');
                testResults.googleProvider = result;
                refreshStatus();
            } catch (error) {
                log(`❌ Google provider test error: ${error.message}`, 'error');
            }
        }

        async function testAppleProvider() {
            log('🧪 Testing Apple provider...', 'info');
            
            if (typeof socialAuthHandler === 'undefined') {
                log('❌ Social auth handler not available', 'error');
                return;
            }

            try {
                const result = await socialAuthHandler.retryProvider('apple');
                log(`Apple provider test result: ${JSON.stringify(result)}`, result.enabled ? 'success' : 'warning');
                testResults.appleProvider = result;
                refreshStatus();
            } catch (error) {
                log(`❌ Apple provider test error: ${error.message}`, 'error');
            }
        }

        async function testAllProviders() {
            log('🧪 Testing all providers...', 'info');
            
            if (typeof socialAuthHandler === 'undefined') {
                log('❌ Social auth handler not available', 'error');
                return;
            }

            try {
                const results = await socialAuthHandler.retryAllProviders();
                log(`All providers test results: ${JSON.stringify(results, null, 2)}`, 'info');
                testResults.allProviders = results;
                refreshStatus();
            } catch (error) {
                log(`❌ All providers test error: ${error.message}`, 'error');
            }
        }

        async function retryFailedProviders() {
            log('🔄 Retrying failed providers...', 'info');
            await testAllProviders();
        }

        // Authentication tests
        async function testGoogleLogin() {
            log('🔐 Testing Google login...', 'info');
            
            if (typeof authManager === 'undefined') {
                log('❌ Auth manager not available', 'error');
                return;
            }

            try {
                const result = await authManager.signInWithGoogle();
                log(`Google login result: ${JSON.stringify(result)}`, result.success ? 'success' : 'warning');
                testResults.googleLogin = result;
            } catch (error) {
                log(`❌ Google login error: ${error.message}`, 'error');
            }
        }

        async function testAppleLogin() {
            log('🔐 Testing Apple login...', 'info');
            
            if (typeof authManager === 'undefined') {
                log('❌ Auth manager not available', 'error');
                return;
            }

            try {
                const result = await authManager.signInWithApple();
                log(`Apple login result: ${JSON.stringify(result)}`, result.success ? 'success' : 'warning');
                testResults.appleLogin = result;
            } catch (error) {
                log(`❌ Apple login error: ${error.message}`, 'error');
            }
        }

        async function testEmailLogin() {
            log('📧 Testing email login...', 'info');
            
            try {
                // Test with admin account
                const result = await authManager.login('<EMAIL>', 'admin123');
                log(`Email login result: ${JSON.stringify(result)}`, result.success ? 'success' : 'warning');
                testResults.emailLogin = result;
            } catch (error) {
                log(`❌ Email login error: ${error.message}`, 'error');
            }
        }

        function showLoginModal() {
            log('📱 Showing login modal...', 'info');
            
            if (typeof authManager !== 'undefined') {
                authManager.showLoginModal();
                log('✅ Login modal displayed', 'success');
            } else {
                log('❌ Auth manager not available', 'error');
            }
        }

        // Configuration tools
        function showSupabaseConfig() {
            log('🔧 Showing Supabase configuration...', 'info');
            
            if (typeof supabaseIntegration !== 'undefined') {
                log(`Supabase URL: ${supabaseIntegration.supabaseUrl}`, 'info');
                log(`Supabase Connected: ${supabaseIntegration.isConnected()}`, 'info');
                log(`Supabase Initialized: ${supabaseIntegration.isInitialized}`, 'info');
            } else {
                log('❌ Supabase integration not available', 'error');
            }
        }

        function showConfigHelp() {
            log('📖 Opening configuration help...', 'info');
            window.open('SUPABASE_SOCIAL_AUTH_SETUP.md', '_blank');
        }

        async function testSupabaseConnection() {
            log('🔗 Testing Supabase connection...', 'info');
            
            if (typeof supabaseIntegration === 'undefined') {
                log('❌ Supabase integration not available', 'error');
                return;
            }

            try {
                const connected = await supabaseIntegration.testConnection();
                log(`Supabase connection: ${connected ? '✅ Connected' : '❌ Failed'}`, connected ? 'success' : 'error');
                testResults.supabaseConnection = connected;
            } catch (error) {
                log(`❌ Supabase connection error: ${error.message}`, 'error');
            }
        }

        function checkAuthState() {
            log('👤 Checking authentication state...', 'info');
            
            if (typeof authManager !== 'undefined') {
                const user = authManager.currentUser;
                log(`Current user: ${user ? user.name + ' (' + user.email + ')' : 'None'}`, user ? 'success' : 'info');
                log(`Is logged in: ${authManager.isLoggedIn()}`, authManager.isLoggedIn() ? 'success' : 'info');
                testResults.authState = { user, isLoggedIn: authManager.isLoggedIn() };
            } else {
                log('❌ Auth manager not available', 'error');
            }
        }

        // Error simulation
        function simulateProviderError() {
            log('🚨 Simulating provider error...', 'warning');
            
            const error = new Error('Unsupported provider: provider is not enabled');
            if (typeof socialAuthHandler !== 'undefined') {
                socialAuthHandler.handleProviderError('google', error);
                log('✅ Provider error simulated', 'success');
            } else {
                log('❌ Social auth handler not available', 'error');
            }
        }

        function simulateRedirectError() {
            log('🚨 Simulating redirect error...', 'warning');
            
            const error = new Error('redirect_uri_mismatch');
            if (typeof socialAuthHandler !== 'undefined') {
                socialAuthHandler.handleProviderError('apple', error);
                log('✅ Redirect error simulated', 'success');
            } else {
                log('❌ Social auth handler not available', 'error');
            }
        }

        function simulateConfigError() {
            log('🚨 Simulating configuration error...', 'warning');
            
            if (typeof socialAuthHandler !== 'undefined') {
                socialAuthHandler.enableFallbackMode('Configuration test');
                log('✅ Configuration error simulated', 'success');
                refreshStatus();
            } else {
                log('❌ Social auth handler not available', 'error');
            }
        }

        function testErrorHandling() {
            log('🧪 Testing error handling...', 'info');
            
            // Test various error scenarios
            simulateProviderError();
            setTimeout(() => simulateRedirectError(), 1000);
            setTimeout(() => simulateConfigError(), 2000);
            setTimeout(() => {
                log('✅ Error handling test complete', 'success');
                refreshStatus();
            }, 3000);
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                testResults,
                systemStatus: typeof socialAuthHandler !== 'undefined' ? socialAuthHandler.getAllProviderStatuses() : null
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `social-auth-test-results-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('✅ Test results exported', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Social Auth Test Page Loaded', 'success');
            
            // Initialize auth manager if available
            if (typeof authManager !== 'undefined') {
                authManager.init();
            }
            
            // Refresh status after a delay
            setTimeout(() => {
                refreshStatus();
            }, 2000);
        });
    </script>
</body>
</html>
