<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Fixes Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0284c7;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        #output {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 UI/UX Fixes Test Page</h1>
    
    <div class="test-section">
        <h2>🔍 Modal Display Tests</h2>
        <button onclick="testLoginModal()">Test Login Modal</button>
        <button onclick="testRegisterModal()">Test Register Modal</button>
        <button onclick="testModalAnimations()">Test Modal Animations</button>
        <button onclick="testModalClosing()">Test Modal Closing</button>
    </div>

    <div class="test-section">
        <h2>🔍 Search Button Tests</h2>
        <button onclick="testSearchButton()">Test Search Button</button>
        <button onclick="testSearchFunctionality()">Test Search Functionality</button>
        <button onclick="testSearchStyling()">Test Search Styling</button>
    </div>

    <div class="test-section">
        <h2>📱 Mobile Navigation Tests</h2>
        <button onclick="testMobileMenu()">Test Mobile Menu</button>
        <button onclick="testFloatingActionButton()">Test Floating Action Button</button>
        <button onclick="testMobileResponsiveness()">Test Mobile Responsiveness</button>
        <button onclick="testTouchInteractions()">Test Touch Interactions</button>
    </div>

    <div class="test-section">
        <h2>🧪 Complete UI Test</h2>
        <button onclick="runCompleteUITest()">Run Complete UI Test</button>
        <button onclick="testOnMobile()">Test Mobile Experience</button>
        <button onclick="testAuthentication()">Test Authentication Flow</button>
    </div>

    <div class="test-section">
        <h2>📊 Test Output</h2>
        <div id="output"></div>
        <button onclick="clearOutput()">Clear Output</button>
    </div>

    <!-- Include all necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/managers/AuthManager.js"></script>
    <script src="js/managers/ProductManager.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/auth-fix.js"></script>

    <script>
        // Test functions
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // Modal Tests
        function testLoginModal() {
            log('🔍 Testing Login Modal...', 'info');
            
            try {
                if (typeof authManager === 'undefined') {
                    log('❌ AuthManager not available', 'error');
                    return;
                }

                authManager.showLoginModal();
                
                setTimeout(() => {
                    const modal = document.getElementById('loginModal');
                    if (modal && !modal.classList.contains('hidden')) {
                        log('✅ Login modal displayed successfully', 'success');
                        
                        // Test modal content
                        const emailInput = modal.querySelector('input[type="email"]');
                        const passwordInput = modal.querySelector('input[type="password"]');
                        const submitBtn = modal.querySelector('button[type="submit"]');
                        
                        if (emailInput && passwordInput && submitBtn) {
                            log('✅ Modal form elements present', 'success');
                        } else {
                            log('❌ Modal form elements missing', 'error');
                        }
                        
                        // Close modal
                        setTimeout(() => {
                            authManager.hideModal('loginModal');
                            log('✅ Modal closed successfully', 'success');
                        }, 1000);
                        
                    } else {
                        log('❌ Login modal not displayed', 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ Login modal test error: ${error.message}`, 'error');
            }
        }

        function testRegisterModal() {
            log('🔍 Testing Register Modal...', 'info');
            
            try {
                if (typeof authManager === 'undefined') {
                    log('❌ AuthManager not available', 'error');
                    return;
                }

                authManager.showRegisterModal();
                
                setTimeout(() => {
                    const modal = document.getElementById('registerModal');
                    if (modal && !modal.classList.contains('hidden')) {
                        log('✅ Register modal displayed successfully', 'success');
                        
                        // Close modal
                        setTimeout(() => {
                            authManager.hideModal('registerModal');
                            log('✅ Modal closed successfully', 'success');
                        }, 1000);
                        
                    } else {
                        log('❌ Register modal not displayed', 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ Register modal test error: ${error.message}`, 'error');
            }
        }

        function testModalAnimations() {
            log('🎬 Testing Modal Animations...', 'info');
            
            try {
                authManager.showLoginModal();
                
                setTimeout(() => {
                    const modal = document.getElementById('loginModal');
                    const modalCard = modal?.querySelector('.modal-card');
                    
                    if (modalCard) {
                        const transform = window.getComputedStyle(modalCard).transform;
                        const opacity = window.getComputedStyle(modal).opacity;
                        
                        log(`Modal opacity: ${opacity}`, 'info');
                        log(`Modal transform: ${transform}`, 'info');
                        
                        if (opacity === '1') {
                            log('✅ Modal animation completed', 'success');
                        } else {
                            log('⚠️ Modal animation may be incomplete', 'warning');
                        }
                    }
                    
                    // Close modal
                    setTimeout(() => authManager.hideModal('loginModal'), 1000);
                    
                }, 500);

            } catch (error) {
                log(`❌ Animation test error: ${error.message}`, 'error');
            }
        }

        function testModalClosing() {
            log('🚪 Testing Modal Closing...', 'info');
            
            try {
                authManager.showLoginModal();
                
                setTimeout(() => {
                    const modal = document.getElementById('loginModal');
                    if (modal) {
                        // Test close button
                        const closeBtn = modal.querySelector('.modal-close');
                        if (closeBtn) {
                            closeBtn.click();
                            log('✅ Close button clicked', 'success');
                            
                            setTimeout(() => {
                                if (!document.getElementById('loginModal')) {
                                    log('✅ Modal removed from DOM', 'success');
                                } else {
                                    log('⚠️ Modal still in DOM', 'warning');
                                }
                            }, 500);
                        } else {
                            log('❌ Close button not found', 'error');
                        }
                    }
                }, 500);

            } catch (error) {
                log(`❌ Modal closing test error: ${error.message}`, 'error');
            }
        }

        // Search Tests
        function testSearchButton() {
            log('🔍 Testing Search Button...', 'info');
            
            try {
                // Check if search button exists
                const searchBtn = document.getElementById('searchBtn');
                if (searchBtn) {
                    log('✅ Search button found', 'success');
                    
                    // Test button styling
                    const styles = window.getComputedStyle(searchBtn);
                    log(`Button background: ${styles.backgroundColor}`, 'info');
                    log(`Button border-radius: ${styles.borderRadius}`, 'info');
                    
                    // Test click functionality
                    searchBtn.click();
                    log('✅ Search button clicked successfully', 'success');
                    
                } else {
                    log('❌ Search button not found', 'error');
                }

            } catch (error) {
                log(`❌ Search button test error: ${error.message}`, 'error');
            }
        }

        function testSearchFunctionality() {
            log('🔍 Testing Search Functionality...', 'info');
            
            try {
                if (typeof productManager === 'undefined') {
                    log('❌ ProductManager not available', 'error');
                    return;
                }

                // Test search function
                productManager.handleSearch('test');
                log('✅ Search function called successfully', 'success');
                
                // Test empty search
                productManager.handleSearch('');
                log('✅ Empty search handled', 'success');

            } catch (error) {
                log(`❌ Search functionality test error: ${error.message}`, 'error');
            }
        }

        function testSearchStyling() {
            log('🎨 Testing Search Styling...', 'info');
            
            try {
                const searchInput = document.querySelector('.search-input');
                const searchBtn = document.querySelector('.search-btn');
                
                if (searchInput && searchBtn) {
                    const inputStyles = window.getComputedStyle(searchInput);
                    const btnStyles = window.getComputedStyle(searchBtn);
                    
                    log(`Input border-radius: ${inputStyles.borderTopLeftRadius}`, 'info');
                    log(`Button border-radius: ${btnStyles.borderTopRightRadius}`, 'info');
                    log(`Button min-width: ${btnStyles.minWidth}`, 'info');
                    
                    log('✅ Search styling verified', 'success');
                } else {
                    log('❌ Search elements not found', 'error');
                }

            } catch (error) {
                log(`❌ Search styling test error: ${error.message}`, 'error');
            }
        }

        // Mobile Tests
        function testMobileMenu() {
            log('📱 Testing Mobile Menu...', 'info');
            
            try {
                if (typeof mobileMenu === 'undefined') {
                    log('❌ Mobile menu not available', 'error');
                    return;
                }

                // Test opening mobile menu
                mobileMenu.open();
                log('✅ Mobile menu opened', 'success');
                
                setTimeout(() => {
                    const overlay = document.getElementById('mobileMenuOverlay');
                    if (overlay && !overlay.classList.contains('hidden')) {
                        log('✅ Mobile menu overlay visible', 'success');
                        
                        // Test closing
                        mobileMenu.close();
                        log('✅ Mobile menu closed', 'success');
                    } else {
                        log('❌ Mobile menu overlay not visible', 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ Mobile menu test error: ${error.message}`, 'error');
            }
        }

        function testFloatingActionButton() {
            log('🎯 Testing Floating Action Button...', 'info');
            
            try {
                const fab = document.querySelector('.floating-action-btn');
                if (fab) {
                    log('✅ Floating action button found', 'success');
                    
                    const styles = window.getComputedStyle(fab);
                    log(`FAB position: ${styles.position}`, 'info');
                    log(`FAB z-index: ${styles.zIndex}`, 'info');
                    
                } else {
                    log('ℹ️ Floating action button not visible (desktop mode)', 'info');
                }

            } catch (error) {
                log(`❌ FAB test error: ${error.message}`, 'error');
            }
        }

        function testMobileResponsiveness() {
            log('📱 Testing Mobile Responsiveness...', 'info');
            
            try {
                const viewport = {
                    width: window.innerWidth,
                    height: window.innerHeight
                };
                
                log(`Viewport: ${viewport.width}x${viewport.height}`, 'info');
                
                if (viewport.width < 768) {
                    log('✅ Mobile viewport detected', 'success');
                    
                    // Test mobile-specific elements
                    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
                    const desktopAuth = document.querySelector('.hidden.md\\:flex');
                    
                    if (mobileMenuBtn) {
                        log('✅ Mobile menu button visible', 'success');
                    } else {
                        log('❌ Mobile menu button not found', 'error');
                    }
                    
                } else {
                    log('ℹ️ Desktop viewport - mobile features may be hidden', 'info');
                }

            } catch (error) {
                log(`❌ Responsiveness test error: ${error.message}`, 'error');
            }
        }

        function testTouchInteractions() {
            log('👆 Testing Touch Interactions...', 'info');
            
            try {
                const touchSupport = 'ontouchstart' in window;
                log(`Touch support: ${touchSupport ? 'Yes' : 'No'}`, 'info');
                
                // Test touch-friendly button sizes
                const buttons = document.querySelectorAll('button');
                let touchFriendlyCount = 0;
                
                buttons.forEach(btn => {
                    const styles = window.getComputedStyle(btn);
                    const height = parseInt(styles.height);
                    if (height >= 44) { // iOS recommended minimum
                        touchFriendlyCount++;
                    }
                });
                
                log(`Touch-friendly buttons: ${touchFriendlyCount}/${buttons.length}`, 'info');
                
                if (touchFriendlyCount > 0) {
                    log('✅ Touch interactions optimized', 'success');
                } else {
                    log('⚠️ Consider increasing button sizes for touch', 'warning');
                }

            } catch (error) {
                log(`❌ Touch interaction test error: ${error.message}`, 'error');
            }
        }

        // Complete Tests
        function runCompleteUITest() {
            log('🧪 Running Complete UI Test...', 'info');
            log('='.repeat(50), 'info');
            
            setTimeout(() => testLoginModal(), 500);
            setTimeout(() => testRegisterModal(), 2000);
            setTimeout(() => testSearchButton(), 3500);
            setTimeout(() => testMobileMenu(), 5000);
            setTimeout(() => testMobileResponsiveness(), 6500);
            setTimeout(() => {
                log('='.repeat(50), 'info');
                log('✅ Complete UI test finished', 'success');
            }, 8000);
        }

        function testOnMobile() {
            log('📱 Testing Mobile Experience...', 'info');
            
            // Simulate mobile viewport
            const meta = document.querySelector('meta[name="viewport"]');
            if (meta) {
                log('✅ Viewport meta tag present', 'success');
            } else {
                log('❌ Viewport meta tag missing', 'error');
            }
            
            testMobileResponsiveness();
            testTouchInteractions();
            testFloatingActionButton();
        }

        function testAuthentication() {
            log('🔐 Testing Authentication Flow...', 'info');
            
            try {
                // Test modal display
                testLoginModal();
                
                setTimeout(() => {
                    // Test with test account
                    if (typeof authManager !== 'undefined') {
                        log('Testing with admin account...', 'info');
                        window.quickLoginAdmin();
                    }
                }, 3000);

            } catch (error) {
                log(`❌ Authentication test error: ${error.message}`, 'error');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof authManager !== 'undefined') {
                authManager.init();
            }
            log('🚀 UI Test page loaded', 'success');
            log('💡 Use the buttons above to test UI components', 'info');
        });
    </script>
</body>
</html>
